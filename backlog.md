# Property Finder Crawler MVP: Implementation Backlog

This backlog breaks down the implementation of the Property Finder crawler into small, testable, and sequential tasks. Each task should be completed and tested before moving to the next.

---

## Phase 1: Foundational Setup

- [x] **Task 0.5: Fix SOURCE_URN Constant**
    - In `PropertyFinderCommon`, update `SOURCE_URN` from `"urn:source:propertyfinder"` to `"urn:source:propertyfinder:ae"` to match spec §5 requirements.

- [x] **Task 1: Verify Package Structure** 
    - Verify the Java package `com.realmond.temporal_service.crawler.uae.property_finder` exists.
    - Verify the sub-package `model` exists with existing POJOs.

- [x] **Task 2: Create Missing Core Components**
    - Create only the missing classes (most already exist):
        - `public class PropertyFinderProjectCrawler implements Crawler<ProjectModel>`
        - `public class PropertyFinderApiService`
    - Note: `PropertyFinderSettings` already exists.

- [x] **Task 3: Configure Spring Components**
    - Annotate `PropertyFinderProjectCrawler` with `@Component` and `@RequiredArgsConstructor`.
    - Annotate `PropertyFinderApiService` with `@Component`, `@Slf4j`, and `@RequiredArgsConstructor`.
    - Note: `PropertyFinderSettings` already has `@ConfigurationProperties("crawler.uae.property-finder")` and `@Data`.

- [x] **Task 3.5: Verify Settings Configuration**
    - Verify `PropertyFinderSettings` extends `DefaultCrawlerSettings` (inherits `maxPages`, `pageSize`, `retry`, `rateLimiter`).
    - Confirm `baseUrl` field exists with default `"https://www.propertyfinder.ae"`.
    - No additional fields needed as inheritance covers requirements.

---

## Phase 2: Crawler & API Service Core Logic

- [x] **Task 4: Implement Crawler Shell Methods**
    - In `PropertyFinderProjectCrawler`:
        - Implement `getSourceUrn()` to return `"urn:source:propertyfinder:ae"`.
        - Implement `supportsPagination()` to return `true`.
        - Implement `fetchAll()` to throw `NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED`.
        - Inject `PropertyFinderApiService` via the constructor.

- [x] **Task 5: Implement Basic Page Processing Structure**
    - In `PropertyFinderApiService`:
        - Inject the existing `PropertyFinderFeignRestClient`.
        - Create the `public List<CrawlRecord<ProjectModel>> fetchProjectsPage(int page)` method stub.
        - Call `client.getNewProjects(page)` and unwrap to get `List<ProjectSummary>` via `.getProps().getPageProps().getSearchResult().getData().getProjects().getDeveloper()`.
        - **Handle 404 gracefully**: Catch `FeignException.NotFound` and return empty list (this indicates end of pagination).
        - For other exceptions, wrap in `RetryableCrawlerException`.
        - For now, log each summary's `developerSlug` and `projectSlug`. Return empty list.

- [x] **Task 5.5: Implement Pagination Discovery Logic (Optimised)**
    - In `PropertyFinderProjectCrawler`:
        - Implement `fetchAllPageIds()` by first fetching page 1 (using either `feignClient.getNewProjects(1)` directly or a helper in `PropertyFinderApiService`).
        - Extract `pagination.total` from the response path `props.pageProps.searchResult.meta.pagination.total`.
        - If `total` is present, build the list `["1", "2", …, String.valueOf(total)]` without making further HTTP calls.
        - If `total` is `null` or missing, assume a single-page catalogue and return an empty list.
        - Handle 404 on page 1 by returning an empty list (no projects), and wrap other errors in `RetryableCrawlerException`.

- [x] **Task 6: Create HTML Stripping Utility**
    - In `PropertyFinderApiService`, create private method `String stripHtml(String html)`.
    - Use Jsoup or regex to remove HTML tags, return plain text.
    - Handle null input gracefully.

- [X] **Task 7: Implement Page Processing Delegation**
    - In `PropertyFinderProjectCrawler`:
        - Implement `parsePage(String pageNum)` to parse the page number and delegate the call to `apiService.fetchProjectsPage()`, as detailed in `spec.md §2.4`.

---

## Phase 3: Data Mapping (Incremental)

*For this phase, create a dedicated unit test class `PropertyFinderApiServiceTest`. Each task requires adding mapping logic to a `convertToProjectModel` method in the `PropertyFinderApiService` and adding a corresponding unit test to verify the new fields.*

- [x] **Task 8: Create `convertToProjectModel` Method Stub**
    - In `PropertyFinderApiService`, create a `private Optional<ProjectModel> convertToProjectModel(ProjectDetails detail)` method.
    - It should initialize a `ProjectModel.builder()` and return `Optional.of(builder.build())`.
    - Create the `PropertyFinderApiServiceTest` class with a setup method that loads sample `ProjectDetails` from `src/test/resources/crawler/uae/propertyfinder/project-details.json`.

- [x] **Task 8.5: Extend Test Fixtures**
    - In `PropertyFinderApiServiceTest` setup, also load the second fixture `project-details-2.json`.
    - Ensure both payloads are tested in all relevant unit tests to cover edge-cases (e.g. missing `locationTree`).

- [x] **Task 9: Map Identifiers & URNs**
    - In `convertToProjectModel`, map the following fields as per `spec.md §5`:
        - `sourceUrn` = `"urn:source:propertyfinder:ae"`
        - Extract `developerSlug` from `detailResult.developer.slug.toLowerCase()`
        - Extract `projectSlug` as substring after `developerSlug/` in `detailResult.slug`
        - `developerUrn` = `${sourceUrn}:developer:${developerSlug}`
        - `projectUrn` = `${developerUrn}:project:${projectSlug}`
        - `externalId` = `detailResult.id` (string)
    - Add a unit test to verify these fields are correctly mapped and URN format matches specification.

- [x] **Task 10: Map Basic Attributes**
    - In `convertToProjectModel`, map the following fields as per `spec.md §6`:
        - `title` = `detailResult.title` (required)
        - `description` = `stripHtml(detailResult.description)` – remove HTML tags
        - `currency` = `"AED"` (hardcoded)
        - `projectSlug` (already mapped in Task 9)
    - Add a unit test to verify these fields.

- [x] **Task 11: Map Location & Geo**
    - In `convertToProjectModel`, map the `LocationModel` object as per `spec.md §7`:
        - `location.address` = `location.fullName`
        - `location.city` = `locationTree.length > 0 ? locationTree[0].name : null`
        - `location.cityDistrict` = `locationTree.length > 1 ? locationTree[1].name : null`
        - `location.state` = `location.city`
        - `location.country` = `"AE"`
    - Map `coordinates`: `lat` = `location.coordinates.lat`, `lng` = `location.coordinates.lon || .lng`
    - Set `polygon` = `null`
    - Add a unit test to verify the location fields, including null-safety when `locationTree` is null or empty.

- [x] **Task 12: Map Media**
    - In `convertToProjectModel`, map `images`, `coverImage`, and `videos` as per `spec.md §8`:
        - Iterate `images[*]` where `type == "image"` → `ProjectModel.images[]` (first becomes `coverImage`)
        - Iterate `images[*]` where `type == "video"` → `ProjectModel.videos[]`
        - Find `images[*]` where `type == "master-plan"` → store URI in `additionalData.masterPlanImage`
    - Map `stripHtml(masterPlan.description)` → `additionalData.masterPlanDescription`.
    - Add a unit test to verify media mapping.

- [x] **Task 12.5: Map Amenities**
    - In `convertToProjectModel`, map `amenities[*].name` to `AmenityModel.label` as per `spec.md §9`.
    - Add a unit test to verify amenity mapping with multiple amenities.

- [x] **Task 13: Map Construction Status**
    - In `convertToProjectModel`, map `constructionPhase` to `projectStatus` as per the rules in `spec.md §10`:
        - `"not_started", "off_plan"` → `NOT_STARTED`
        - `"under_construction"` → `ACTIVE`
        - `"completed", "ready"` → `FINISHED`
        - `null/unknown` → `null`
    - Add a unit test to verify the status enum mapping.

- [x] **Task 14: Map ProjectStats (Scalar Fields)**
    - In `convertToProjectModel`, create a `ProjectStats.builder()`.
    - Map scalar fields as per `spec.md §11`:
        - `priceMin` = `startingPrice` (AED)
        - `launchDate` = `salesStartDate` (ISO → yyyy-MM-dd)
        - `completionDate` = `deliveryDate` (ISO → yyyy-MM-dd)
    - Add a unit test to verify these stats fields.

- [x] **Task 14.5: Implement `parseIsoDate` Helper**
    - In `PropertyFinderApiService`, create private method `LocalDate parseIsoDate(String iso)`.
    - Use `DateTimeFormatter.ISO_DATE_TIME` parsing; return `null` when input is null or unparseable.
    - Add unit tests for valid and invalid inputs.

- [x] **Task 15: Map ProjectStats (Property Types)**
    - In `convertToProjectModel`, map the `propertyTypes` list as per `spec.md §11`:
        - Convert `propertyTypes[*]` → enum `AdModel.PropertyTypeModel`
        - Apply upper-case conversion, '-' → '_' replacement
        - Use `RESIDENTIAL_FALLBACK` as fallback for unmapped types
    - Add a unit test for property type mapping with various input types.

- [x] **Task 16: Map ProjectStats (UnitStats)**
    - In `convertToProjectModel`, iterate and map the nested `units` list into a list of `UnitStats` objects, as per `spec.md §11`:
        - Iterate over `units[*].units[*].list[*]`
        - `propertyType` = parent UnitSet mapping
        - `bedrooms` = (double) bedrooms – Integer to double conversion
        - `priceMin` = startingPrice (AED)
        - `areaMinSqm` = `UnitConversionUtils.sqftToSqm(areaFrom, 2)` – 2 decimal places
        - `areaMaxSqm` = `UnitConversionUtils.sqftToSqm(areaTo, 2)` if areaTo > 0, else null
        - `areaRangeSqm` = areaMinSqm (deprecated field for backward compatibility)
    - Add a unit test to verify the `UnitStats` list.

- [x] **Task 17: Create SHA-1 Hashing Utility**
    - In `PropertyFinderApiService`, create private method `String generateSha1Hash(String input)`.
    - Use `MessageDigest.getInstance("SHA-1")` to compute hash.
    - Return hex-lowercase string representation.

- [x] **Task 17.5: Map Floor Plans**
    - In `convertToProjectModel`, map `layouts` to `floorPlans` as per `spec.md §12`:
        - For each `layouts[*]`, iterate `floorPlans[*]` URLs
        - Build `FloorPlanModel` with:
            - `externalId` = **SHA-1(URL)** (hex-lower)
            - `floorplanUrn` = `${sourceUrn}:floorplan:${externalId}`
            - `projectUrn`, `sourceUrn` as usual
            - `propertyType` = parent UnitSet mapping
            - `bedrooms` = layouts[*].bedrooms
            - `title` = layouts[*].layoutType
            - `image` = first URL → ImageModel
        - Deduplicate by URL
    - Add a unit test for floor plan mapping.

- [x] **Task 18: Map Brochures & Payment Plans**
    - In `convertToProjectModel`, map `brochureUrl` and `paymentPlans` as per `spec.md §13` and `§14`:
        - If `brochureUrl` present → create `BrochureModel` → `ProjectModel.brochures[]`
        - For each `paymentPlans[*]`: create `PaymentPlanModel` with `title`
        - For each `phases[*]`: create `PaymentPhaseModel` with `label`, `value`
        - For each `phases[*].miles[*]`: create `PaymentMileModel` with `label`, `value`
    - Add a unit test for these fields.

- [x] **Task 19: Map Additional Data**
    - In `convertToProjectModel`, map all remaining fields into the `additionalData` map as specified in `spec.md §15`:
        - `developerId` = `developer.id`
        - `masterPlanDescription` = raw string
        - `masterPlanImage` = raw string
        - `faqs` = list of `{question: faqs[*].question, answer: stripHtml(faqs[*].answer)}`
        - `ownershipType` = enum FREEHOLD / LEASEHOLD / UNKNOWN (normalize from raw string)
        - `salesPhase` = raw string
        - `lastInspectionDate` = `parseIsoDate(lastInspectionDate) ?? null` (yyyy-MM-dd format)
        - `timelinePhases` = raw list
    - Add a unit test for the `additionalData` fields.

- [x] **Task 19.5: Map `additionalData.ADDITIONAL_SALES_PHASE` Enum**
    - Extend Task 19 conversion logic: when `salesPhase` is non-null, also write its upper-snake value to `additionalData.ADDITIONAL_SALES_PHASE`.
    - Add unit test verifying correct enum derivation (e.g., `booking_started` → `BOOKING_STARTED`).

- [x] **Task 20: Map Project URLs**
    - In `convertToProjectModel`, build and set the canonical project URL in the `urls` list as per `spec.md §16`:
        - `ProjectModel.urls[0]` = `https://www.propertyfinder.ae/en/new-projects/{developerSlug}/{projectSlug}`
    - Add a unit test to verify the URL format.

---

## Phase 4: Integration & Finalization

- [x] **Task 21: Integrate Project Details Fetching**
    - In `PropertyFinderApiService.fetchProjectsPage()`:
        - For each `ProjectSummary`, extract `developerSlug` and `projectSlug` from the summary.
        - Call `client.getProjectDetails(developerSlug, projectSlug)` and unwrap to get `ProjectDetails` via `.getProps().getPageProps().getDetailResult()`.
        - Handle individual project fetch failures gracefully (log warning, continue processing).

- [x] **Task 22: Integrate Conversion with Error Handling**
    - In `PropertyFinderApiService.fetchProjectsPage()`:
        - Call your completed `convertToProjectModel(detail)` method.
        - Log warnings for conversion failures and continue processing.
        - Only add successfully converted projects to result list.

- [x] **Task 23: Implement CrawlRecord Wrapping**
    - In `PropertyFinderApiService`:
        - Implement the `wrapInCrawlRecord` and `createMetadata` private helper methods as specified in `spec.md §3.4`.
        - In `fetchProjectsPage`, use these helpers to wrap the `ProjectModel` in a `CrawlRecord` and add it to the result list.

- [x] **Task 24: Add Comprehensive Error Logging**
    - Review all code against the spec, especially error handling and logging patterns from `spec.md §17`:
        - Add all required `log.info`, `log.warn`, and `log.error` statements with correct formatting
        - Ensure proper exception propagation following ARO patterns
        - Add structured logging with project identifiers

- [x] **Task 25: Add Javadoc Documentation**
    - Add Javadoc comments to all public methods in `PropertyFinderProjectCrawler` and `PropertyFinderApiService`.
    - Follow existing patterns from ARO implementation.

- [ ] **Task 26: Create Integration Test**
    - Create a `PropertyFinderProjectCrawlerTest` class.
    - Write an integration test that mocks the `PropertyFinderFeignRestClient` to test the full `parsePage` flow.
    - Mock both `getNewProjects()` and `getProjectDetails()` with sample JSON responses from existing test fixtures.
    - Verify that a 404 from the client is handled gracefully during pagination discovery.
    - Test that conversion produces valid `ProjectModel` objects with correct URNs and metadata.