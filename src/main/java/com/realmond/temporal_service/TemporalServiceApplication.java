package com.realmond.temporal_service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@EnableFeignClients
@EnableConfigurationProperties
@ComponentScan(excludeFilters = {
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*FeignConfiguration")
})
public class TemporalServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(TemporalServiceApplication.class, args);
	}

}
