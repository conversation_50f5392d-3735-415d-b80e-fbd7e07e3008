package com.realmond.temporal_service.client.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * Feign client for PropertyFinder website.
 * This interface defines the API endpoints for fetching data from PropertyFinder.
 */
@FeignClient(name = "propertyFinder", url = "${propertyfinder.base-url:https://www.propertyfinder.ae}", configuration = com.realmond.temporal_service.crawler.uae.property_finder.PropertyFinderFeignConfiguration.class)
public interface PropertyFinderFeignClient {

    /**
     * Fetches the projects list page.
     *
     * @param page The page number
     * @param headers The HTTP headers to include in the request
     * @return The HTML content of the page
     */
    @GetMapping("/en/new-projects")
    String getProjectsList(
            @RequestParam("page") int page,
            @RequestHeader Map<String, String> headers
    );

    /**
     * Fetches a project detail page.
     *
     * @param path The path part of the URL (without the domain)
     * @param headers The HTTP headers to include in the request
     * @return The HTML content of the page
     */
    @GetMapping("{path}")
    String getProjectDetail(
            @PathVariable("path") String path,
            @RequestHeader Map<String, String> headers
    );
}
