package com.realmond.temporal_service.config;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;

/**
 * Global configuration enabling Spring's cache abstraction. This lightweight
 * class activates caching across the application so that {@code @Cacheable},
 * {@code @CacheEvict}, etc. annotations take effect. It does not declare any
 * beans itself: concrete cache managers are provided in module-specific
 * configurations such as {@link com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictFeignConfiguration}.
 */
@Configuration
@EnableCaching
public class CacheConfig {
    // Intentionally empty – presence of the annotation is sufficient.
} 