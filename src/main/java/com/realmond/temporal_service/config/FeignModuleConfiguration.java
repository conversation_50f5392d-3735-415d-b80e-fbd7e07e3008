package com.realmond.temporal_service.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Marker annotation for Feign configuration classes that should be excluded
 * from global component scanning to prevent bean conflicts.
 * 
 * These configurations are intended to be used only as Feign client-specific
 * configurations via the @FeignClient(configuration = ...) attribute.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface FeignModuleConfiguration {
}
