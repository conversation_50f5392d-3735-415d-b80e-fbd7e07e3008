package com.realmond.temporal_service.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Retryer;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Getter
public class StrapiClientConfig implements RequestInterceptor {

    @Value("${strapi.api.url}")
    private String url;

    @Value("${strapi.api.token}")
    private String apiToken;

    @Bean
    public Retryer neverRetry() {
        return Retryer.NEVER_RETRY;
    }

    @Override
    public void apply(RequestTemplate template) {
        if (apiToken != null && !apiToken.isEmpty()) {
            template.header("Authorization", "Bearer " + apiToken);
        }
    }
}
