package com.realmond.temporal_service.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Strapi-related beans.
 * This class provides global Spring beans that are needed by other components.
 */
@Configuration
public class StrapiConfig {

    /**
     * Provides StrapiClientConfig as a Spring bean for dependency injection.
     */
    @Bean
    public StrapiClientConfig strapiClientConfig() {
        return new StrapiClientConfig();
    }
}
