package com.realmond.temporal_service.crawler.common;

import org.jsoup.Jsoup;

/**
 * Common string processing utilities for crawler services.
 */
public final class StringUtils {

    private StringUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Strips HTML tags from a string and decodes HTML entities.
     * Returns null if the input is null or blank.
     *
     * @param html The HTML string to clean
     * @return The plain text content, or null if input was null/blank
     */
    public static String stripHtml(String html) {
        if (html == null || html.isBlank()) {
            return null;
        }
        // Use Jsoup to strip out HTML tags and decode HTML entities
        return Jsoup.parse(html).text();
    }
} 