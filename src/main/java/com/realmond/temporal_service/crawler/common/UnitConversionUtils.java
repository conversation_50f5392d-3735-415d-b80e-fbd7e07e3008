package com.realmond.temporal_service.crawler.common;

import java.math.BigDecimal;
import java.math.RoundingMode;

public final class UnitConversionUtils {

    private static final double SQFT_TO_SQM_FACTOR = 0.092903;

    private UnitConversionUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Converts square feet to square meters and rounds the result to the specified scale.
     *
     * @param sqft The area in square feet.
     * @param scale The number of decimal places to round to.
     * @return The area in square meters, rounded.
     */
    public static double sqftToSqm(double sqft, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("Scale must be a non-negative integer.");
        }
        BigDecimal sqftDecimal = BigDecimal.valueOf(sqft);
        BigDecimal factorDecimal = BigDecimal.valueOf(SQFT_TO_SQM_FACTOR);
        BigDecimal result = sqftDecimal.multiply(factorDecimal);
        return result.setScale(scale, RoundingMode.HALF_UP).doubleValue();
    }
} 