package com.realmond.temporal_service.crawler.rate_limit;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Configuration properties for Bucket4j-based rate limiter.
 * <p>
 * <p>Default behaviour:</p>
 * <ul>
 *     <li>10 requests per second (burst size&nbsp;=&nbsp;10, refill&nbsp;=&nbsp;10 tokens every&nbsp;1&nbsp;s).</li>
 *     <li>Operates <em>in-memory</em> by default; set {@code useRedis=true} to enable a
 *     distributed bucket (uses Lettuce Redis client already present in the project).</li>
 *     <li>Redis connection parameters fall back to {@code localhost:6379} DB&nbsp;0 with no password.</li>
 * </ul>
 * <p>The defaults ensure that, without extra configuration, every micro-service
 * instance shares a simple local rate-limit suitable for development or a
 * single-node deployment.</p>
 *
 * Enabling {@code useRedis = true} switches the limiter to the distributed mode
 * backed by Redis so multiple crawler nodes will all respect the same global
 * quota.  Connection parameters are taken from the fields below so that the
 * limiter does not have any additional external configuration dependencies.
 */
@Data
@NoArgsConstructor
public class RateLimiterSettings {

    /** Maximum number of tokens that can be stored in the bucket (burst size). */
    private long capacity = 10;

    /** Number of tokens that are added to the bucket every refill period. */
    private long refillTokens = 10;

    /** Refill period in seconds. */
    private long refillPeriodSeconds = 1;

    /** If {@code true} the limiter state is stored in Redis – useful when the crawler runs on several nodes. */
    private boolean useRedis = false;

    // ───────────────────────────────── Redis connection ─────────────────────────

    private String redisHost = "localhost";
    private int redisPort = 6379;
    private String redisPassword;
    private int redisDatabase = 0;
}
