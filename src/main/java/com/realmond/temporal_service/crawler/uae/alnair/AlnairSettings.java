package com.realmond.temporal_service.crawler.uae.alnair;

import com.realmond.temporal_service.crawler.DefaultCrawlerSettings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for the Alnair crawler (covers both api.alnair.ae and alnair.ae front-end).
 * <p>
 * Values can be overridden in <code>application.yaml</code> using the prefix <pre>crawler.uae.alnair</pre>.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Component
@ConfigurationProperties(prefix = "crawler.uae.alnair")
public class AlnairSettings extends DefaultCrawlerSettings {

    /** Base URL of the public Alnair REST API */
    private String apiBaseUrl = "https://api.alnair.ae";

    /** Base URL of the public Alnair website that serves packed JSON *.data payloads */
    private String siteBaseUrl = "https://alnair.ae";
} 