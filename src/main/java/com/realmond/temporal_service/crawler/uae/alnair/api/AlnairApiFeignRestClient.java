package com.realmond.temporal_service.crawler.uae.alnair.api;

import com.realmond.temporal_service.crawler.uae.alnair.model.DeveloperDetails;
import com.realmond.temporal_service.crawler.uae.alnair.model.DevelopersResponse;
import com.realmond.temporal_service.crawler.uae.alnair.model.LayoutDetails;
import com.realmond.temporal_service.crawler.uae.alnair.model.LayoutUnitsResponse;
import com.realmond.temporal_service.crawler.uae.alnair.model.ProjectsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Declarative Feign client for the public https://api.alnair.ae REST API.
 */
@FeignClient(
        name = "alnair-api",
        url = "${crawler.uae.alnair.api-base-url:https://api.alnair.ae}",
        configuration = AlnairApiFeignConfiguration.class)
public interface AlnairApiFeignRestClient {

    /**
     * Lists projects with pagination.
     */
    @GetMapping("/project/find")
    ProjectsResponse findProjects(
            @RequestParam("limit") int limit,
            @RequestParam("page") int page,
            @RequestHeader("X-CITY") String cityId);

    /**
     * Fetches details of a specific layout.
     */
    @GetMapping("/project/layout/{layoutId}")
    LayoutDetails getLayoutDetails(@PathVariable("layoutId") Integer layoutId,
                                   @RequestHeader("X-CITY") String cityId);

    /**
     * Retrieves grouped units for a project.
     */
    @GetMapping("/project/{projectId}/layouts/units")
    LayoutUnitsResponse getLayoutUnits(@PathVariable("projectId") Integer projectId,
                                       @RequestHeader("X-CITY") String cityId);

    /**
     * Fetches developer profile.
     */
    @GetMapping("/builders/{builderId}/view")
    DeveloperDetails getDeveloperDetails(@PathVariable("builderId") Integer builderId,
                                         @RequestHeader("X-CITY") String cityId);

    /**
     * Retrieves paginated developers collection.
     */
    @GetMapping("/builders/collection")
    DevelopersResponse getDevelopers(
            @RequestParam("page") int page,
            @RequestParam("limit") int limit,
            @RequestHeader("X-CITY") String cityId);
} 