package com.realmond.temporal_service.crawler.uae.alnair.decoder;

// Needed imports
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * Java port of <PERSON><PERSON>r's <em>Packed-JSON</em> decoder (skeleton).
 * <p>
 * The implementation will be completed in follow-up tasks. For now this class
 * only stores the packed JSON array and exposes a {@link #decode(int)} stub
 * so that the codebase compiles successfully.
 */
@Slf4j
@RequiredArgsConstructor
public final class PackedJsonDecoder {

    /**
     * The packed JSON array as produced by <PERSON><PERSON><PERSON>'s encoder. Each element can
     * be a primitive, list, map or reference key (e.g. "_42").
     */
    private final List<Object> packed;

    /**
     * Decode the packed JSON structure starting from {@code startIndex}.
     *
     * @param startIndex index of the element that should become the root of
     *                   the resulting structure (usually {@code 0}).
     * @return fully decoded Java object graph.
     * @throws IndexOutOfBoundsException if {@code startIndex} is outside the
     *                                   bounds of the packed array.
     */
    public Object decode(int startIndex) {
        Objects.checkIndex(startIndex, packed.size());
        return decodeValueAt(startIndex);
    }

    /**
     * Convenience method equivalent to {@link #decode(int) decode(0)}.
     */
    public Object decode() {
        return decode(0);
    }

    /**
     * Convenience wrapper returning the decoded value as a {@link java.util.Map}.
     * Most Alnair payloads – including the dictionary file – are objects at
     * the top level, so this helps avoid unchecked casts in caller code.
     *
     * @throws IllegalStateException if the decoded element is not a Map.
     */
    @SuppressWarnings("unchecked")
    public java.util.Map<String, Object> decodeToMap() {
        return decodeToMap(0);
    }

    /**
     * Same as {@link #decodeToMap()} but starting from an arbitrary index.
     */
    @SuppressWarnings("unchecked")
    public java.util.Map<String, Object> decodeToMap(int startIndex) {
        Object decoded = decode(startIndex);
        if (decoded instanceof java.util.Map<?, ?> map) {
            return (java.util.Map<String, Object>) map;
        }
        throw new IllegalStateException("Decoded element at index " + startIndex + " is not a Map but " + decoded.getClass());
    }

    // ---------------------------------------------------------------------
    // Internal helpers
    // ---------------------------------------------------------------------

    private Object decodeValueAt(int index) {
        if (index < 0 || index >= packed.size()) {
            return null;
        }

        Object raw = packed.get(index);

        // Handle sentinel integers early
        if (raw instanceof Integer i) {
            if (i == -5) {
                return null; // -5 -> null sentinel
            }
            if (i < 0) {
                log.error("Unknown sentinel value {} at pos {}", i, index);
                throw new IllegalStateException("Unknown sentinel value " + i);
            }
        }

        return decodeElement(raw);
    }

    @SuppressWarnings("unchecked")
    private Object decodeElement(Object element) {
        if (element instanceof java.util.Map<?, ?> map) {
            // Check for reference-encoded objects first
            if (containsReferenceKeys(map)) {
                return decodeReferenceObject((java.util.Map<String, Object>) map);
            }
            // Regular map – decode each value if it is an int index
            java.util.Map<String, Object> result = new java.util.LinkedHashMap<>();
            for (java.util.Map.Entry<?, ?> e : map.entrySet()) {
                String key = e.getKey().toString();
                Object value = e.getValue();
                result.put(key, decodeAny(value));
            }
            return result;
        } else if (element instanceof java.util.List<?> list) {
            java.util.List<Object> result = new java.util.ArrayList<>(list.size());
            for (Object item : list) {
                result.add(decodeAny(item));
            }
            return result;
        }

        // Primitive / non-container types are returned verbatim
        return element;
    }

    private Object maybeDecodeValue(Object candidate) {
        if (candidate instanceof Integer idx && idx >= 0) {
            return decodeValueAt(idx);
        }
        return candidate;
    }

    private Object decodeAny(Object candidate) {
        if (candidate instanceof Integer i) {
            if (i == -5) {
                return null;
            }
            if (i < 0) {
                log.error("Unknown sentinel value {} in value", i);
                throw new IllegalStateException("Unknown sentinel value " + i);
            }
            return decodeValueAt(i);
        } else if (candidate instanceof java.util.Map<?, ?> || candidate instanceof java.util.List<?>) {
            return decodeElement(candidate);
        } else {
            return candidate;
        }
    }

    private boolean containsReferenceKeys(java.util.Map<?, ?> map) {
        for (Object keyObj : map.keySet()) {
            if (keyObj instanceof String key && isReferenceKey(key)) {
                return true;
            }
        }
        return false;
    }

    private boolean isReferenceKey(String key) {
        // Must start with '_' followed by at least one digit
        if (key.length() < 2 || key.charAt(0) != '_') {
            return false;
        }
        for (int i = 1; i < key.length(); i++) {
            if (!Character.isDigit(key.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * Decodes an object where every entry uses the <code>"_n": m</code>
     * pattern. The numeric part of the key (<code>n</code>) references the
     * index holding the <em>property name</em>, while the value (<code>m</code>)
     * references the index holding the <em>property value</em>.
     * <p>
     * Example:
     * <pre>
     * [
     *   "pages",               // index 0 –&gt; property name
     *   "project",              // index 1 –&gt; property name
     *   {"_0": 3, "_1": 4},  // index 2 –&gt; reference object
     *   "/data",                // index 3 –&gt; property value
     *   42                       // index 4 –&gt; property value
     * ]
     * </pre>
     */
    private java.util.Map<String, Object> decodeReferenceObject(java.util.Map<String, Object> referenceObj) {
        java.util.Map<String, Object> result = new java.util.LinkedHashMap<>();

        for (java.util.Map.Entry<String, Object> entry : referenceObj.entrySet()) {
            String referenceKey = entry.getKey();
            Object valueRef = entry.getValue();

            if (!isReferenceKey(referenceKey)) {
                // Fall back to normal decoding behaviour
                result.put(referenceKey, maybeDecodeValue(valueRef));
                continue;
            }

            int propertyNameIndex;
            try {
                propertyNameIndex = Integer.parseInt(referenceKey.substring(1));
            } catch (NumberFormatException ex) {
                // Should not happen due to earlier validation, log & skip
                log.error("Invalid reference key '{}'", referenceKey, ex);
                continue;
            }

            if (propertyNameIndex < 0 || propertyNameIndex >= packed.size()) {
                log.error("Property name index {} out of bounds (packed size {})", propertyNameIndex, packed.size());
                continue;
            }

            Object rawPropertyName = packed.get(propertyNameIndex);
            if (!(rawPropertyName instanceof String propertyName)) {
                log.error("Referenced property name at index {} is not a String: {}", propertyNameIndex, rawPropertyName);
                continue;
            }

            Object decodedValue = decodeAny(valueRef);
            result.put(propertyName, decodedValue);
        }

        return result;
    }
} 