package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.realmond.temporal_service.config.FeignModuleConfiguration;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import feign.RequestInterceptor;
import feign.Retryer;
import feign.codec.Decoder;

/**
 * Feign configuration skeleton for the Alnair dictionary client.
 * <p>
 * Phase&nbsp;4 – Task&nbsp;8 only requires a module-local {@link ObjectMapper}
 * bean that will later be reused by the custom HTML decoder and the Feign
 * infrastructure. The bean is <strong>not</strong> marked {@code @Primary}
 * so that it does not override the application-wide mapper.
 */
@Configuration
@FeignModuleConfiguration
public class AlnairDictFeignConfiguration {

    /**
     * Provides an {@link ObjectMapper} scoped to the Alnair dictionary crawler
     * module. Additional configuration (modules, features) can be applied in
     * follow-up backlog tasks.
     */
    @Bean
    public ObjectMapper alnairDictObjectMapper() {
        return new ObjectMapper();
    }

    /**
     * Cache manager holding decoded <em>Alnair</em> catalogs in-memory. The
     * cache characteristics (TTL &amp; maximum size) are driven by
     * {@link AlnairDictSettings} so that they can be configured externally
     * via <code>application.yaml</code> or environment variables.
     */
    @Bean
    public CacheManager alnairDictCacheManager(AlnairDictSettings settings) {
        CaffeineCacheManager manager = new CaffeineCacheManager("alnairDictionary");
        manager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(settings.getCacheTtl())
                .maximumSize(settings.getCacheMaxSize()));
        return manager;
    }

    /**
     * Applies realistic browser headers to every outgoing request using the
     * shared {@link FingerprintGenerator} service.
     */
    @Bean
    public RequestInterceptor alnairDictRequestInterceptor(FingerprintGenerator fingerprintGenerator) {
        return template -> fingerprintGenerator.applyBrowserHeaders(template);
    }

    /**
     * Configures the Feign retry policy based on externally provided
     * {@link AlnairDictSettings#getRetry()} parameters.
     */
    @Bean
    public Retryer alnairDictRetryer(AlnairDictSettings settings) {
        return new Retryer.Default(
                settings.getRetry().getRetryDelayMs(),
                settings.getRetry().getMaxPeriodMs(),
                settings.getRetry().getMaxRetries());
    }

    /**
     * Registers the custom HTML decoder that extracts and converts the packed
     * JSON dictionary embedded in Alnair's <code>/app</code> page.
     */
    @Bean
    public Decoder alnairDictDecoder(ObjectMapper alnairDictObjectMapper) {
        return new AlnairDictDecoder(alnairDictObjectMapper);
    }
}
