package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.realmond.temporal_service.crawler.uae.alnair.model.Dictionary;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Declarative Feign client that fetches the packed dictionary HTML from the
 * public Alnair website. The custom decoder defined in
 * {@link AlnairDictFeignConfiguration} converts the response directly to a
 * strongly typed {@link Dictionary} object, so the interface exposes that type
 * as its sole endpoint return value.
 */
@FeignClient(
        name = "alnair-web",
        url = "${crawler.uae.alnair.dict.base-url}",
        configuration = AlnairDictFeignConfiguration.class)
public interface AlnairDictFeignRestClient {

    /**
     * Retrieves the latest catalogs dictionary from Alnair. The request path is
     * configurable via <code>crawler.uae.alnair.dict.dictionary-path</code> and
     * defaults to "/app" (see {@link AlnairDictSettings}).
     *
     * @return decoded {@link Dictionary} structure
     */
    @GetMapping("${crawler.uae.alnair.dict.dictionary-path}")
    Dictionary getDictionary();
} 
