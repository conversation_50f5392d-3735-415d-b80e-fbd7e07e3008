package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.realmond.temporal_service.crawler.RequestRetrySettings;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.Duration;

/**
 * Configuration properties for Alnair dictionary crawler module.
 * <p>
 * Values may be overridden via Spring's external configuration mechanism using
 * the prefix {@code crawler.uae.alnair.dict}. If not provided, sane defaults
 * matching the public production site are applied as defined in the module
 * specification (spec §4).
 */
@Data
@Component
@ConfigurationProperties(prefix = "crawler.uae.alnair.dict")
public class AlnairDictSettings {

    /** Public root of the Alnair website. */
    private URL baseUrl;

    /** Relative path whose HTML carries the packed dictionary. */
    private String dictionaryPath = "/app.data";

    /** TTL of the in-memory cache holding decoded catalogs. */
    private Duration cacheTtl = Duration.ofDays(1);

    /** Maximum number of Catalogs instances retained in the Caffeine cache. */
    private long cacheMaxSize = 100;

    /** HTTP retry/back-off policy. */
    private RequestRetrySettings retry = new RequestRetrySettings();

    public AlnairDictSettings() {
        try {
            this.baseUrl = new URL("https://alnair.ae");
        } catch (MalformedURLException e) {
            // Should never happen – constant string.
            throw new IllegalStateException("Invalid default base URL", e);
        }
    }
}
