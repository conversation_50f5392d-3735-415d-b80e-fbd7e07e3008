package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.realmond.temporal_service.crawler.uae.alnair.model.Dictionary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * Public, cache-aware service that exposes the <em>Alnair</em> reference
 * dictionary – cities, countries and catalogs – as a strongly typed
 * {@link Dictionary.Info} DTO.
 * <p>
 * The service delegates the actual HTTP retrieval and HTML decoding to the
 * {@link AlnairDictFeignRestClient}. Caching is handled via Spring Cache
 * abstraction with a Caffeine backend configured in
 * {@link AlnairDictFeignConfiguration}.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlnairDictionaryService {

    private final AlnairDictFeignRestClient client;

    /**
     * Returns the latest {@link Dictionary.Info} reference data. The result is cached to
     * avoid repeated network calls during the configured TTL.
     *
     * @return decoded reference dictionary (cities, countries, catalogs)
     * @throws IllegalStateException if the expected dictionary structure is
     *                               missing in the response
     */
    @Cacheable(cacheManager = "alnairDictCacheManager", value = "alnairDictionary", unless = "#result == null")
    public Dictionary.Info getReferenceData() {
        log.debug("Cache miss – fetching Alnair reference dictionary via Feign");

        Dictionary dictionary = client.getDictionary();
        if (dictionary == null ||
            dictionary.getLoaderData() == null ||
            dictionary.getLoaderData().getAppLayout() == null ||
            dictionary.getLoaderData().getAppLayout().getInfo() == null) {
            throw new IllegalStateException("Alnair dictionary structure missing");
        }
        return dictionary.getLoaderData().getAppLayout().getInfo();
    }
} 