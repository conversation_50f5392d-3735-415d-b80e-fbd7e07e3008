import json
from typing import Any, Dict, List, Union


class PackedJSONDecoder:
    """
    Decoder for packed JSON format where values are stored in a root array
    and referenced using underscore-prefixed numeric keys.
    
    Example:
    [
        {"_1": 2},           # Object with reference to index 1 and 2
        "pages/project",     # Index 1: string value
        {"_3": 4},           # Index 2: nested object
        "data",              # Index 3: string value
        {...}                # Index 4: nested object
    ]
    """
    
    def __init__(self, packed_data: List[Any]):
        """Initialize decoder with packed data array"""
        self.packed_data = packed_data
        
    def decode(self, start_index: int = 0) -> Any:
        """
        Decode the packed JSON starting from given index
        
        Args:
            start_index: Index in the packed array to start decoding from
            
        Returns:
            Decoded data structure
        """
        return self._decode_value(start_index)
    
    def _decode_value(self, index: int) -> Any:
        """Recursively decode a value at given index"""
        if index < 0 or index >= len(self.packed_data):
            return None
            
        value = self.packed_data[index]
        
        # If it's a dict with underscore keys, it's a reference object
        if isinstance(value, dict) and self._has_reference_keys(value):
            return self._decode_reference_object(value)
        
        # If it's a list, decode each element
        elif isinstance(value, list):
            return [self._decode_value(item) if isinstance(item, int) and item >= 0 else item 
                   for item in value]
        
        # If it's a dict without reference keys, decode recursively
        elif isinstance(value, dict):
            return {k: self._decode_value(v) if isinstance(v, int) and v >= 0 else v 
                   for k, v in value.items()}
        
        # Otherwise return the value as-is
        else:
            return value
    
    def _has_reference_keys(self, obj: Dict) -> bool:
        """Check if object contains reference keys (starting with underscore)"""
        return any(key.startswith('_') and key[1:].isdigit() for key in obj.keys())
    
    def _decode_reference_object(self, obj: Dict) -> Dict[str, Any]:
        """Decode an object containing reference keys"""
        result = {}
        
        for key, value in obj.items():
            if key.startswith('_') and key[1:].isdigit():
                # This is a reference key
                property_name_index = int(key[1:])
                
                # Get the property name from the referenced index
                if property_name_index < len(self.packed_data):
                    property_name = self.packed_data[property_name_index]
                    
                    # Handle special case where value is -5 (seems to be null/undefined)
                    if value == -5:
                        result[property_name] = None
                    else:
                        # Decode the value at the referenced index
                        result[property_name] = self._decode_value(value)
            else:
                # Regular key-value pair, decode if value is a reference
                if isinstance(value, int) and value >= 0:
                    result[key] = self._decode_value(value)
                else:
                    result[key] = value
                    
        return result


def decode_packed_json_file(file_path: str) -> Any:
    """
    Convenience function to decode a packed JSON file
    
    Args:
        file_path: Path to the JSON file containing packed data
        
    Returns:
        Decoded data structure
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        packed_data = json.load(f)
    
    if not isinstance(packed_data, list):
        raise ValueError("Packed JSON must be a list/array")
    
    decoder = PackedJSONDecoder(packed_data)
    return decoder.decode()


def decode_packed_json_string(json_string: str) -> Any:
    """
    Convenience function to decode a packed JSON string
    
    Args:
        json_string: JSON string containing packed data
        
    Returns:
        Decoded data structure
    """
    packed_data = json.loads(json_string)
    
    if not isinstance(packed_data, list):
        raise ValueError("Packed JSON must be a list/array")
    
    decoder = PackedJSONDecoder(packed_data)
    return decoder.decode()


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) > 1:
        # Use command line argument if provided
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else "decoded_output.json"
    else:
        # Default to tmp.json if it exists
        input_file = "tmp.json"
        output_file = "decoded_output.json"
    
    try:
        print(f"Decoding {input_file}...")
        decoded = decode_packed_json_file(input_file)
        
        # Pretty print the first level to see the structure
        print("Decoded structure:")
        if isinstance(decoded, dict):
            for key, value in list(decoded.items())[:5]:  # Show first 5 keys
                if isinstance(value, (str, int, float, bool)) or value is None:
                    print(f"  {key}: {value}")
                else:
                    print(f"  {key}: <{type(value).__name__}>")
            if len(decoded) > 5:
                print(f"  ... and {len(decoded) - 5} more keys")
        
        # Save decoded JSON to a new file
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(decoded, f, indent=2, ensure_ascii=False)
        
        print(f"\nDecoded JSON saved to '{output_file}'")
        print(f"Usage: python {sys.argv[0]} <input_file> [output_file]")
        
    except FileNotFoundError:
        print(f"File not found: {input_file}")
        print(f"Usage: python {sys.argv[0]} <input_file> [output_file]")
    except Exception as e:
        print(f"Error: {e}") 