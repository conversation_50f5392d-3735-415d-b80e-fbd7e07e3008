# Alnair Dictionary Service – Technical Specification

## 1. Purpose
Provide an injectable Spring service that retrieves, decodes and caches the "catalogs" dictionary exposed on https://alnair.ae/app and maps it to the existing `Dictionary` POJO.

## 2. Glossary
* **Packed-JSON** – Array-based JSON compression format used by Alnair where values are stored once and referenced through underscore-prefixed numeric keys (see `api_decoder.py`).
* **Dictionary** – Java data model located in `…/alnair/model/Dictionary.java`.
* **TTL** – Time-to-live for the in-memory cache.
* **Catalogs** – Nested POJO inside `Dictionary` exposing the catalog hierarchy returned by Alnair.

## 3. Public API
```
Catalogs AlnairDictionaryService.getCatalogs()
```
Returns the latest `Catalogs` object. The method is side-effect free and transparently cached.

## 4. Configuration (`AlnairDictSettings`)
| Property                                   | Type   | Default                     | Description                          |
|--------------------------------------------|--------|-----------------------------|--------------------------------------|
| `crawler.uae.alnair.dict.base-url`         | URL    | `https://alnair.ae`         | Public root of the Alnair web site. |
| `crawler.uae.alnair.dict.dictionary-path`  | String | `/app`                      | Path whose HTML carries the packed dictionary. |
| `crawler.uae.alnair.dict.cache-ttl`        | Duration | `1d`                    | How long the decoded catalogs stay in the local cache. |
| `crawler.uae.alnair.dict.cache-max-size`   | Long     | `100`                   | Maximum number of `Catalogs` instances (entries) retained in the Caffeine cache. |
| `crawler.uae.alnair.dict.retry`            | `RequestRetrySettings` | *(defaults from `RequestRetrySettings`)* | HTTP retry/back-off policy built from existing `RequestRetrySettings` class located at `…/crawler/RequestRetrySettings.java`. |

`AlnairDictSettings` is a standalone `@ConfigurationProperties(prefix = "crawler.uae.alnair.dict")` **`@Component`** class (does **not** extend any parent settings).

## 5. Components & Packages
All classes live under
```
com.realmond.temporal_service.crawler.uae.alnair
└── dict          <— public service, config, cache, HTML decoder, Feign ctx
└── decoder       <— reusable Packed-JSON decoder (shared by other modules)
```

### 5.1 `AlnairDictFeignRestClient` (`…alnair.dict`)
```java
@FeignClient(
    name = "alnair-web",
    url = "${crawler.uae.alnair.dict.base-url}",
    configuration = AlnairDictFeignConfiguration.class)
public interface AlnairDictFeignRestClient {
    @GetMapping("${crawler.uae.alnair.dict.dictionary-path}")
    Dictionary getDictionary();
}
```
The custom **HTML decoder** (see §5.3) returns the full <code>Dictionary</code> object. Catalog extraction is delegated to the service layer (§5.5), keeping the decoder completely type-agnostic.

### 5.2 `AlnairDictFeignConfiguration` (`…alnair.dict`)
* Registers a Jackson `ObjectMapper` bean **local to this module** (not marked `@Primary`).
* Defines a **cache manager** backed by Caffeine (`alnairDictionary` cache) with TTL pulled from `AlnairDictSettings`.
* Registers `AlnairDictHtmlDecoder` as the only Feign `Decoder`.
* Adds a `RequestInterceptor` that applies realistic browser headers via `FingerprintGenerator`.
* Requires a singleton, thread-safe `FingerprintGenerator` bean already available in the parent Spring context.
* Provides a `Retryer.Default` bean built from `AlnairDictSettings.retry` (exponential back-off).
* The `ObjectMapper` bean is singleton and reused across requests (thread-safe).

Example beans:

```java
@Bean
public RequestInterceptor alnairDictRequestInterceptor(FingerprintGenerator fingerprintGenerator) {
    return template -> fingerprintGenerator.applyBrowserHeaders(template);
}

@Bean
public Retryer alnairDictRetryer(AlnairDictSettings settings) {
    return new Retryer.Default(
            settings.getRetry().getRetryDelayMs(),
            settings.getRetry().getMaxPeriodMs(),
            settings.getRetry().getMaxRetries());
}
```

### 5.3 `AlnairDictHtmlDecoder` (`…alnair.dict`)
Responsibilities:
1. Read full HTML from the Feign `Response`.
2. Parse with Jsoup.
3. Locate `<script>` element whose body contains `streamController.enqueue(`.
4. Extract the JavaScript string argument (regex shown in §7). Unescape JSON string via `objectMapper.readValue("\"" + raw + "\"", String.class)`.
5. Deserialize the **packed JSON** into `List<Object>` using `objectMapper`.
6. Run `PackedJsonDecoder` (Java port of `api_decoder.py`) starting at index `0`, obtaining a normal `Map`.
7. Convert the decoded map to the caller-requested Java type (obtained from Feign's generic return type) with `objectMapper.convertValue()`.
8. Return the fully-typed instance (typically `Dictionary`); otherwise throw `FeignException` with the original response for observability.

### 5.4 `PackedJsonDecoder` (`…alnair.decoder`)
* Straight 1-to-1 port of `api_decoder.py` into Java.
* API:
  ```java
  public final class PackedJsonDecoder {
      public PackedJsonDecoder(List<Object> packed);
      public Object decode(int startIndex);                 // defaults to 0
      public Map<String, Object> decodeToMap();             // convenience – decode(0)
      public Map<String, Object> decodeToMap(int startIndex);
  }
  ```
* Handles `-5` sentinel as `null` (this is the **only** sentinel value used by the source Python implementation).

### 5.5 `AlnairDictionaryService` (`…alnair.dict`)
```java
@Slf4j
@Service
@RequiredArgsConstructor
public class AlnairDictionaryService {
    private final AlnairDictFeignRestClient client;

    @Cacheable(cacheManager = "alnairDictCacheManager", value = "alnairDictionary", unless = "#result == null")
    public Catalogs getCatalogs() {
        Dictionary dictionary = client.getDictionary();
        if (dictionary == null ||
            dictionary.getLoaderData() == null ||
            dictionary.getLoaderData().getAppLayout() == null ||
            dictionary.getLoaderData().getAppLayout().getInfo() == null) {
            throw new IllegalStateException("Alnair dictionary structure missing");
        }
        return dictionary.getLoaderData().getAppLayout().getInfo().getCatalogs();
    }
}
```

### 5.6 Cache configuration
* Uses Spring Cache Abstraction with Caffeine implementation.
* `@EnableCaching` already enabled at parent project level; otherwise add a lightweight config class.
* Cache bean example:
  ```java
  @Bean
  public CacheManager alnairDictCacheManager(AlnairDictSettings settings) {
      return new CaffeineCacheManager("alnairDictionary") {{
          setCaffeine(Caffeine.newBuilder()
                  .expireAfterWrite(settings.getCacheTtl())
                  .maximumSize(settings.getCacheMaxSize()));
      }};
  }
  ```

## 6. Control Flow
```
Client call → AlnairDictionaryService.getCatalogs()
             │  (hit?)
             ├─> cache miss → AlnairDictFeignRestClient.getDictionary()
             │                 ↓
             │             Feign → GET {baseUrl}{dictionaryPath}
             │                 ↓
             │        AlnairDictHtmlDecoder
             │             1. HTML → Jsoup
             │             2. Regex extraction
             │             3. PackedJsonDecoder → Dictionary
             │                 ↓
             └── cached Catalogs ←───────────────┘
```

## 7. Implementation Details & Gotchas
* **Regex for JSON capture** (DOTALL):
  ```java
  Pattern p = Pattern.compile("streamController\\.enqueue\\(\"(.*?)\"\)", Pattern.DOTALL);
  ```
* Use `StandardCharsets.UTF_8` everywhere.
* **HTML size** may be > 1 MB; read once with `InputStream.readAllBytes()`.
  In-memory buffering is sufficient for the expected traffic profile.
* **Error scenarios**:
  * Non-2xx HTTP status → let Feign propagate its native exception (fail fast).
  * Missing script → log warn & throw `FeignException.errorStatus("Missing dictionary", response)`.
  * JSON decode failure → wrap in `FeignException`.
  * Unknown sentinel value in the packed JSON → throw `IllegalStateException` (fail fast), as this indicates that the external encoding contract has changed.
* **Thread-safety**: both the module-local `ObjectMapper` and `PackedJsonDecoder` are stateless and thread-safe; they may be cached and reused across requests.
* **Unit conversions**: The decoder returns `Map`
with correct Java types thanks to Jackson – no manual string→int casts necessary.

## 8. Testing Strategy
1. **Decoder tests** – Feed stored `app.html` sample, assert that the resulting object is an instance of `Catalogs` and contains expected values.
2. **Integration test** – Wire Feign to MockWebServer serving the fixture HTML.
3. **Cache test** – Ensure service is invoked once when called twice within TTL.

## 9. Dependencies
* `org.jsoup:jsoup`
* `com.github.ben-manes.caffeine:caffeine` (cache backend)
* `org.springframework.cloud:spring-cloud-starter-openfeign`

Versions are inherited from the existing project BOM; no new BOM entries required.

## 10. Future Extensions
* Retry & Circuit-breaker using Resilience4J around the Feign client.
* Persist decoded dictionary in PostgreSQL for historical snapshots.
* Expose `/api/dictionary` REST endpoint.

## 11. Logging Guidelines

### General
* Use Lombok's `@Slf4j` annotation on every concrete class in this module. The annotation injects a `static final org.slf4j.Logger log` field; **do not** create loggers manually via `LoggerFactory`.
* Message format: plain strings with `{}` placeholders (SLF4J style); never concatenate.
* Never log full HTML payloads or packed JSON arrays—they can exceed 1 MB.

### Category naming
```
com.realmond.temporal_service.crawler.uae.alnair      (umbrella)
└── dict                                             (public service & Feign)
└── decoder                                          (PackedJsonDecoder port)
```
The Lombok-generated logger uses the declaring class's FQN, which naturally follows the hierarchy above.

### Recommended levels & messages
| Location / Event                                   | Level | Example message                                                          |
|----------------------------------------------------|-------|--------------------------------------------------------------------------|
| AlnairDictionaryService – cache **miss**           | DEBUG | "Cache miss – fetching Alnair catalogs via Feign"                        |
| AlnairDictFeignRestClient – invocation             | TRACE | "GET {}{}", baseUrl, dictionaryPath                                      |
| AlnairDictHtmlDecoder – start of decode            | DEBUG | "Decoding Alnair dictionary HTML ({} bytes)", htmlLength                 |
| AlnairDictHtmlDecoder – script tag not found       | WARN  | "Stream controller script not found in HTML – cannot extract dictionary" |
| AlnairDictHtmlDecoder – packed JSON decode fail    | ERROR | "Failed to decode packed JSON from Alnair HTML", exception               |
| PackedJsonDecoder – unknown sentinel value         | ERROR | "Unknown sentinel value {} at pos {}", value, position                   |
| PackedJsonDecoder – successful top-level decode    | TRACE | "Packed JSON decoded to Map with {} keys", mapSize                       |

### Operational hints
1. Set the default level for `…alnair` to INFO in production; enable DEBUG/TRACE temporarily during troubleshooting:
   ```properties
   logging.level.com.realmond.temporal_service.crawler.uae.alnair=DEBUG
   ```
2. In tests, add a log appender (e.g. Logback `ListAppender`) if you need to assert on specific log outputs.
3. All ERROR-level logs are accompanied by thrown exceptions; no additional wrapping is required.

## 12. File Map

Relative to the project root (`/src` unless noted otherwise), the following files are introduced or referenced by this specification.

### 12.1  Main sources
| Path | Purpose |
|------|---------|
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictSettings.java` | Spring `@ConfigurationProperties` for external settings |
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictionaryService.java` | Public, cache-aware service API |
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictFeignRestClient.java` | Feign interface to Alnair web site |
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictFeignConfiguration.java` | Feign-specific bean configuration (cache, `ObjectMapper`, decoder) |
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictHtmlDecoder.java` | Custom Feign `Decoder` parsing packed JSON inside HTML |
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/decoder/PackedJsonDecoder.java` | Java port of `api_decoder.py` |

### 12.2  External model reused
| Path | Purpose |
|------|---------|
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/model/Dictionary.java` | Existing domain model referenced by the service |

### 12.3  Test sources & fixtures
| Path | Purpose |
|------|---------|
| `src/test/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictHtmlDecoderTest.java` | Unit test for HTML decoder (fixture based) |
| `src/test/java/com/realmond/temporal_service/crawler/uae/alnair/dict/AlnairDictionaryServiceIT.java` | Integration test with MockWebServer |
| `src/test/resources/crawler/uae/alnair/app.html` | Sample HTML page captured from production (used by tests) |
| `src/test/resources/crawler/uae/alnair/dictionary.encoded.json` | Packed-JSON fixture used by `PackedJsonDecoder` tests |

### 12.4  Supporting script (reference)
| Path | Purpose |
|------|---------|
| `src/main/java/com/realmond/temporal_service/crawler/uae/alnair/dict/api_decoder.py` | Original Python implementation serving as porting reference (read-only) |
| `src/main/java/com/realmond/temporal_service/crawler/FingerprintGenerator.java` | Utility that adds realistic browser headers in Feign calls |

This map serves as a quick lookup for engineers and reviewers, linking every logical component described in this document to a concrete location in the codebase.

---
This specification is exhaustive and unambiguous; every class, package, property and corner-case has been declared to enable straight-forward implementation without further clarification.