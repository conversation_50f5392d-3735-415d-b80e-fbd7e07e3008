package com.realmond.temporal_service.crawler.uae.alnair.legacy;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign client for Alnair API.
 * This interface defines the API endpoints for fetching data from Alnair.
 */
@FeignClient(name = "alnairApi", url = "${alnair.api.base-url:https://api.alnair.ae}", configuration = com.realmond.temporal_service.crawler.uae.alnair.api.AlnairApiFeignConfiguration.class)
public interface AlnairApiFeignClient {

    /**
     * Fetches the main data from the API.
     *
     * @return The JSON response as a string
     */
    @GetMapping("/main")
    String getMainData();

    /**
     * Fetches projects data from the API.
     *
     * @param page The page number
     * @param limit The number of items per page
     * @return The JSON response as a string
     */
    @GetMapping("/project/find")
    String getProjects(
            @RequestParam("page") int page,
            @RequestParam("limit") int limit
    );
}
