package com.realmond.temporal_service.crawler.uae.alnair;

import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Implementation of Alnair Crawler.
 * Uses existing Alnair parser components to fetch and parse developer data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlnairDeveloperCrawler implements Crawler<DeveloperModel> {

    private final AlnairApiService alnairApiService;

    @Override
    public String getSourceUrn() {
        return AlnairSettings.SOURCE_URN;
    }

    @Override
    public Boolean supportsPagination() {
        return false;
    }

    @Override
    public List<String> fetchAllPageIds() {
        throw NonRetryableCrawlerException.INCREMENTAL_FETCH_NOT_SUPPORTED;
    }

    @Override
    public List<CrawlRecord<DeveloperModel>> parsePage(String pageNum) {
        throw NonRetryableCrawlerException.INCREMENTAL_FETCH_NOT_SUPPORTED;
    }

    @Override
    public List<CrawlRecord<DeveloperModel>> fetchAll() {
        return alnairApiService.fetchDevelopers();
    }
}
