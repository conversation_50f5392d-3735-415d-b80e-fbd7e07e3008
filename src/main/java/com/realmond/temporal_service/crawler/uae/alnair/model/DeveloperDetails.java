package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realmond.temporal_service.crawler.uae.alnair.model.developers.DeveloperStatsTotal;
import com.realmond.temporal_service.crawler.uae.alnair.model.developer_details.DeveloperUnitStats;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.ImageResource;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.Photo;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.SalesOffice;
import lombok.Data;

import java.util.List;

/**
 * Detailed developer profile (details.developer.json).
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeveloperDetails {

    private Integer id;
    @JsonProperty("is_promoted")
    private Boolean isPromoted;
    private String address;
    private List<Integer> cities;
    private String title;
    private String description;
    @JsonProperty("cooperation_terms")
    private String cooperationTerms;
    private String website;
    private Long phone;
    @JsonProperty("founded_at")
    private String foundedAt;

    @JsonProperty("stats_units")
    private List<DeveloperUnitStats> statsUnits;

    @JsonProperty("stats_total")
    private List<DeveloperStatsTotal> statsTotal;

    private ImageResource logo;
    private List<Photo> photos;
    private Object promotions;
    @JsonProperty("sales_offices")
    private List<SalesOffice> salesOffices;
}
