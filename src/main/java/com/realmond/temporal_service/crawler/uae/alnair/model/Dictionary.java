package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * Represents the dictionary structure returned by <PERSON><PERSON><PERSON> loaderData endpoint.
 *
 * The JSON shape (see src/test/resources/crawler/uae/alnair/dictionary.json) is:
 * {
 *   "loaderData": {
 *     "routes/layouts/app_layout": {
 *       "info": {
 *         "catalogs": { ... }
 *       }
 *     }
 *   }
 * }
 *
 * All nested objects are modelled as static inner classes to keep the
 * declaration self-contained and follow the established pattern inside the
 * {@code com.realmond.temporal_service.crawler.uae.aro.model} package.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Dictionary {
    @JsonProperty("loaderData")
    private LoaderData loaderData;

    /* --------------------------------------------------------------------- */
    /*                               NESTED POJOs                            */
    /* --------------------------------------------------------------------- */

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LoaderData {
        /**
         * There may be several route/layout keys. Currently we only map the
         * one used by the file – "routes/layouts/app_layout". Additional keys
         * will be ignored by Jackson unless explicitly added later.
         */
        @JsonProperty("routes/layouts/app_layout")
        private RouteLayout appLayout;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RouteLayout {
        private Info info;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Info {
        private Catalogs catalogs;
        /**
         * List of supported cities returned by the dictionary endpoint.
         */
        private List<City> cities;

        /**
         * List of supported countries returned by the dictionary endpoint.
         */
        private List<Country> countries;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Catalogs {
        @JsonProperty("compound_villa_plot")
        private Catalog compoundVillaPlot;
        @JsonProperty("payment_plan_when")
        private Catalog paymentPlanWhen;
        @JsonProperty("unit_media_type")
        private Catalog unitMediaType;
        @JsonProperty("unit_furnishing")
        private Catalog unitFurnishing;
        @JsonProperty("project_gallery_category")
        private Catalog projectGalleryCategory;
        @JsonProperty("project_facilities")
        private Catalog projectFacilities;
        @JsonProperty("project_media_type")
        private Catalog projectMediaType;
        private Catalog rooms;
        @JsonProperty("unit_type_room")
        private Catalog unitTypeRoom;
        @JsonProperty("unit_status")
        private Catalog unitStatus;
        @JsonProperty("project_badges")
        private Catalog projectBadges;
        @JsonProperty("development_stage")
        private Catalog developmentStage;
        @JsonProperty("project_sales_status")
        private Catalog projectSalesStatus;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Catalog {
        private Integer id;
        private List<Option> options;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Option {
        private Integer id;
        private String value;
        private String key;
        /**
         * The content of the "data" field varies between catalogs. It can be
         * an object with arbitrary attributes (icon, bg_color, …) or an empty
         * array. Using {@code Object} ensures maximum flexibility.
         */
        private Object data;
    }

    /* --------------------------------------------------------------------- */
    /*                          ADDITIONAL NESTED POJOs                       */
    /* --------------------------------------------------------------------- */

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class City {
        private Integer id;
        private String name;

        @JsonProperty("country_id")
        private Integer countryId;

        private String latitude;
        private String longitude;
        private String currency;

        @JsonProperty("is_private")
        private Boolean isPrivate;

        /**
         * Stored as 0/1 in JSON. Using Integer provides flexibility for future
         * non-boolean representations whilst still being easy to interpret.
         */
        @JsonProperty("is_permit_number")
        private Integer isPermitNumber;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Country {
        private Integer id;
        private String title;
        private String currency;
    }
} 