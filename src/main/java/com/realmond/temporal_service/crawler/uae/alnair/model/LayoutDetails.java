package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realmond.temporal_service.crawler.uae.alnair.model.layout_details.LayoutLevel;
import com.realmond.temporal_service.crawler.uae.alnair.model.layout_details.UnitsSummary;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.PaymentPlan;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.Photo;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LayoutDetails {

    private Integer id;
    private String title;
    private String description;

    private String area;
    @JsonProperty("area_balcony")
    private String areaBalcony;
    @JsonProperty("area_living")
    private String areaLiving;

    @JsonProperty("catalog_number_rooms_id")
    private Integer catalogNumberRoomsId;
    @JsonProperty("catalog_furnishing_id")
    private Integer catalogFurnishingId;

    private Integer level;

    private List<LayoutLevel> levels;
    private List<Photo> photos;

    private UnitsSummary units;

    @JsonProperty("payment_plans")
    private List<PaymentPlan> paymentPlans;
}
