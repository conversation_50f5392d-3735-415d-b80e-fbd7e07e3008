package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.realmond.temporal_service.crawler.uae.alnair.model.layout_units.LayoutUnitGroup;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LayoutUnitsResponse {

    private Map<String, LayoutUnitGroup> rooms = new HashMap<>();

    @JsonAnySetter
    public void add(String key, LayoutUnitGroup value) {
        rooms.put(key, value);
    }
}
