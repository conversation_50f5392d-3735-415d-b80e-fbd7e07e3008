package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realmond.temporal_service.crawler.uae.alnair.model.projects.ProjectsData;
import lombok.Data;

/**
 * Root wrapper for the /projects endpoint – corresponds to {@code projects.json} fixture.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectsResponse {

    private ProjectsData data;

    /** Total number of items across all pages */
    private Integer count;
    /** Current page index (1-based) */
    private Integer page;
    /** Number of available pages */
    private Integer pages;
} 