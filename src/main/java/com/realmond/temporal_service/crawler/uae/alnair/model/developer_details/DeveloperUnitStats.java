package com.realmond.temporal_service.crawler.uae.alnair.model.developer_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Per-room/unit statistics in developer profile.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeveloperUnitStats {
    @JsonProperty("city_id")
    private Integer cityId;
    @JsonProperty("room_id")
    private Integer roomId;
    private String key;
    private Integer count;
    @JsonProperty("price_from")
    private Integer priceFrom;
    @JsonProperty("price_to")
    private Integer priceTo;
    @JsonProperty("price_m2_from")
    private Integer priceM2From;
    @JsonProperty("price_m2_to")
    private Integer priceM2To;
    @JsonProperty("area_from")
    private String areaFrom;
    @JsonProperty("area_to")
    private String areaTo;
} 