package com.realmond.temporal_service.crawler.uae.alnair.model.developers;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.ImageResource;
import lombok.Data;

import java.util.List;

/**
 * Represents a developer record in the public list endpoint.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeveloperSummary {
    private Integer id;
    private Integer weight;
    private String title;
    private ImageResource logo;

    @JsonProperty("stats_total")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<DeveloperStatsTotal> statsTotal;
}
