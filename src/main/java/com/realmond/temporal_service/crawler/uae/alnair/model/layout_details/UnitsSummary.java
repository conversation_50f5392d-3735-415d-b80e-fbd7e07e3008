package com.realmond.temporal_service.crawler.uae.alnair.model.layout_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnitsSummary {
    @JsonProperty("construction_id")
    private Integer constructionId;
    @JsonProperty("project_id")
    private Integer projectId;
    private Integer count;
    @JsonProperty("price_min")
    private Integer priceMin;
    @JsonProperty("price_max")
    private Integer priceMax;
    @JsonProperty("area_min")
    private Double areaMin;
    @JsonProperty("area_max")
    private Double areaMax;
    @JsonProperty("area_balcony_min")
    private Double areaBalconyMin;
    @JsonProperty("area_balcony_max")
    private Double areaBalconyMax;
    @JsonProperty("catalog_unit_type_room_ids")
    private List<Integer> catalogUnitTypeRoomIds;
    private List<Object> units; // unknown structure, empty in fixture
} 