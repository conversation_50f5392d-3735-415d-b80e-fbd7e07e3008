package com.realmond.temporal_service.crawler.uae.alnair.model.layout_units;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LayoutUnitTotal {
    private Integer count;
    @JsonProperty("price_min")
    private Integer priceMin;
    @JsonProperty("price_max")
    private Integer priceMax;
    @JsonProperty("area_min")
    private Double areaMin;
    @JsonProperty("area_max")
    private Double areaMax;
} 