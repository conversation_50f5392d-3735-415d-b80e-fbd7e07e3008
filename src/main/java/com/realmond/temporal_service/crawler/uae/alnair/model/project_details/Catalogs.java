package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Catalog IDs attached to a project (development_stage, facilities, ...).
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Catalogs {
    @JsonProperty("development_stage")
    private List<Integer> developmentStage;
    @JsonProperty("project_facilities")
    private List<Integer> projectFacilities;
    @JsonProperty("project_sales_status")
    private List<Integer> projectSalesStatus;
    @JsonProperty("project_badges")
    private List<Integer> projectBadges;
}
