package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Represents a single construction (building) within a project.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Construction {
    private Integer id;
    private String address;
    private Double latitude;
    private Double longitude;
    @JsonProperty("project_id")
    private Integer projectId;
    private String number;
    private List<List<Double>> polygon;
    @JsonProperty("floor_to")
    private Integer floorTo;
    @JsonProperty("completion_construction_at")
    private String completionConstructionAt;
    @JsonProperty("is_grid_view")
    private Boolean isGridView;
    @JsonProperty("is_floor_plan_view")
    private Boolean isFloorPlanView;
    @JsonProperty("property_age")
    private Integer propertyAge;
    private Catalogs catalogs;
    private ConstructionStatistics statistics;
}
