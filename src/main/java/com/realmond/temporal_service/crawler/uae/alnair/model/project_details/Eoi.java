package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Expression of Interest (EOI) block.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Eoi {
    @JsonProperty("is_eoi_return")
    private Boolean isEoiReturn;
    @JsonProperty("eoi_items")
    private List<Object> eoiItems; // structure unknown as of now
}
