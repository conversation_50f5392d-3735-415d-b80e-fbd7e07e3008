package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Gallery entry belonging to a project – e.g. presentation, infrastructure.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Gallery {
    private Integer id;
    private String type;
    private String name;
    private String description;
    private String percentage;
    @JsonProperty("date_at")
    private String dateAt;
    private Integer total;
    @JsonProperty("number_of_photos")
    private Integer numberOfPhotos;
    @JsonProperty("number_of_videos")
    private Integer numberOfVideos;
    private List<Photo> photos;
}
