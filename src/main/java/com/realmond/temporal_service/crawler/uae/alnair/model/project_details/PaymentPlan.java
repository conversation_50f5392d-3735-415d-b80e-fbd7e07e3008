package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Payment plan description attached to a project.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentPlan {
    private Integer id;
    private String title;
    @JsonProperty("predicted_completion_at")
    private String predictedCompletionAt;
    @JsonProperty("updated_at")
    private String updatedAt;
    private List<String> constructions;
    private PaymentPlanInfo info;
    private Map<String, List<PaymentPlanItem>> items;
}
