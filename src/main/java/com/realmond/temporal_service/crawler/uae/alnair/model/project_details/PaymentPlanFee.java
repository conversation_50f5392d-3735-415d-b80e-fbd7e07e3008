package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * Simple fee container used inside {@link PaymentPlanInfo}.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentPlanFee {
    private Integer percent;
    private Integer fix;
    private Integer fix_m2; // JSON camel-case is fix_m2; Lombok will generate getter getFix_m2()
}
