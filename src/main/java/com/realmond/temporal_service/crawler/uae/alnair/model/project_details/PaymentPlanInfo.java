package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * High-level breakdown of a {@link PaymentPlan} (booking, construction, handover, ...).
 * <p>
 * The structure is quite verbose – only the most common fields are explicitly
 * mapped for now. <PERSON> will silently ignore any others thanks to
 * {@link JsonIgnoreProperties}.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentPlanInfo {

    /* --------------------------- Booking phase --------------------------- */
    @JsonProperty("on_booking_percent")
    private Integer onBookingPercent;
    @JsonProperty("on_booking_fix")
    private Integer onBookingFix;
    @JsonProperty("on_booking_fix_m2")
    private Integer onBookingFixM2;
    @JsonProperty("on_booking_payments_count")
    private Integer onBookingPaymentsCount;
    @JsonProperty("on_booking_fees")
    private PaymentPlanFee onBookingFees;

    /* ------------------------- Construction phase ----------------------- */
    @JsonProperty("on_construction_percent")
    private Integer onConstructionPercent;
    @JsonProperty("on_construction_fix")
    private Integer onConstructionFix;
    @JsonProperty("on_construction_fix_m2")
    private Integer onConstructionFixM2;
    @JsonProperty("on_construction_payments_count")
    private Integer onConstructionPaymentsCount;
    @JsonProperty("on_construction_fees")
    private PaymentPlanFee onConstructionFees;

    /* --------------------------- Handover phase ------------------------- */
    @JsonProperty("on_handover_percent")
    private Integer onHandoverPercent;
    @JsonProperty("on_handover_fix")
    private Integer onHandoverFix;
    @JsonProperty("on_handover_fix_m2")
    private Integer onHandoverFixM2;
    @JsonProperty("on_handover_payments_count")
    private Integer onHandoverPaymentsCount;
    @JsonProperty("on_handover_fees")
    private PaymentPlanFee onHandoverFees;

    /* --------------------------- Additional fees ------------------------ */
    private List<AdditionalPayment> additional;
    @JsonProperty("additional_percent")
    private Integer additionalPercent;
    @JsonProperty("additional_fix")
    private Integer additionalFix;
    @JsonProperty("additional_fix_m2")
    private Integer additionalFixM2;

    /* --------------------------- Price totals --------------------------- */
    @JsonProperty("price_total")
    private PriceValue priceTotal;
    @JsonProperty("fees_included_total")
    private PriceValue feesIncludedTotal;

    /* --------------------------- Inner types --------------------------- */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdditionalPayment {
        private String title;
        private Integer percent;
        private Integer fix;
        @JsonProperty("fixM2")
        private Integer fixM2;
    }
}
