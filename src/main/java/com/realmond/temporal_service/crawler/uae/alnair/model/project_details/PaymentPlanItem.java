package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Entry inside the various phases (booking, construction, handover...) of a payment plan.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentPlanItem {
    private String key;
    @JsonProperty("catalog_payment_plan_when_id")
    private Integer catalogPaymentPlanWhenId;
    private String milestone;
    @JsonProperty("when_at")
    private String whenAt;
    @JsonProperty("when_period")
    private String whenPeriod;
    @JsonProperty("when_period_count")
    private Integer whenPeriodCount;
    @JsonProperty("when_percent")
    private Integer whenPercent;

    private Integer percent;
    private Integer price;
    @JsonProperty("price_m2")
    private Integer priceM2;
    @JsonProperty("price_ft2")
    private Integer priceFt2;

    @JsonProperty("total_percent")
    private Integer totalPercent;
    @JsonProperty("total_price")
    private Integer totalPrice;
    @JsonProperty("total_price_m2")
    private Integer totalPriceM2;
    @JsonProperty("total_price_ft2")
    private Integer totalPriceFt2;

    @JsonProperty("repeat_count")
    private Integer repeatCount;
    @JsonProperty("repeat_period")
    private String repeatPeriod;
    @JsonProperty("repeat_period_count")
    private Integer repeatPeriodCount;
    @JsonProperty("repeat_from_at")
    private String repeatFromAt;
    @JsonProperty("repeat_to_at")
    private String repeatToAt;

    @JsonProperty("is_roi")
    private Boolean isRoi;
}
