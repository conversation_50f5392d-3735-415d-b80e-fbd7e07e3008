package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * Holds triple set of values (percent, fix, fix_m2) for price totals.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriceValue {
    private Integer percent;
    private Integer fix;
    private Integer fix_m2;
}
