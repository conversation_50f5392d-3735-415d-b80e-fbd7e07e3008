package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Detailed description of a project returned by <PERSON><PERSON><PERSON> "project_page" endpoint.
 * <p>
 * Most attributes map 1:1 to the JSON properties (snake_case is converted via
 * {@link JsonProperty} where needed). Optional fields are left as their boxed
 * types (e.g. {@link Integer}, {@link Boolean}) so that {@code null} can signal
 * absence of data.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProjectDetails {

    private Integer id;
    private String title;
    private String description;
    private String type;

    @JsonProperty("updated_at")
    private String updatedAt;
    @JsonProperty("start_at")
    private String startAt;
    @JsonProperty("planned_at")
    private String plannedAt;
    @JsonProperty("predicted_completion_at")
    private String predictedCompletionAt;
    @JsonProperty("completed_at")
    private String completedAt;

    private String website;
    private List<List<Double>> polygon;

    @JsonProperty("dld_project_number")
    private Integer dldProjectNumber;
    @JsonProperty("construction_percent")
    private Integer constructionPercent;
    @JsonProperty("construction_inspection_date")
    private String constructionInspectionDate;
    @JsonProperty("is_range_mode")
    private Boolean isRangeMode;
    @JsonProperty("service_charge")
    private String serviceCharge;
    private String assignment;
    @JsonProperty("escrow_bank")
    private String escrowBank;
    @JsonProperty("escrow_number")
    private String escrowNumber;

    private String ownership;
    @JsonProperty("ownership_lease_duration_years")
    private Integer ownershipLeaseDurationYears;
    @JsonProperty("ownership_lease_end_at")
    private String ownershipLeaseEndAt;
    @JsonProperty("ownership_lease_auto_renewal")
    private Boolean ownershipLeaseAutoRenewal;
    @JsonProperty("ownership_description")
    private String ownershipDescription;

    @JsonProperty("agent_fee_value")
    private String agentFeeValue;
    @JsonProperty("agent_fee_description")
    private String agentFeeDescription;

    private String district;
    private String address;

    private Builder builder;
    @JsonProperty("property_age")
    private Integer propertyAge;

    private ImageResource cover;
    private ImageResource logo;

    @JsonProperty("catalog_recommend")
    private Boolean catalogRecommend;
    @JsonProperty("catalog_sales_status")
    private Integer catalogSalesStatus;
    @JsonProperty("catalog_sales_status_start_at")
    private String catalogSalesStatusStartAt;
    @JsonProperty("catalog_sales_status_end_at")
    private String catalogSalesStatusEndAt;

    private List<Gallery> galleries;
    private List<Brochure> brochures;
    private Catalogs catalogs;
    private List<Construction> constructions;
    private Eoi eoi;
    @JsonProperty("payment_plans")
    private List<PaymentPlan> paymentPlans;

    @JsonProperty("sales_offices")
    private List<SalesOffice> salesOffices;

    private Statistics statistics;

    /* --------------------------------------------------------------------- */
    /*     Inner/simple types placed below for clarity (distinct classes)    */
    /* --------------------------------------------------------------------- */

    /**
     * Fallback POJO to capture "catalogs" objects inside deeply nested
     * structures (e.g. SalesOffice.contacts[*].catalogs). They vary a lot so a
     * simple {@code Map<String, List<Integer>>} is the most flexible option.
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SimpleCatalogContainer {
        private Map<String, List<Integer>> catalogs;
    }
}
