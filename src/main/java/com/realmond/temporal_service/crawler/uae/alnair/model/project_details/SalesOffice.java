package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Sales office/contact point for project purchases. Only a subset of fields
 * required by our parser is represented for now.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesOffice {
    private Integer id;
    @JsonProperty("agency_id")
    private Integer agencyId;
    private String title;
    private String description;
    @JsonProperty("description_short")
    private String descriptionShort;
    @JsonProperty("commission_percent")
    private String commissionPercent;
    @JsonProperty("user_commission_percent")
    private String userCommissionPercent;
    private String phone;
    private String email;
    private String website;
    @JsonProperty("address")
    private String address;
    private Double latitude;
    private Double longitude;
    @JsonProperty("is_available_booking")
    private Boolean isAvailableBooking;
    @JsonProperty("is_priority")
    private Boolean isPriority;
    @JsonProperty("is_open")
    private Boolean isOpen;
    @JsonProperty("logo")
    private ImageResource logo;
    // Other nested objects (contacts, agency, builder etc.) are omitted for brevity.
}
