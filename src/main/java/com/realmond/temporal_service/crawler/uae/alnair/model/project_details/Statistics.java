package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Map;

/**
 * Project-level statistics block (transactions, rents, units, ...).
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Statistics {
    private Map<String, Integer> transactions;
    private Object rents; // Structure not yet mapped
    private TotalStatistics total;
    private Map<String, UnitStatistics> units;
}
