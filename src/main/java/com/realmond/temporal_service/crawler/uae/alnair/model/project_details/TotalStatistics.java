package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Overall statistics block that appears both at construction- and project-level.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TotalStatistics {
    private Integer count;
    @JsonProperty("price_from")
    private Integer priceFrom;
    @JsonProperty("price_to")
    private Integer priceTo;
    @JsonProperty("price_m2_from")
    private Integer priceM2From;
    @JsonProperty("price_m2_to")
    private Integer priceM2To;
    @JsonProperty("units_count")
    private Integer unitsCount;
    @JsonProperty("units_area_mt")
    private String unitsAreaMt;
    @JsonProperty("units_max_floor")
    private Integer unitsMaxFloor;
}
