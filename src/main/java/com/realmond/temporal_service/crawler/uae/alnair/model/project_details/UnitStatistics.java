package com.realmond.temporal_service.crawler.uae.alnair.model.project_details;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Unit-level statistics inside {@link ConstructionStatistics#units} map.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnitStatistics {
    private Integer count;
    @JsonProperty("price_from")
    private Integer priceFrom;
    @JsonProperty("price_to")
    private Integer priceTo;
    @JsonProperty("price_m2_from")
    private Integer priceM2From;
    @JsonProperty("price_m2_to")
    private Integer priceM2To;
    @JsonProperty("area_from")
    private Double areaFrom;
    @JsonProperty("area_to")
    private Double areaTo;
}
