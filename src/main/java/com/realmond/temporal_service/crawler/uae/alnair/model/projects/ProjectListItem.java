package com.realmond.temporal_service.crawler.uae.alnair.model.projects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.ImageResource;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.Catalogs;
import com.realmond.temporal_service.crawler.uae.alnair.model.project_details.Statistics;
import lombok.Data;

/**
 * Lightweight project list record returned by the projects endpoint.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProjectListItem {
    private Integer id;
    private Integer weight;
    private String title;
    private String type;

    private Double latitude;
    private Double longitude;

    private ImageResource logo;
    private ImageResource cover;

    @JsonProperty("construction_percent")
    private String constructionPercent;
    @JsonProperty("construction_inspection_date")
    private String constructionInspectionDate;
    @JsonProperty("construction_percent_out_of_plan")
    private Double constructionPercentOutOfPlan;

    @JsonProperty("agent_fee_value")
    private String agentFeeValue;

    /** Builder is just a string (name) in list response */
    private String builder;

    private Catalogs catalogs;
    private Statistics statistics;
}
