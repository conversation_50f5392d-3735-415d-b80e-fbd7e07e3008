package com.realmond.temporal_service.crawler.uae.alnair.site;

import com.realmond.temporal_service.crawler.uae.alnair.model.ProjectDetailsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * Feign client for https://alnair.ae website that serves packed JSON .data payloads.
 */
@FeignClient(
        name = "alnair-site",
        url = "${crawler.uae.alnair.site-base-url:https://alnair.ae}",
        configuration = AlnairSiteFeignConfiguration.class)
public interface AlnairSiteFeignRestClient {

    /**
     * Retrieves project details as a fully decoded POJO.
     *
     * @param projectId numeric project identifier
     * @return decoded {@link ProjectDetailsResponse} instance
     */
    @GetMapping("/app/project/view/{projectId}.data")
    ProjectDetailsResponse getProjectDetails(@PathVariable("projectId") Integer projectId);
} 