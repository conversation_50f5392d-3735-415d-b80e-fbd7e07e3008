package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.AdModel;
import com.realmond.etl.model.AmenityModel;
import com.realmond.etl.model.ImageModel;
import com.realmond.etl.model.PriceModel;
import com.realmond.etl.model.ProjectModel;
import com.realmond.etl.model.ProjectStats;
import com.realmond.etl.model.UnitStats;
import com.realmond.etl.model.common.BrochureModel;
import com.realmond.etl.model.common.FloorPlanModel;
import com.realmond.etl.model.common.LocationModel;
import com.realmond.etl.model.common.PaymentPlanModel;
import com.realmond.etl.model.common.PaymentStep;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.AroCommon;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import com.realmond.temporal_service.crawler.uae.aro.model.PaymentPlanResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectSummary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Service for processing ARO.ae data using typed REST API responses.
 * Eliminates HTML parsing in favor of structured JSON data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroApiService {

    private final AroFeignRestClient aroClient;
    private final AroEnrichmentService enrichmentService;
    private final AroSettings settings;
    private final AroBuildingApiService buildingApiService;
    private final AroFloorPlanConverter aroFloorPlanConverter;

    /**
     * Result of project conversion including both the ProjectModel and raw API metadata.
     */
    private record ProjectConversionResult(Optional<ProjectModel> projectModel,
                                           Map<String, Object> rawApiMetadata) {
    }

    /**
     * Fetches the total number of pages from ARO.ae using the API.
     *
     * @return The total number of pages
     */
    public int fetchProjectPagesCount() {
        try {
            // Fetch the first page to get pagination information
            AroApiResponse<ProjectSummary> response = aroClient.getProjects(
                  1,
                  settings.getPageSize()
            );

            if (response.getPaging() != null && response.getPaging().getTotal() != null) {
                int totalPages = response.getPaging().getTotal();

                log.info(
                      "Total projects: {}, page size: {}, total pages: {}",
                      response.getTotal(),
                      response.getPaging().getSize(),
                      totalPages
                );
                return totalPages;
            } else {
                log.warn("No pagination information found in response, defaulting to 1 page");
                return 1;
            }
        } catch (Exception e) {
            log.error("Error fetching project pages count: {}", e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching pages count", e);
        }
    }

    /**
     * Fetches and parses all projects from a single page using the typed API.
     *
     * @param page The page number to fetch and parse
     *
     * @return List of CrawlRecord containing all parsed projects from the page
     */
    public List<CrawlRecord<ProjectModel>> fetchProjectsPage(int page) {
        log.info("Fetching ARO.ae page: {}", page);
        List<CrawlRecord<ProjectModel>> result = new ArrayList<>();

        try {
            // Fetch the projects page using typed API
            AroApiResponse<ProjectSummary> response = aroClient.getProjects(
                  page,
                  settings.getPageSize()
            );

            if (response.getData() == null || response.getData().isEmpty()) {
                log.warn("No projects found on page {}", page);
                return result;
            }

            log.info("Found {} projects on page {}", response.getData().size(), page);

            // Process each project on the page
            for (ProjectSummary projectSummary : response.getData()) {
                try {
                    // Fetch detailed project information
                    ProjectDetail projectDetail = aroClient.getProjectBySlug(projectSummary.getSlug());

                    // Convert the typed response to ProjectModel and get raw API data
                    ProjectConversionResult conversionResult = convertToProjectModelWithMetadata(projectDetail,
                                                                                                 projectSummary
                    );

                    if (conversionResult.projectModel().isPresent()) {
                        ProjectModel project = conversionResult.projectModel().get();

                        // Wrap in CrawlRecord with raw API data in metadata
                        CrawlRecord<ProjectModel> crawlRecord = wrapProjectModelInCrawlRecord(project,
                                                                                              conversionResult.rawApiMetadata()
                        );
                        result.add(crawlRecord);

                        log.info(
                              "Successfully processed ARO.ae project: {}",
                              projectSummary.getSlug()
                        );
                    } else {
                        log.warn(
                              "Failed to convert ARO.ae project to ProjectModel: {}",
                              projectSummary.getSlug()
                        );
                    }
                } catch (Exception e) {
                    log.error(
                          "Error processing ARO.ae project {}: {}",
                          projectSummary.getSlug(),
                          e.getMessage(),
                          e
                    );
                    // Continue with next project even if one fails
                }
            }

            log.info("Successfully processed page {} with {} projects", page, result.size());
        } catch (Exception e) {
            log.error("Error fetching ARO.ae page {}: {}", page, e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching page: " + page, e);
        }

        return result;
    }

    /**
     * Converts ARO API ProjectDetail to ProjectModel with raw API metadata.
     * This method includes all raw API data in the metadata for the CrawlRecord.
     *
     * @param detail  The typed project detail from the API
     * @param summary The project summary from the listing API
     *
     * @return ProjectConversionResult containing both ProjectModel and raw API metadata
     */
    private ProjectConversionResult convertToProjectModelWithMetadata(
          ProjectDetail detail,
          ProjectSummary summary
    ) {
        Map<String, Object> metadata = new HashMap<>();
        Map<String, Object> rawData = new HashMap<>();

        try {
            // Store raw API responses in raw_data
            rawData.put("project_summary", summary);
            rawData.put("project_detail", detail);

            // Get enriched data and store raw responses
            AroEnrichmentService.EnrichedProjectInfo enrichedInfo = null;
            try {
                enrichedInfo = enrichmentService.enrichProjectDetail(detail);
                rawData.put("enriched_info", enrichedInfo);
                rawData.put("amenities", enrichedInfo.getAmenities());
                rawData.put("unit_stats", enrichedInfo.getUnitStats());
                rawData.put("unit_templates", enrichedInfo.getUnitTemplates());
                rawData.put("buildings", enrichedInfo.getBuildings());
                rawData.put("payment_plan_response", enrichedInfo.getPaymentPlanResponse());
            } catch (Exception e) {
                log.warn(
                      "Failed to enrich project detail for {}: {}",
                      detail.getSlug(),
                      e.getMessage()
                );
                // Store empty lists as fallback when enrichment fails
                rawData.put("enriched_info", null);
                rawData.put("amenities", Collections.emptyList());
                rawData.put("unit_stats", Collections.emptyList());
                rawData.put("unit_templates", Collections.emptyList());
                rawData.put("buildings", Collections.emptyList());
                rawData.put("payment_plan_response", null);
                rawData.put("enrichment_error", e.getMessage());

                // Create a minimal enriched info with empty lists for processing
                enrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
                      .projectDetail(detail)
                      .amenities(Collections.emptyList())
                      .unitStats(Collections.emptyList())
                      .unitTemplates(Collections.emptyList())
                      .buildings(Collections.emptyList())
                      .paymentPlanResponse(null)
                      .build();
            }

            // Wrap raw data under raw_data key
            metadata.put("raw_data", rawData);

            // Add metadata about the conversion process
            metadata.put("conversion_timestamp", java.time.Instant.now().toString());
            metadata.put("source_api", AroCommon.SOURCE_URN);
            metadata.put("api_version", "v1");

            // Convert to ProjectModel using existing logic
            Optional<ProjectModel> projectModel = convertToProjectModel(detail, enrichedInfo);

            return new ProjectConversionResult(projectModel, metadata);

        } catch (Exception e) {
            log.error(
                  "Error converting project detail to ProjectModel with metadata: {}",
                  e.getMessage(),
                  e
            );
            metadata.put("raw_data", rawData);
            metadata.put("conversion_error", e.getMessage());
            metadata.put("conversion_error_timestamp", java.time.Instant.now().toString());
            return new ProjectConversionResult(Optional.empty(), metadata);
        }
    }

    /**
     * Converts ARO API ProjectDetail to ProjectModel.
     * This method uses the enrichment service to get all additional data in one call.
     *
     * @param detail       The typed project detail from the API
     * @param enrichedInfo Pre-fetched enriched project information
     *
     * @return Optional ProjectModel
     */
    private Optional<ProjectModel> convertToProjectModel(
          ProjectDetail detail,
          AroEnrichmentService.EnrichedProjectInfo enrichedInfo
    ) {
        try {
            // --- Basic identifiers & metadata --------------------------------------------------
            String developerName = detail.getDeveloper() != null ? detail.getDeveloper()
                  .getTitle() : "unknown-developer";
            String developerSlug = slugify(developerName);
            String developerUrn = AroCommon.Developer.urn(developerSlug);

            String projectSlug = detail.getSlug();
            String projectUrn = AroCommon.Project.urn(developerSlug, projectSlug);

            var builder = ProjectModel.builder();
            builder.withProjectUrn(projectUrn);
            builder.withDeveloperUrn(developerUrn);
            builder.withProjectSlug(projectSlug);
            builder.withSourceUrn(AroCommon.SOURCE_URN);
            builder.withTitle(detail.getTitle());
            builder.withDescription(detail.getDescription());
            builder.withCurrency("AED");

            // Set external ID
            builder.withExternalId(String.valueOf(detail.getId()));

            // --- Location --------------------------------------------------------------------
            if (detail.getAddress() != null && !detail.getAddress().isBlank()) {
                var locBuilder = LocationModel.builder()
                      .withAddress(detail.getAddress())
                      .withCity("Dubai")
                      .withState("Dubai")
                      .withCountry("AE");
                builder.withLocation(locBuilder.build());
            }

            // --- Images ----------------------------------------------------------------------
            List<ImageModel> images = new ArrayList<>();
            if (detail.getImages() != null) {
                for (String imgUrl : detail.getImages()) {
                    try {
                        ImageModel img = ImageModel.builder()
                              .withUrl(new java.net.URI(imgUrl))
                              .build();
                        images.add(img);
                    } catch (Exception ex) {
                        log.debug("Invalid image URI skipped: {}", imgUrl);
                    }
                }
            }
            if (!images.isEmpty()) {
                builder.withImages(images);
                builder.withCoverImage(images.get(0));
            }

            // --- Project status --------------------------------------------------------------
            if (detail.getHandoverDate() != null) {
                if (detail.getHandoverDate().isBefore(java.time.ZonedDateTime.now())) {
                    builder.withProjectStatus(AdModel.ConstructionStatusModel.FINISHED);
                } else {
                    builder.withProjectStatus(AdModel.ConstructionStatusModel.ACTIVE);
                }
            }

            // --- USE PROVIDED ENRICHED INFO ------------------------------------------------

            // --- Project Stats with enriched data -------------------------------------------
            ProjectStats.ProjectStatsBuilderBase statsBuilder = ProjectStats.builder();

            // Set completion date from handover date
            if (detail.getHandoverDate() != null) {
                String completionDateStr = detail.getHandoverDate().toLocalDate().toString();
                statsBuilder.withCompletionDate(completionDateStr);
            }

            // --- Property Types from Building data (as requested) ---------------------------
            List<AdModel.PropertyTypeModel> propertyTypes = extractPropertyTypesFromBuildings(
                  enrichedInfo.getBuildings());
            if (!propertyTypes.isEmpty()) {
                statsBuilder.withPropertyTypes(propertyTypes);
            } else {
                // Default fallback
                propertyTypes = List.of(AdModel.PropertyTypeModel.APARTMENT);
                statsBuilder.withPropertyTypes(propertyTypes);
            }

            // --- Unit stats & min price ---------------------------------------------------
            List<UnitStats> internalUnitStats = new ArrayList<>();
            if (enrichedInfo.getUnitTemplates() != null && !enrichedInfo.getUnitTemplates().isEmpty()) {
                // Calculate minimum price from templates
                double minPrice = enrichedInfo.getUnitTemplates()
                      .stream()
                      .filter(t -> t.getPriceFrom() != null && t.getPriceFrom().getAmount() != null)
                      .mapToDouble(t -> t.getPriceFrom().getAmount().doubleValue())
                      .min()
                      .orElse(Double.NaN);

                if (!Double.isNaN(minPrice)) {
                    PriceModel priceMinModel = PriceModel.builder()
                          .withValue(minPrice)
                          .withCurrency("AED")
                          .build();
                    statsBuilder.withPriceMin(priceMinModel);
                }

                // Create unit stats aggregated from templates
                internalUnitStats = createUnitStatsFromTemplates(
                        enrichedInfo.getUnitTemplates(),
                        propertyTypes);

            } else if (enrichedInfo.getUnitStats() != null && !enrichedInfo.getUnitStats().isEmpty()) {
                // Fallback to bedroom aggregates endpoint
                double minPrice = enrichedInfo.getUnitStats()
                      .stream()
                      .filter(us -> us.getPriceFrom() != null && us.getPriceFrom().getAmount() != null)
                      .mapToDouble(us -> us.getPriceFrom().getAmount().doubleValue())
                      .min()
                      .orElse(Double.NaN);

                if (!Double.isNaN(minPrice)) {
                    PriceModel priceMinModel = PriceModel.builder()
                          .withValue(minPrice)
                          .withCurrency("AED")
                          .build();
                    statsBuilder.withPriceMin(priceMinModel);
                }

                internalUnitStats = createUnitStatsWithPropertyTypes(
                        enrichedInfo.getUnitStats(),
                        propertyTypes);
            }

            if (!internalUnitStats.isEmpty()) {
                statsBuilder.withUnits(internalUnitStats);
            }

            builder.withProjectStats(statsBuilder.build());

            // --- Amenities -------------------------------------------------------------------
            if (enrichedInfo.getAmenities() != null && !enrichedInfo.getAmenities().isEmpty()) {
                List<AmenityModel> amenities = new ArrayList<>();
                for (com.realmond.temporal_service.crawler.uae.aro.model.Amenity am : enrichedInfo.getAmenities()) {
                    if (am.getTitle() != null && !am.getTitle().trim().isEmpty()) {
                        AmenityModel amenity = AmenityModel.builder()
                              .withLabel(am.getTitle())
                              .build();
                        amenities.add(amenity);
                    }
                }
                builder.withAmenities(amenities);
            }

            // --- Payment Plans ---------------------------------------------------------------
            if (enrichedInfo.getPaymentPlanResponse() != null) {
                Optional<PaymentPlanModel> paymentPlan = convertPaymentPlanResponse(
                      enrichedInfo.getPaymentPlanResponse(),
                      projectUrn
                );
                if (paymentPlan.isPresent()) {
                    builder.withPaymentPlans(List.of(paymentPlan.get()));
                    log.debug("Added payment plan for project: {}", projectSlug);
                } else {
                    log.debug("Payment plan conversion failed for project: {}", projectSlug);
                }
            }

            // --- Floor Plans (with de-duplication) -------------------------------------------
            Set<Integer> buildingFloorPlanIds = new HashSet<>();
            if (enrichedInfo.getBuildings() != null && !enrichedInfo.getBuildings().isEmpty()) {
                for (Building building : enrichedInfo.getBuildings()) {
                    buildingApiService.fetchFloorPlansForBuilding(building.getId())
                            .stream()
                            .map(com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate::getUnitTemplateId)
                            .forEach(buildingFloorPlanIds::add);
                }
                log.info("Found {} unique floor plan IDs across {} buildings for project {}",
                        buildingFloorPlanIds.size(), enrichedInfo.getBuildings().size(), projectSlug);
            }

            List<FloorPlanModel> projectFloorPlans = new ArrayList<>();
            if (enrichedInfo.getUnitTemplates() != null && !enrichedInfo.getUnitTemplates().isEmpty()) {
                long totalBeforeDedup = enrichedInfo.getUnitTemplates().size();
                enrichedInfo.getUnitTemplates().stream()
                        .filter(template -> !buildingFloorPlanIds.contains(template.getUnitTemplateId()))
                        .forEach(template -> aroFloorPlanConverter.convert(template, projectUrn, null)
                                .ifPresent(projectFloorPlans::add));
                log.info("Attached {} project-level floor plans to project {} ({} were duplicates of building floor plans)",
                        projectFloorPlans.size(), projectSlug, totalBeforeDedup - projectFloorPlans.size());
            }

            if (!projectFloorPlans.isEmpty()) {
                builder.withFloorPlans(projectFloorPlans);
            }

            // --- Brochure --------------------------------------------------------------------
            if (Boolean.TRUE.equals(detail.getHasBrochure())) {
                String brochureLink = settings.getBaseUrl() +
                      "/api/v2/projects/" +
                      detail.getId() +
                      "/download-brochure";
                try {
                    BrochureModel brochure = BrochureModel.builder()
                          .withUrl(new java.net.URI(brochureLink))
                          .build();
                    builder.withBrochures(List.of(brochure));
                } catch (Exception ex) {
                    log.debug("Invalid brochure URI for project {}", detail.getSlug());
                }
            }

            // --- URLs ------------------------------------------------------------------------
            try {
                java.net.URI pageUri = new java.net.URI(settings.getBaseUrl() +
                                                              "/project/" +
                                                              projectSlug +
                                                              "/");
                builder.withUrls(List.of(pageUri));
            } catch (Exception ignored) {
                log.debug("Could not create project URL for {}", projectSlug);
            }

            return Optional.of(builder.build());
        } catch (Exception e) {
            log.error("Error converting project detail to ProjectModel: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Converts ARO API PaymentPlanResponse to our domain model PaymentPlanModel.
     *
     * @param paymentPlanResponse The ARO API payment plan response
     * @param projectUrn          The project URN to associate with the payment plan
     * @return Optional PaymentPlanModel
     */
    private Optional<PaymentPlanModel> convertPaymentPlanResponse(
          PaymentPlanResponse paymentPlanResponse,
          String projectUrn
    ) {
        try {
            if (paymentPlanResponse == null ||
                paymentPlanResponse.getProject() == null ||
                paymentPlanResponse.getProject().getPaymentPlan() == null) {
                log.debug("Payment plan response or nested objects are null");
                return Optional.empty();
            }

            PaymentPlanResponse.PaymentPlan apiPaymentPlan = paymentPlanResponse.getProject().getPaymentPlan();

            if (apiPaymentPlan.getFields() == null || apiPaymentPlan.getFields().isEmpty()) {
                log.debug("Payment plan has no fields");
                return Optional.empty();
            }

            // Build payment steps from API fields
            List<PaymentStep> paymentSteps = new ArrayList<>();
            for (PaymentPlanResponse.PaymentField field : apiPaymentPlan.getFields()) {
                if (field.getValue() != null && field.getValue() > 0) {
                    PaymentStep step = convertPaymentField(field);
                    paymentSteps.add(step);
                }
            }

            if (paymentSteps.isEmpty()) {
                log.debug("No valid payment steps found");
                return Optional.empty();
            }

            // Create payment plan name - use title if available, otherwise generate one
            String planName = apiPaymentPlan.getTitle();
            if (planName == null || planName.trim().isEmpty()) {
                planName = "Payment Plan";
            }

            PaymentPlanModel paymentPlan = PaymentPlanModel.builder()
                  .withName(planName)
                  .withProjectUrn(projectUrn)
                  .withPaymentSteps(paymentSteps)
                  .build();

            log.debug("Successfully converted payment plan with {} steps", paymentSteps.size());
            return Optional.of(paymentPlan);

        } catch (Exception e) {
            log.error("Error converting payment plan response: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Converts a single ARO API PaymentField to our PaymentStep domain model.
     *
     * @param field The ARO API payment field
     * @return PaymentStep
     */
    private PaymentStep convertPaymentField(PaymentPlanResponse.PaymentField field) {
        String fieldTitle = field.getTitle() != null ? field.getTitle().trim() : "";
        String fieldDescription = field.getDescription() != null ? field.getDescription().trim() : "";

        // Determine frequency based on field title/description
        PaymentStep.Frequency frequency = determineFrequency(fieldTitle, fieldDescription);

        // Determine stage based on field title/description
        PaymentStep.Stage stage = determineStage(fieldTitle, fieldDescription);

        return PaymentStep.builder()
              .withName(fieldTitle.isEmpty() ? "Payment Step" : fieldTitle)
              .withFrequency(frequency)
              .withStage(stage)
              .withPercentage(field.getValue().doubleValue())
              .withDescription(fieldDescription.isEmpty() ? null : fieldDescription)
              .build();
    }

    /**
     * Determines the payment frequency based on field title and description.
     * Updated based on real ARO API data analysis.
     *
     * @param title       Payment field title
     * @param description Payment field description
     * @return PaymentStep.Frequency
     */
    private PaymentStep.Frequency determineFrequency(String title, String description) {
        String combined = (title + " " + description).toLowerCase();

        // Based on real API data: most payments are SINGLE, not recurring
        // Only specific patterns indicate recurring payments
        if (combined.contains("per month") || combined.contains("monthly payment")) {
            return PaymentStep.Frequency.MONTHLY;
        } else if (combined.contains("quarterly")) {
            return PaymentStep.Frequency.QUARTERLY;
        } else if (combined.contains("biweekly")) {
            return PaymentStep.Frequency.BIWEEKLY;
        } else if (combined.contains("bimonthly")) {
            return PaymentStep.Frequency.BIMONTHLY;
        } else if (combined.contains("weekly")) {
            return PaymentStep.Frequency.WEEKLY;
        } else {
            // Default to SINGLE for all standard patterns:
            // - "Down Payment", "On Handover", "During Construction"
            // - "1st Installment", "2nd Installment", etc.
            // - "Within X days/months", "Upon X% Completion"
            return PaymentStep.Frequency.SINGLE;
        }
    }

    /**
     * Determines the payment stage based on field title and description.
     * Updated based on real ARO API data patterns.
     *
     * @param title       Payment field title
     * @param description Payment field description
     * @return PaymentStep.Stage or null if cannot be determined
     */
    private PaymentStep.Stage determineStage(String title, String description) {
        String combined = (title + " " + description).toLowerCase();

        // Real API patterns - check SPECIFIC terms first to avoid false matches
        
        // POST_HANDOVER patterns (most specific)
        if (combined.contains("after handover") || combined.contains("post handover") ||
            combined.contains("over 5 years") || combined.contains("years after")) {
            return PaymentStep.Stage.POST_HANDOVER;
        }
        
        // ON_SIGNING_SPA patterns (specific)
        else if (combined.contains("signing") || combined.contains("spa") ||
                 combined.contains("sale purchase agreement")) {
            return PaymentStep.Stage.ON_SIGNING_SPA;
        }
        
        // ON_BOOKING patterns - check for short-term payments and down payments FIRST
        else if (combined.contains("down payment") || 
                 (combined.contains("booking") && !combined.contains("from booking")) ||
                 (combined.contains("within") && combined.contains("days") && combined.contains("from booking"))) {
            return PaymentStep.Stage.ON_BOOKING;
        }
        
        // POST_BOOKING patterns - check BEFORE general completion/booking checks
        else if (combined.contains("during construction") || 
                 (combined.contains("construction completion") && !combined.contains("100%")) ||
                 (combined.contains("% construction") && !combined.contains("100%")) ||
                 (combined.contains("upon") && combined.contains("completion") && !combined.contains("100%")) ||
                 combined.contains("installment") || combined.contains("instalment") ||
                 (combined.contains("within") && combined.contains("months") && combined.contains("from booking")) ||
                 combined.contains("progress")) {
            return PaymentStep.Stage.POST_BOOKING;
        }
        
        // ON_HANDOVER patterns - check for specific completion patterns
        else if (combined.contains("on handover") || 
                 (combined.contains("handover") && !combined.contains("after")) ||
                 combined.contains("on completion") || 
                 (combined.contains("upon completion") && combined.contains("100%")) ||
                 combined.contains("100% construction completion") || 
                 combined.contains("upon 100%")) {
            return PaymentStep.Stage.ON_HANDOVER;
        }
        
        else {
            // Return null for undetermined stages - schema allows this
            return null;
        }
    }

    /**
     * Extracts property types from building data using the specified mapping.
     *
     * @param buildings List of buildings from the API
     *
     * @return List of mapped property types
     */
    private List<AdModel.PropertyTypeModel> extractPropertyTypesFromBuildings(List<Building> buildings) {
        Set<AdModel.PropertyTypeModel> propertyTypes = new HashSet<>();

        if (buildings != null && !buildings.isEmpty()) {
            for (Building building : buildings) {
                if (building.getCategory() != null && !building.getCategory().trim().isEmpty()) {
                    AdModel.PropertyTypeModel mappedType = AroCommon.PROPERTY_TYPE_MAPPING.get(building.getCategory());
                    if (mappedType != null) {
                        propertyTypes.add(mappedType);
                        log.debug(
                              "Mapped building category '{}' to property type '{}'",
                              building.getCategory(),
                              mappedType
                        );
                    } else {
                        log.debug(
                              "Unknown building category '{}', using residential fallback",
                              building.getCategory()
                        );
                        propertyTypes.add(AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK);
                    }
                }
            }
        }

        return new ArrayList<>(propertyTypes);
    }

    /**
     * Creates unit stats with appropriate property types based on building data.
     * Logic:
     * - If single property type: use it straight
     * - If multiple property types: duplicate stats for each residential type
     * - If no property types: use apartment as fallback
     *
     * @param apiUnitStats            Unit stats from ARO API
     * @param discoveredPropertyTypes Property types discovered from buildings
     *
     * @return List of internal unit stats with proper property types
     */
    private List<UnitStats> createUnitStatsWithPropertyTypes(
          List<com.realmond.temporal_service.crawler.uae.aro.model.UnitStats> apiUnitStats,
          List<AdModel.PropertyTypeModel> discoveredPropertyTypes
    ) {

        List<UnitStats> internalUnitStats = new ArrayList<>();

        if (apiUnitStats == null || apiUnitStats.isEmpty()) {
            return internalUnitStats;
        }

        // Determine which property types to use for unit stats
        List<AdModel.PropertyTypeModel> applicablePropertyTypes = getResidentialPropertyTypes(
              discoveredPropertyTypes);

        // If no residential types found, default to apartment
        if (applicablePropertyTypes.isEmpty()) {
            applicablePropertyTypes = List.of(AdModel.PropertyTypeModel.APARTMENT);
            log.debug("No residential property types found, defaulting to APARTMENT for unit stats");
        }

        // Create unit stats for each applicable property type
        for (com.realmond.temporal_service.crawler.uae.aro.model.UnitStats us : apiUnitStats) {
            for (AdModel.PropertyTypeModel propertyType : applicablePropertyTypes) {
                var usb = UnitStats.builder()
                      .withBedrooms(us.getBedrooms() != null ? us.getBedrooms()
                            .doubleValue() : null)
                      .withPropertyType(propertyType);

                if (us.getPriceFrom() != null && us.getPriceFrom().getAmount() != null) {
                    PriceModel unitPrice = PriceModel.builder()
                          .withValue(us.getPriceFrom().getAmount().doubleValue())
                          .withCurrency("AED")
                          .build();
                    usb.withPriceMin(unitPrice);
                }

                if (us.getSizeFrom() != null && us.getSizeFrom().getValue() != null) {
                    // Convert sqft to sqm (1 sqft = 0.092903 sqm)
                    double areaSqm = us.getSizeFrom().getValue() * 0.092903;
                    usb.withAreaRangeSqm(areaSqm);
                }

                internalUnitStats.add(usb.build());

                log.debug(
                      "Created unit stat for {} bedrooms with property type {}",
                      us.getBedrooms(),
                      propertyType
                );
            }
        }

        return internalUnitStats;
    }

    /**
     * Filters property types to only include residential types suitable for unit stats.
     *
     * @param propertyTypes All discovered property types
     *
     * @return Only residential property types
     */
    private List<AdModel.PropertyTypeModel> getResidentialPropertyTypes(List<AdModel.PropertyTypeModel> propertyTypes) {
        Set<AdModel.PropertyTypeModel> residentialTypes = Set.of(
              AdModel.PropertyTypeModel.APARTMENT,
              AdModel.PropertyTypeModel.VILLA,
              AdModel.PropertyTypeModel.TOWNHOUSE,
              AdModel.PropertyTypeModel.DUPLEX,
              AdModel.PropertyTypeModel.TRIPLEX,
              AdModel.PropertyTypeModel.PENTHOUSE,
              AdModel.PropertyTypeModel.HOTEL_APARTMENT,
              AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK
        );

        return propertyTypes.stream()
              .filter(residentialTypes::contains)
              .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * Converts a string into a URL friendly slug.
     */
    private String slugify(String input) {
        if (input == null) {
            return "";
        }
        String slug = input.toLowerCase();
        slug = slug.replaceAll("\\s+", "-");
        slug = slug.replaceAll("[^a-z0-9-]", "");
        slug = slug.replaceAll("-+", "-");
        slug = slug.replaceAll("^-|-$", "");
        return slug;
    }

    /**
     * Wraps a ProjectModel in a CrawlRecord.
     *
     * @param model        The project model to wrap
     * @param syncMetadata Additional metadata for the crawl record
     *
     * @return The wrapped CrawlRecord
     */
    private static CrawlRecord<ProjectModel> wrapProjectModelInCrawlRecord(
          ProjectModel model,
          Map<String, Object> syncMetadata
    ) {
        return new CrawlRecord<>(model, syncMetadata, model.getSourceUrn(), model.getProjectUrn());
    }

    /**
     * Aggregates raw unit-template rows from ARO API into our bedroom-bucket UnitStats representation.
     * <p>
     * Steps:
     * 1.  Group templates by {@code bedroom} count (studio = 0).
     * 2.  For every group compute:
     *      – price_min  / price_max  → AED values (min & max of {@code price_from}).
     *      – area_min_sqm / area_max_sqm → min & max of {@code area.from} and {@code area.to} converted from sqft ➝ sqm.
     *      – bathrooms  → statistical mode across templates (null when absent).
     *      – floor_from / floor_to → global min & max across templates.
     *      – availability_count  → sum of {@code availabilities}.
     *      – media → first five distinct media URLs across templates converted to ImageModel.
     * 3.  Build one UnitStats object per (bedroom, propertyType) bucket, duplicating buckets
     *     when multiple residential property types were discovered for the project.
     * 4.  The deprecated field {@code area_range_sqm} is filled with {@code area_min_sqm} for
     *     backward compatibility.
     * <p>
     * If the caller did not pass any residential property type we default to APARTMENT.
     *
     * @param templates               raw unit-template rows from ARO API
     * @param discoveredPropertyTypes residential property types found via building data
     * @return aggregated UnitStats list ready for ProjectStats
     */
    private List<UnitStats> createUnitStatsFromTemplates(
            List<com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate> templates,
            List<AdModel.PropertyTypeModel> discoveredPropertyTypes) {

        List<UnitStats> result = new ArrayList<>();
        if (templates == null || templates.isEmpty()) {
            return result;
        }

        // Group templates by bedroom count
        Map<Integer, List<com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate>> byBedrooms = new java.util.HashMap<>();
        for (var t : templates) {
            Integer beds = t.getBedroom();
            if (beds == null) {
                // Skip templates without bedroom info
                continue;
            }
            byBedrooms.computeIfAbsent(beds, k -> new ArrayList<>()).add(t);
        }

        // Determine applicable property types (residential only)
        List<AdModel.PropertyTypeModel> applicableTypes = getResidentialPropertyTypes(discoveredPropertyTypes);
        if (applicableTypes.isEmpty()) {
            applicableTypes = List.of(AdModel.PropertyTypeModel.APARTMENT);
        }

        for (Map.Entry<Integer, List<com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate>> entry : byBedrooms.entrySet()) {
            int bedrooms = entry.getKey();
            List<com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate> group = entry.getValue();

            // Aggregations
            double priceMin = group.stream()
                    .filter(t -> t.getPriceFrom() != null && t.getPriceFrom().getAmount() != null)
                    .mapToDouble(t -> t.getPriceFrom().getAmount().doubleValue())
                    .min().orElse(Double.NaN);
            double priceMax = group.stream()
                    .filter(t -> t.getPriceFrom() != null && t.getPriceFrom().getAmount() != null)
                    .mapToDouble(t -> t.getPriceFrom().getAmount().doubleValue())
                    .max().orElse(Double.NaN);

            // area in sqm conversion
            double areaMinSqm = group.stream()
                    .filter(t -> t.getArea() != null && t.getArea().getFrom() != null && t.getArea().getFrom().getValue() != null)
                    .mapToDouble(t -> t.getArea().getFrom().getValue() * 0.092903)
                    .min().orElse(Double.NaN);
            double areaMaxSqm = group.stream()
                    .filter(t -> t.getArea() != null && t.getArea().getTo() != null && t.getArea().getTo().getValue() != null)
                    .mapToDouble(t -> t.getArea().getTo().getValue() * 0.092903)
                    .max().orElse(Double.NaN);

            // bathrooms mode
            java.util.Map<Integer, Long> bathCounts = group.stream()
                    .filter(t -> t.getBathroom() != null)
                    .collect(java.util.stream.Collectors.groupingBy(com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate::getBathroom, java.util.stream.Collectors.counting()));
            Double bathroomsMode = bathCounts.entrySet().stream()
                    .max(java.util.Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .map(Integer::doubleValue)
                    .orElse(null);

            // floors
            Integer floorFrom = group.stream()
                    .filter(t -> t.getFloor() != null && t.getFloor().getFrom() != null)
                    .map(t -> t.getFloor().getFrom())
                    .min(Integer::compareTo)
                    .orElse(null);
            Integer floorTo = group.stream()
                    .filter(t -> t.getFloor() != null && t.getFloor().getTo() != null)
                    .map(t -> t.getFloor().getTo())
                    .max(Integer::compareTo)
                    .orElse(null);

            // availability
            int availabilitySum = group.stream()
                    .filter(t -> t.getAvailabilities() != null)
                    .mapToInt(com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate::getAvailabilities)
                    .sum();

            // Collect media (max 5 distinct URLs)
            List<ImageModel> mediaImages = new ArrayList<>();
            group.stream()
                    .flatMap(t -> t.getMedia() != null ? t.getMedia().stream() : java.util.stream.Stream.empty())
                    .distinct()
                    .limit(5)
                    .forEach(url -> {
                        try {
                            mediaImages.add(ImageModel.builder().withUrl(new java.net.URI(url)).build());
                        } catch (Exception ignored) {
                        }
                    });

            // Build UnitStats for each applicable property type
            for (AdModel.PropertyTypeModel propertyType : applicableTypes) {
                var usb = UnitStats.builder()
                        .withPropertyType(propertyType)
                        .withBedrooms((double) bedrooms);

                if (!Double.isNaN(priceMin)) {
                    usb.withPriceMin(PriceModel.builder().withValue(priceMin).withCurrency("AED").build());
                }
                if (!Double.isNaN(priceMax) && priceMax > priceMin) {
                    usb.withPriceMax(PriceModel.builder().withValue(priceMax).withCurrency("AED").build());
                }
                if (!Double.isNaN(areaMinSqm)) {
                    usb.withAreaMinSqm(areaMinSqm);
                    // fill deprecated field for compatibility
                    usb.withAreaRangeSqm(areaMinSqm);
                }
                if (!Double.isNaN(areaMaxSqm) && areaMaxSqm > areaMinSqm) {
                    usb.withAreaMaxSqm(areaMaxSqm);
                }
                if (bathroomsMode != null) {
                    usb.withBathrooms(bathroomsMode);
                }
                if (floorFrom != null) {
                    usb.withFloorFrom(floorFrom);
                }
                if (floorTo != null && (floorFrom == null || floorTo > floorFrom)) {
                    usb.withFloorTo(floorTo);
                }
                if (availabilitySum > 0) {
                    usb.withAvailabilityCount(availabilitySum);
                }
                if (!mediaImages.isEmpty()) {
                    usb.withMedia(mediaImages);
                }

                result.add(usb.build());
            }
        }

        return result;
    }
}
