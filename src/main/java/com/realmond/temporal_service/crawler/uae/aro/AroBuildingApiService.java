package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.BuildingModel;
import com.realmond.etl.model.CoordinatesModel;
import com.realmond.etl.model.Geometry;
import com.realmond.etl.model.common.FloorPlanModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.AroCommon;
import com.realmond.temporal_service.crawler.uae.aro.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service that converts ARO "building" API payloads into {@link BuildingModel}s and wraps them in
 * {@link CrawlRecord}s for persistence.  It follows the same pagination strategy used by
 * {@link AroProjectCrawler}: the external page index corresponds to the underlying project listing
 * pages.  Each project page is fetched, then every project on that page is inspected to collect its
 * buildings.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AroBuildingApiService {

    private final AroFeignRestClient aroClient;
    private final AroSettings settings;
    private final AroFloorPlanConverter aroFloorPlanConverter;

    /**
     * Mapping between building.category values and the internal PropertyType enum used elsewhere
     * in the ETL pipeline.  Duplicated from {@link AroApiService} because the enum is required
     * both at project and building granularity.
     */
    private static final java.util.Map<String, com.realmond.etl.model.AdModel.PropertyTypeModel> CATEGORY_TO_PROPERTY_TYPE =
            java.util.Map.of(
                    "Apartments", com.realmond.etl.model.AdModel.PropertyTypeModel.APARTMENT,
                    "Townhouse", com.realmond.etl.model.AdModel.PropertyTypeModel.TOWNHOUSE,
                    "Villa", com.realmond.etl.model.AdModel.PropertyTypeModel.VILLA,
                    "Residential", com.realmond.etl.model.AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK,
                    "Non-Residential", com.realmond.etl.model.AdModel.PropertyTypeModel.COMMERCIAL_FALLBACK
            );

    private static final String DELIMITER = "|";

    /**
     * Encodes minimal project context into a single string:  id|slug|developerSlug
     */
    private String encodeProjectIdentifier(ProjectSummary ps) {
        String developerSlug = slugify(ps.getDeveloper() != null ? ps.getDeveloper().getTitle() : "unknown-developer");
        return ps.getId() + DELIMITER + ps.getSlug() + DELIMITER + developerSlug;
    }

    /** Decodes the identifier back to its parts. */
    private record DecodedProject(int id, String slug, String developerSlug) {}

    private DecodedProject decodeProjectIdentifier(String encoded) {
        String[] parts = encoded.split("\\" + DELIMITER, 3);
        if (parts.length != 3) throw new IllegalArgumentException("Invalid project identifier: " + encoded);
        return new DecodedProject(Integer.parseInt(parts[0]), parts[1], parts[2]);
    }

    /**
     * Enumerates all projects and returns encoded identifiers that carry enough information for the building phase.
     */
    public List<String> fetchAllProjectPageIds() {
        List<String> ids = new ArrayList<>();
        int page = 1;
        while (true) {
            AroApiResponse<ProjectSummary> rsp;
            try {
                rsp = aroClient.getProjects(page, settings.getPageSize());
            } catch (Exception ex) {
                log.error("Failed to fetch projects page {}: {}", page, ex.getMessage());
                throw new RetryableCrawlerException("failed to enumerate projects", ex);
            }

            if (rsp == null || rsp.getData() == null || rsp.getData().isEmpty()) {
                break;
            }
            rsp.getData().forEach(p -> ids.add(encodeProjectIdentifier(p)));

            // stop condition
            if (rsp.getPaging() != null && rsp.getPaging().getNumber() != null && rsp.getPaging().getTotal() != null) {
                if (page >= rsp.getPaging().getTotal()) break;
            } else if (rsp.getData().size() < settings.getPageSize()) {
                break;
            }
            page++;
        }
        log.info("Enumerated {} projects for building crawl", ids.size());
        return ids;
    }

    /**
     * Processes a single encoded project identifier.
     */
    public List<CrawlRecord<BuildingModel>> fetchBuildingsForProject(String encodedProjectId) {
        DecodedProject dp = decodeProjectIdentifier(encodedProjectId);
        return processProject(dp.id(), dp.slug(), dp.developerSlug());
    }

    /**
     * Fetches all floor plan unit templates for a given building ID, handling API pagination.
     *
     * @param buildingId The ARO-specific ID for the building.
     * @return A list of all {@link UnitTemplate} objects associated with the building.
     * @throws RetryableCrawlerException if the API call fails.
     */
    public List<UnitTemplate> fetchFloorPlansForBuilding(int buildingId) {
        List<UnitTemplate> allTemplates = new ArrayList<>();
        int page = 1;
        while (true) {
            AroApiResponse<UnitTemplate> response;
            try {
                log.debug("Fetching floor plans for building {} - page {}", buildingId, page);
                response = aroClient.getBuildingMapUnitTemplates(buildingId, page, settings.getPageSize());
            } catch (Exception e) {
                log.error("Failed to fetch floor plans for building {} on page {}: {}", buildingId, page, e.getMessage());
                throw new RetryableCrawlerException("API call failed for getBuildingMapUnitTemplates", e);
            }

            if (response == null || response.getData() == null || response.getData().isEmpty()) {
                log.info("No more floor plans found for building {}. Total found: {}", buildingId, allTemplates.size());
                break;
            }

            allTemplates.addAll(response.getData());

            AroApiResponse.Paging paging = response.getPaging();
            if (paging == null || paging.getNumber() == null || paging.getTotal() == null || page >= paging.getTotal()) {
                log.info("Finished fetching all pages of floor plans for building {}. Total found: {}", buildingId, allTemplates.size());
                break;
            }
            page++;
        }
        return allTemplates;
    }

    // ---------------------------------------------------------------------
    // Per-project processing
    // ---------------------------------------------------------------------

    private List<CrawlRecord<BuildingModel>> processProject(int projectId, String projectSlug, String developerSlug) {
        String developerUrn = AroCommon.Developer.urn(developerSlug);
        String projectUrn   = AroCommon.Project.urn(developerSlug, projectSlug);

        List<Building> buildings;
        try {
            buildings = aroClient.getProjectBuildings(projectId, "sqft");
        } catch (Exception ex) {
            log.warn("Failed to fetch buildings for project {}: {}", projectId, ex.getMessage());
            return List.of();
        }

        if (buildings == null || buildings.isEmpty()) {
            return List.of();
        }

        return buildings.stream()
                .map(b -> convertBuilding(projectId, projectUrn, b))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // ---------------------------------------------------------------------
    // Building conversion
    // ---------------------------------------------------------------------

    private CrawlRecord<BuildingModel> convertBuilding(int projectId, String projectUrn, Building building) {
        // Fetch detailed geometry (optional)
        BuildingDetail detail = null;
        try {
            detail = aroClient.getBuildingById(building.getId());
        } catch (Exception ex) {
            log.debug("Detailed building fetch failed for id {} – continuing with base data", building.getId());
        }

        Geometry geometry = buildGeometry(building, detail);

        String buildingUrn = AroCommon.Building.urn(building.getId());

        BuildingModel.BuildingModelBuilderBase<?> builder = BuildingModel.builder()
                .withBuildingUrn(buildingUrn)
                .withSourceUrn(AroCommon.SOURCE_URN)
                .withExternalId(String.valueOf(building.getId()))
                .withProjectUrn(projectUrn);

        if (building.getNumber() != null && !building.getNumber().isBlank()) {
            builder.withTitle(building.getNumber());
        }

        if (geometry != null) {
            builder.withGeometry(geometry);
        }

        // Attach floor plans
        List<UnitTemplate> templates = fetchFloorPlansForBuilding(building.getId());
        if (!templates.isEmpty()) {
            List<FloorPlanModel> floorPlans = new ArrayList<>();
            Set<Integer> floorPlanIds = new HashSet<>();

            for (UnitTemplate template : templates) {
                aroFloorPlanConverter.convert(template, projectUrn, buildingUrn)
                        .ifPresent(fp -> {
                            floorPlans.add(fp);
                            floorPlanIds.add(template.getUnitTemplateId());
                        });
            }

            if (!floorPlans.isEmpty()) {
                builder.withAdditionalProperty("floor_plans", floorPlans);
                builder.withAdditionalProperty("floorplan_ids", floorPlanIds);
            }
        }

        // Additional info (category, price etc.) saved as supplementary data
        builder.withAdditionalProperty("category", building.getCategory());
        builder.withAdditionalProperty("price_from", building.getPriceFrom());
        builder.withAdditionalProperty("availabilities_count", building.getAvailabilitiesCount());
        builder.withAdditionalProperty("height", building.getHeight());
        builder.withAdditionalProperty("target_height", building.getTargetHeight());
        builder.withAdditionalProperty("building_template_id", building.getBuildingTemplateId());
        if (detail != null) {
            builder.withAdditionalProperty("scale", detail.getScale());
            builder.withAdditionalProperty("rotation", detail.getRotation());
            builder.withAdditionalProperty("altitude", detail.getAltitude());
            builder.withAdditionalProperty("baked_urls", detail.getBakedUrls());
            builder.withAdditionalProperty("optimized_url", detail.getOptimizedUrl());
        }

        // ---------------------------------------------------------------
        // Property types derived from building category
        // ---------------------------------------------------------------
        if (building.getCategory() != null && !building.getCategory().isBlank()) {
            var mapped = CATEGORY_TO_PROPERTY_TYPE.get(building.getCategory());
            if (mapped != null) {
                builder.withPropertyTypes(List.of(mapped));
            }
        }

        BuildingModel model = builder.build();

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("project_id", projectId);
        metadata.put("building_id", building.getId());
        if (detail != null) {
            metadata.put("raw_building_detail", detail);
        }

        return new CrawlRecord<>(model, metadata, model.getSourceUrn(), model.getBuildingUrn());
    }

    // ---------------------------------------------------------------------
    // Geometry helpers
    // ---------------------------------------------------------------------

    private Geometry buildGeometry(Building building, BuildingDetail detail) {
        try {
            CoordinatesModel centroid = null;
            // Prefer detailed center_position
            if (detail != null && detail.getCenterPosition() != null &&
                detail.getCenterPosition().getCoordinates() != null &&
                detail.getCenterPosition().getCoordinates().size() >= 2) {
                List<Double> coords = detail.getCenterPosition().getCoordinates();
                centroid = CoordinatesModel.builder()
                        .withLat(coords.get(1))
                        .withLng(coords.get(0))
                        .build();
            } else if (building.getPosition() != null && building.getPosition().getCoordinates() != null &&
                       building.getPosition().getCoordinates().size() >= 2) {
                List<Double> coords = building.getPosition().getCoordinates();
                centroid = CoordinatesModel.builder()
                        .withLat(coords.get(1))
                        .withLng(coords.get(0))
                        .build();
            }

            List<CoordinatesModel> polygon = null;
            if (detail != null && detail.getGeobox() != null && detail.getGeobox().getCoordinates() != null &&
                !detail.getGeobox().getCoordinates().isEmpty()) {
                // geobox.coordinates[0] is the outer ring
                List<List<Double>> ring = detail.getGeobox().getCoordinates().get(0);
                polygon = ring.stream()
                        .filter(l -> l.size() >= 2)
                        .map(l -> CoordinatesModel.builder()
                                .withLat(l.get(1))
                                .withLng(l.get(0))
                                .build())
                        .collect(Collectors.toList());
            }

            if (centroid == null && (polygon == null || polygon.isEmpty())) {
                return null; // insufficient data
            }

            Geometry.GeometryBuilderBase<?> gb = Geometry.builder();
            if (centroid != null) gb.withCentroid(centroid);
            if (polygon != null && !polygon.isEmpty()) gb.withPolygon(polygon);
            return (Geometry) gb.build();
        } catch (Exception ex) {
            log.debug("Geometry build failed for building {}: {}", building.getId(), ex.getMessage());
            return null;
        }
    }

    // ---------------------------------------------------------------------
    // Utils
    // ---------------------------------------------------------------------

    private String slugify(String input) {
        return input.toLowerCase(Locale.ROOT)
                .replaceAll("[^a-z0-9]+", "-")
                .replaceAll("(^-|-$)", "");
    }
}
