package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.BuildingModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Crawler responsible for fetching and parsing individual building data from ARO.ae API.
 * <p>
 * This follows the same pagination-first approach as {@link AroProjectCrawler}, but delegates
 * the heavy lifting to {@link AroBuildingApiService} which converts each API response into a
 * {@link BuildingModel} wrapped in {@link CrawlRecord} for downstream ETL processing.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroBuildingCrawler implements Crawler<BuildingModel> {

    private final AroBuildingApiService buildingApiService;

    @Override
    public String getSourceUrn() {
        return AroCommon.SOURCE_URN;
    }

    @Override
    public Boolean supportsPagination() {
        return true;
    }

    @Override
    public List<String> fetchAllPageIds() {
        return buildingApiService.fetchAllProjectPageIds();
    }

    @Override
    public List<CrawlRecord<BuildingModel>> parsePage(String pageNum) {
        log.info("Parsing ARO.ae building page with ID: {}", pageNum);
        return buildingApiService.fetchBuildingsForProject(pageNum);
    }

    @Override
    public List<CrawlRecord<BuildingModel>> fetchAll() {
        throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
    }
} 