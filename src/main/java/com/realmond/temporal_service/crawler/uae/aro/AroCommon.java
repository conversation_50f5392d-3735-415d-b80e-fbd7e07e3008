package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.AdModel;
import java.util.Map;

public final class AroCommon {
    public static final String SOURCE_URN = "urn:source:aro";

    public static final Map<String, AdModel.PropertyTypeModel> PROPERTY_TYPE_MAPPING = Map.of(
            "Apartments",
            AdModel.PropertyTypeModel.APARTMENT,
            "Non-Residential",
            AdModel.PropertyTypeModel.COMMERCIAL_FALLBACK,
            "Residential",
            AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK,
            "Townhouse",
            AdModel.PropertyTypeModel.TOWNHOUSE,
            "Villa",
            AdModel.PropertyTypeModel.VILLA
    );

    public static final class Developer {
        public static String urn(String developerSlug) {
            return SOURCE_URN + ":developer:" + developerSlug;
        }
    }

    public static final class Project {
        public static String urn(String developerSlug, String projectSlug) {
            return Developer.urn(developerSlug) + ":project:" + projectSlug;
        }
    }

    public static final class Building {
        public static String urn(int buildingId) {
            return SOURCE_URN + ":building:" + buildingId;
        }
    }

    public static final class FloorPlan {
        public static String urn(int templateId) {
            return SOURCE_URN + ":floorplan:" + templateId;
        }
    }
} 