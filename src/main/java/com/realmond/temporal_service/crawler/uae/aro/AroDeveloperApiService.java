package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.AroCommon;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Developer;
import com.realmond.temporal_service.crawler.uae.aro.model.DeveloperDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service for processing ARO.ae developer data using typed REST API responses.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroDeveloperApiService {

    private final AroFeignRestClient aroClient;
    private final AroSettings settings;

    /**
     * Result of developer conversion including both the DeveloperModel and raw API metadata.
     */
    private record DeveloperConversionResult(Optional<DeveloperModel> developerModel,
                                            Map<String, Object> rawApiMetadata) {
    }

    /**
     * Fetches the total number of developer pages from ARO.ae using the API.
     *
     * @return The total number of pages
     */
    public int fetchDeveloperPagesCount() {
        log.info("Fetching ARO.ae developer pages count");
        try {
            AroApiResponse<Developer> response = aroClient.getDevelopers(1, settings.getPageSize());
            if (response.getPaging() != null) {
                int totalPages = response.getPaging().getTotal();
                log.info("Found {} total developer pages", totalPages);
                return totalPages;
            } else {
                log.warn("No pagination info in developer response, defaulting to 1 page");
                return 1;
            }
        } catch (Exception e) {
            log.error("Error fetching developer pages count: {}", e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching developer pages count", e);
        }
    }

    /**
     * Fetches and processes a single page of developers from ARO.ae.
     *
     * @param page The page number (1-based)
     * @return List of CrawlRecord containing DeveloperModel and metadata
     */
    public List<CrawlRecord<DeveloperModel>> fetchDevelopersPage(int page) {
        log.info("Fetching ARO.ae developer page: {}", page);
        List<CrawlRecord<DeveloperModel>> result = new ArrayList<>();

        try {
            // Fetch the developers page using typed API
            AroApiResponse<Developer> response = aroClient.getDevelopers(
                    page,
                    settings.getPageSize()
            );

            if (response.getData() == null || response.getData().isEmpty()) {
                log.warn("No developers found on page {}", page);
                return result;
            }

            log.info("Found {} developers on page {}", response.getData().size(), page);

            // Process each developer on the page
            for (Developer developerSummary : response.getData()) {
                try {
                    log.debug("Processing developer: {}", developerSummary.getTitle());

                    // Fetch detailed developer information
                    DeveloperDetail developerDetail = aroClient.getDeveloperById(developerSummary.getId());

                    // Convert to DeveloperModel with metadata
                    DeveloperConversionResult conversionResult = convertToDeveloperModelWithMetadata(
                            developerDetail, developerSummary);

                    if (conversionResult.developerModel().isPresent()) {
                        DeveloperModel developerModel = conversionResult.developerModel().get();
                        
                        // Create CrawlRecord with metadata
                        CrawlRecord<DeveloperModel> crawlRecord = new CrawlRecord<>(
                                developerModel,
                                conversionResult.rawApiMetadata(),
                                AroCommon.SOURCE_URN,
                                developerModel.getDeveloperUrn()
                        );
                        
                        result.add(crawlRecord);
                        log.debug("Successfully processed developer: {}", developerModel.getTitle());
                    } else {
                        log.warn("Failed to convert developer: {}", developerSummary.getTitle());
                    }

                } catch (Exception e) {
                    log.error("Error processing developer {}: {}", 
                            developerSummary.getTitle(), e.getMessage(), e);
                    // Continue processing other developers even if one fails
                }
            }

        } catch (Exception e) {
            log.error("Error fetching ARO.ae developer page {}: {}", page, e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching developer page: " + page, e);
        }

        return result;
    }

    /**
     * Converts ARO API DeveloperDetail to DeveloperModel with raw API metadata.
     * This method includes all raw API data in the metadata for the CrawlRecord.
     *
     * @param detail  The typed developer detail from the API
     * @param summary The developer summary from the listing API
     *
     * @return DeveloperConversionResult containing both DeveloperModel and raw API metadata
     */
    private DeveloperConversionResult convertToDeveloperModelWithMetadata(
            DeveloperDetail detail,
            Developer summary
    ) {
        Map<String, Object> metadata = new HashMap<>();
        Map<String, Object> rawData = new HashMap<>();

        try {
            // Store raw API responses in raw_data
            rawData.put("developer_summary", summary);
            rawData.put("developer_detail", detail);

            // Wrap raw data under raw_data key
            metadata.put("raw_data", rawData);

            // Add metadata about the conversion process
            metadata.put("conversion_timestamp", java.time.Instant.now().toString());
            metadata.put("source_api", "aro.ae");
            metadata.put("api_version", "v1");

            // Convert to DeveloperModel using existing logic
            Optional<DeveloperModel> developerModel = convertToDeveloperModel(detail);

            return new DeveloperConversionResult(developerModel, metadata);

        } catch (Exception e) {
            log.error(
                    "Error converting developer detail to DeveloperModel with metadata: {}",
                    e.getMessage(),
                    e
            );
            metadata.put("raw_data", rawData);
            metadata.put("conversion_error", e.getMessage());
            metadata.put("conversion_error_timestamp", java.time.Instant.now().toString());
            return new DeveloperConversionResult(Optional.empty(), metadata);
        }
    }

    /**
     * Converts ARO API DeveloperDetail to DeveloperModel.
     *
     * @param detail The typed developer detail from the API
     * @return Optional DeveloperModel
     */
    private Optional<DeveloperModel> convertToDeveloperModel(DeveloperDetail detail) {
        try {
            // --- Validate essential fields first -------------------------------------------
            if (detail.getId() == null) {
                log.warn("Developer detail has null ID, skipping conversion");
                return Optional.empty();
            }
            
            if (detail.getTitle() == null || detail.getTitle().isBlank()) {
                log.warn("Developer detail has null/blank title for ID {}, skipping conversion", detail.getId());
                return Optional.empty();
            }
            
            // --- Basic identifiers & metadata --------------------------------------------------
            String developerSlug = detail.getSlug() != null ? detail.getSlug() : slugify(detail.getTitle());
            String developerUrn = AroCommon.Developer.urn(developerSlug);

            var builder = DeveloperModel.builder();
            builder.withDeveloperUrn(developerUrn);
            builder.withSourceUrn(AroCommon.SOURCE_URN);
            builder.withTitle(detail.getTitle());
            builder.withExternalId(String.valueOf(detail.getId()));

            // --- Direct field mappings ----------------------------------------------------------
            if (detail.getDescription() != null && !detail.getDescription().isBlank()) {
                builder.withDescription(detail.getDescription());
            }

            if (detail.getLogo() != null && !detail.getLogo().isBlank()) {
                builder.withLogoUrl(detail.getLogo());
            }

            if (detail.getWebsiteUrl() != null && !detail.getWebsiteUrl().isBlank()) {
                builder.withWebsite(detail.getWebsiteUrl());
            }

            // --- Founded date (direct field) ---------------------------------------------------
            if (detail.getFounded() != null) {
                LocalDate foundedDate = detail.getFounded().toLocalDate();
                builder.withFounded(foundedDate.toString());
            }

            // Employee count and valuation will be stored in additional data
            // as direct builder methods may not exist

            // --- Government relation (direct field) ---------------------------------------------
            if (detail.getGovernmentRelation() != null && !detail.getGovernmentRelation().isBlank()) {
                // Map string to enum - need to find the correct enum values
                try {
                    builder.withGovernmentRelation(mapGovernmentRelation(detail.getGovernmentRelation()));
                } catch (Exception e) {
                    log.warn("Unable to map government relation '{}', storing in additional data", detail.getGovernmentRelation());
                    // Will be stored in additional data below
                }
            }

            // --- Location mapping from address --------------------------------------------------
            if (detail.getAddress() != null && !detail.getAddress().isBlank()) {
                var locationBuilder = com.realmond.etl.model.common.LocationModel.builder();
                locationBuilder.withAddress(detail.getAddress());
                
                // Default UAE location since ARO is UAE-focused
                locationBuilder.withCountry("AE");
                
                // Try to extract city from address (basic parsing)
                String address = detail.getAddress().toLowerCase();
                if (address.contains("dubai")) {
                    locationBuilder.withCity("Dubai");
                    locationBuilder.withState("Dubai");
                } else if (address.contains("abu dhabi")) {
                    locationBuilder.withCity("Abu Dhabi");
                    locationBuilder.withState("Abu Dhabi");
                } else if (address.contains("sharjah")) {
                    locationBuilder.withCity("Sharjah");
                    locationBuilder.withState("Sharjah");
                } else {
                    // Default to Dubai if no specific emirate detected
                    locationBuilder.withCity("Dubai");
                    locationBuilder.withState("Dubai");
                }
                
                builder.withLocation(locationBuilder.build());
            }

            // --- Developer stats mapping --------------------------------------------------------
            if (detail.getFinishedProjectsCount() != null || detail.getAvailableProjects() != null) {
                var statsBuilder = com.realmond.etl.model.DeveloperStats.builder();
                
                if (detail.getFinishedProjectsCount() != null) {
                    statsBuilder.withCompletedProjectsCount(detail.getFinishedProjectsCount());
                }
                
                if (detail.getAvailableProjects() != null) {
                    statsBuilder.withNotCompletedProjectsCount(detail.getAvailableProjects());
                }
                
                builder.withDeveloperStats(statsBuilder.build());
            }

            // --- Additional data (for truly additional fields) ----------------------------------
            Map<String, Object> additionalData = new HashMap<>();

            // Awards - complex structured data
            if (detail.getAwards() != null && !detail.getAwards().isEmpty()) {
                additionalData.put("awards", detail.getAwards());
            }

            // Flagship projects - complex structured data
            if (detail.getFlagshipProjects() != null && !detail.getFlagshipProjects().isEmpty()) {
                additionalData.put("flagship_projects", detail.getFlagshipProjects());
            }

            // Presence - list of locations/markets
            if (detail.getPresence() != null && !detail.getPresence().isEmpty()) {
                additionalData.put("presence", detail.getPresence());
            }

            // Include original slug for reference
            if (detail.getSlug() != null && !detail.getSlug().isBlank()) {
                additionalData.put("original_slug", detail.getSlug());
            }

            // Government relation if mapping failed
            if (detail.getGovernmentRelation() != null && !detail.getGovernmentRelation().isBlank()) {
                additionalData.put("government_relation_raw", detail.getGovernmentRelation());
            }

            // Add fields that might not have direct builder methods as fallback
            if (detail.getEmployeesCount() != null) {
                additionalData.put("employee_count", detail.getEmployeesCount());
            }

            if (detail.getValuation() != null && !detail.getValuation().isBlank()) {
                additionalData.put("valuation", detail.getValuation());
            }

            if (!additionalData.isEmpty()) {
                builder.withAdditionalData(additionalData);
            }

            DeveloperModel developerModel = builder.build();
            log.debug("Successfully converted developer: {}", developerModel.getTitle());
            return Optional.of(developerModel);

        } catch (Exception e) {
            log.error("Error converting developer detail to DeveloperModel: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Creates a URL-safe slug from a title string.
     *
     * @param title The title to convert
     * @return URL-safe slug
     */
    private String slugify(String title) {
        if (title == null || title.isBlank()) {
            return "unknown";
        }
        return title.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")  // Remove special characters
                .replaceAll("\\s+", "-")           // Replace spaces with hyphens
                .replaceAll("-+", "-")             // Replace multiple hyphens with single
                .replaceAll("^-|-$", "");          // Remove leading/trailing hyphens
    }

    /**
     * Maps government relation string to the appropriate enum value.
     *
     * @param governmentRelation The string value from ARO API
     * @return The mapped enum value
     * @throws IllegalArgumentException if the value cannot be mapped
     */
    private com.realmond.etl.model.DeveloperModel.GovernmentRelation mapGovernmentRelation(String governmentRelation) {
        if (governmentRelation == null || governmentRelation.isBlank()) {
            throw new IllegalArgumentException("Government relation cannot be null or blank");
        }
        
        String normalized = governmentRelation.toLowerCase().trim();
        return switch (normalized) {
            case "public" -> com.realmond.etl.model.DeveloperModel.GovernmentRelation.PUBLIC;
            case "private" -> com.realmond.etl.model.DeveloperModel.GovernmentRelation.PRIVATE;
            case "mixed" -> com.realmond.etl.model.DeveloperModel.GovernmentRelation.MIXED;
            default -> throw new IllegalArgumentException("Unknown government relation: " + governmentRelation);
        };
    }
} 