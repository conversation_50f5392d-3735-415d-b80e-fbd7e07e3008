package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.IntStream;

/**
 * Implementation of ARO Developer Crawler.
 * Uses existing ARO parser components to fetch and parse developer data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroDeveloperCrawler implements Crawler<DeveloperModel> {

    private final AroDeveloperApiService aroDeveloperApiService;

    @Override
    public String getSourceUrn() {
        return AroCommon.SOURCE_URN;
    }

    @Override
    public Boolean supportsPagination() {
        return true;
    }

    @Override
    public List<String> fetchAllPageIds() {
        int pagesCount = aroDeveloperApiService.fetchDeveloperPagesCount();
        return IntStream.range(1, pagesCount + 1).boxed().map(Object::toString).toList();
    }

    @Override
    public List<CrawlRecord<DeveloperModel>> parsePage(String pageNum) {
        log.info("Parsing ARO.ae developer page with ID: {}", pageNum);
        int page;
        try {
            page = Integer.parseInt(pageNum);
        } catch (NumberFormatException e) {
            throw new NonRetryableCrawlerException("failed to parse page number: " + pageNum, e);
        }

        return aroDeveloperApiService.fetchDevelopersPage(page);
    }

    @Override
    public List<CrawlRecord<DeveloperModel>> fetchAll() {
        throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
    }
} 