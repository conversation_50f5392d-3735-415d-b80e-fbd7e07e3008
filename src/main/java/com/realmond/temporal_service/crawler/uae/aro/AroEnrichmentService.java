package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.uae.aro.model.Amenity;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import com.realmond.temporal_service.crawler.uae.aro.model.PaymentPlanResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitStats;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for enriching project data with additional information from ARO.ae API.
 * Demonstrates usage of the Feign client for various endpoints.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AroEnrichmentService {

    private final AroFeignRestClient aroFeignRestClient;
    private final AroSettings settings;

    /**
     * Enriches project detail with amenities, unit statistics, buildings, and payment plans.
     * @param projectDetail The base project detail
     * @return Enriched project information
     */
    public EnrichedProjectInfo enrichProjectDetail(ProjectDetail projectDetail) {
        log.info("Enriching project detail for: {}", projectDetail.getSlug());

        // Fetch amenities
        List<Amenity> amenities = fetchProjectAmenities(projectDetail.getId());

        // Fetch unit statistics (bedroom aggregates)
        List<UnitStats> unitStats = fetchUnitStatistics(projectDetail.getId());

        // Fetch unit templates (rich per-template info)
        List<UnitTemplate> unitTemplates = fetchUnitTemplates(projectDetail.getId());

        // Fetch building details (measurement in sqft)
        List<Building> buildings = fetchBuildingDetails(projectDetail.getId());

        // Fetch payment plan
        PaymentPlanResponse paymentPlanResponse = fetchPaymentPlan(projectDetail.getId());

        // Build enriched info
        return EnrichedProjectInfo.builder()
                .projectDetail(projectDetail)
                .amenities(amenities)
                .unitStats(unitStats)
                .unitTemplates(unitTemplates)
                .buildings(buildings)
                .paymentPlanResponse(paymentPlanResponse)
                .build();
    }

    /**
     * Fetches amenities for a project.
     * @param projectId Project ID
     * @return List of amenities
     */
    public List<Amenity> fetchProjectAmenities(int projectId) {
        try {
            log.debug("Fetching amenities for project ID: {}", projectId);
            return aroFeignRestClient.getProjectAmenities(projectId);
        } catch (Exception e) {
            log.warn("Failed to fetch amenities for project {}: {}", projectId, e.getMessage());
            return List.of();
        }
    }

    /**
     * Fetches unit statistics for a project.
     * @param projectId Project ID
     * @return List of unit statistics
     */
    public List<UnitStats> fetchUnitStatistics(int projectId) {
        try {
            log.debug("Fetching unit statistics for project ID: {}", projectId);
            return aroFeignRestClient.getUnitStats(projectId, 1, 50); // Get all unit types
        } catch (Exception e) {
            log.warn("Failed to fetch unit statistics for project {}: {}", projectId, e.getMessage());
            return List.of();
        }
    }

    /**
     * Fetches unit templates (extended unit statistics) for a project.
     *
     * @param projectId Project ID
     * @return List of unit templates (can be empty when API returns none)
     */
    public List<UnitTemplate> fetchUnitTemplates(int projectId) {
        List<UnitTemplate> allTemplates = new ArrayList<>();
        int page = 1;
        while (true) {
            AroApiResponse<UnitTemplate> response;
            try {
                log.debug("Fetching project unit templates for project {} - page {}", projectId, page);
                response = aroFeignRestClient.getProjectUnitTemplates(projectId, page, settings.getPageSize());
            } catch (Exception e) {
                log.warn("Failed to fetch unit templates for project {} on page {}: {}", projectId, page, e.getMessage());
                // Unlike building-level, we don't rethrow here; an empty list is an acceptable fallback.
                return List.of();
            }

            if (response == null || response.getData() == null || response.getData().isEmpty()) {
                log.info("No more unit templates found for project {}. Total found: {}", projectId, allTemplates.size());
                break;
            }

            allTemplates.addAll(response.getData());

            AroApiResponse.Paging paging = response.getPaging();
            if (paging == null || paging.getNumber() == null || paging.getTotal() == null || page >= paging.getTotal()) {
                log.info("Finished fetching all pages of unit templates for project {}. Total found: {}", projectId, allTemplates.size());
                break;
            }
            page++;
        }
        return allTemplates;
    }

    /**
     * Fetches building details for a project.
     *
     * @param projectId Project ID
     * @return List of buildings (can be empty when API returns none)
     */
    public List<Building> fetchBuildingDetails(int projectId) {
        try {
            log.debug("Fetching building details for project ID: {}", projectId);
            // measurement type set to sqft by default as used in Postman collection
            return aroFeignRestClient.getProjectBuildings(projectId, "sqft");
        } catch (Exception e) {
            log.warn("Failed to fetch building details for project {}: {}", projectId, e.getMessage());
            return List.of();
        }
    }

    /**
     * Fetches payment plan for a project.
     *
     * @param projectId Project ID
     * @return Payment plan response (can be null if not available)
     */
    public PaymentPlanResponse fetchPaymentPlan(int projectId) {
        try {
            log.debug("Fetching payment plan for project ID: {}", projectId);
            return aroFeignRestClient.getProjectPaymentPlan(projectId);
        } catch (Exception e) {
            log.warn("Failed to fetch payment plan for project {}: {}", projectId, e.getMessage());
            return null;
        }
    }

    /**
     * Enriched project information containing all additional data fetched from ARO APIs.
     */
    @lombok.Data
    @lombok.Builder
    public static class EnrichedProjectInfo {
        private ProjectDetail projectDetail;
        private List<Amenity> amenities;
        private List<UnitStats> unitStats;
        private List<UnitTemplate> unitTemplates;
        private List<Building> buildings;
        private PaymentPlanResponse paymentPlanResponse;
    }
}
