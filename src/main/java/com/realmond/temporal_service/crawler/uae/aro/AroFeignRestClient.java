package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.uae.aro.model.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Feign client for ARO.ae REST API with typed responses.
 */
@FeignClient(
    name = "aro-api",
    url = "${crawler.uae.aro.base-url:https://aro.ae}",
    configuration = AroFeignConfiguration.class
)
public interface AroFeignRestClient {

    /**
     * Fetches paginated list of projects.
     * @param pageNumber Page number (1-based)
     * @param pageSize Number of items per page
     * @return Paginated response containing project summaries
     */
    @GetMapping("/api/v1/projects")
    AroApiResponse<ProjectSummary> getProjects(
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );

    /**
     * Fetches detailed project information by slug.
     * @param slug Project slug identifier
     * @return Detailed project information
     */
    @GetMapping("/api/v1/projects/{slug}")
    ProjectDetail getProjectBySlug(@PathVariable("slug") String slug);

    /**
     * Fetches amenities for a project.
     * @param projectId Project ID
     * @return List of amenities
     */
    @GetMapping("/api/v1/amenities")
    List<Amenity> getProjectAmenities(@RequestParam("projectId") int projectId);

    /**
     * Fetches unit statistics grouped by bedroom count.
     * @param projectId Project ID
     * @param pageNumber Page number (1-based)
     * @param pageSize Number of items per page
     * @return List of unit statistics
     */
    @GetMapping("/api/v1/listings/bedrooms")
    List<UnitStats> getUnitStats(
            @RequestParam("projectId") int projectId,
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );

    /**
     * Fetches building details for a project.
     *
     * @param projectId Project ID
     * @param smt Size measurement type, e.g. "sqft" or "sqm"
     * @return List of buildings with typed structure
     */
    @GetMapping("/api/v1/buildings")
    List<Building> getProjectBuildings(
            @RequestParam("projectId") int projectId,
            @RequestParam("smt") String smt
    );

    /**
     * Fetches paginated list of developers.
     * @param pageNumber Page number (1-based)
     * @param pageSize Number of items per page
     * @return Paginated response containing developer summaries
     */
    @GetMapping("/api/v1/developer")
    AroApiResponse<Developer> getDevelopers(
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );

    /**
     * Fetches detailed developer information by ID.
     * @param developerId Developer ID
     * @return Detailed developer information
     */
    @GetMapping("/api/v1/developer/{developerId}")
    DeveloperDetail getDeveloperById(@PathVariable("developerId") int developerId);

    /**
     * Fetches payment plan information for a project.
     * @param projectId Project ID
     * @return Payment plan information
     */
    @GetMapping("/api/v1/projects/{projectId}/payment-plan")
    PaymentPlanResponse getProjectPaymentPlan(@PathVariable("projectId") int projectId);

    /**
     * Fetches floor plans (unit templates) for a project.
     * @param projectId Project ID
     * @param pageNumber Page number (1-based)
     * @param pageSize Number of items per page
     * @return Paginated response containing unit templates
     */
    @GetMapping("/api/v1/listings/unit-templates")
    AroApiResponse<UnitTemplate> getProjectUnitTemplates(
            @RequestParam("projectId") int projectId,
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );

    /**
     * Fetches detailed information about a specific building by its ID.
     *
     * Endpoint example: GET /api/v1/buildings/178100
     *
     * @param buildingId Building ID
     * @return Detailed building information with typed structure
     */
    @GetMapping("/api/v1/buildings/{buildingId}")
    BuildingDetail getBuildingById(@PathVariable("buildingId") int buildingId);

    /**
     * Fetches geo-scoped floor-plan (unit template) information for a specific building.
     * <p>
     * Endpoint example: GET /api/v1/listings/map-unit-templates?buildingId=178100&pageNumber=1&pageSize=30
     *
     * @param buildingId Building ID
     * @param pageNumber Page number (1-based)
     * @param pageSize   Number of items per page
     * @return Paginated response containing unit templates scoped to the building
     */
    @GetMapping("/api/v1/listings/map-unit-templates")
    AroApiResponse<UnitTemplate> getBuildingMapUnitTemplates(
            @RequestParam("buildingId") int buildingId,
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );
}
