package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.AdModel;
import com.realmond.etl.model.ImageModel;
import com.realmond.etl.model.common.AreaModel;
import com.realmond.etl.model.common.FloorPlanModel;
import com.realmond.etl.model.common.LevelImage;
import com.realmond.temporal_service.crawler.common.UnitConversionUtils;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class AroFloorPlanConverter {

    /**
     * Converts a single ARO UnitTemplate (floor-plan payload) into a {@link FloorPlanModel}.
     * <p>
     * Mandatory fields are validated as per {@code spec.md}: if the template is missing a
     * primary image or an external ID, the method returns {@link Optional#empty()} so the caller
     * can safely ignore invalid templates.
     * </p>
     *
     * @param template    Source payload from ARO API (unit-template).
     * @param projectUrn  Parent project URN (never {@code null}).
     * @param buildingUrn Nullable building URN – populated only when the template originates
     *                    from the building-scoped endpoint.
     * @return Optional containing the converted {@link FloorPlanModel} or empty when the input
     *         does not meet minimum validity requirements.
     */
    public Optional<FloorPlanModel> convert(UnitTemplate template, String projectUrn, String buildingUrn) {
        if (template.getUnitTemplateId() == null) {
            return Optional.empty();
        }

        // As per spec, if media is absent or invalid, the floor plan is dropped.
        if (template.getMedia() == null || template.getMedia().isEmpty() || template.getMedia().get(0).isBlank()) {
            return Optional.empty();
        }

        Optional<ImageModel> mainImage = createImageModel(template.getMedia().get(0));
        if (mainImage.isEmpty()) {
            return Optional.empty(); // Drop if the primary image URL is invalid
        }

        var builder = FloorPlanModel.builder()
                .withExternalId(String.valueOf(template.getUnitTemplateId()))
                .withFloorplanUrn(AroCommon.FloorPlan.urn(template.getUnitTemplateId()))
                .withSourceUrn(AroCommon.SOURCE_URN)
                .withProjectUrn(projectUrn)
                .withBuildingUrn(buildingUrn)
                .withImage(mainImage.get());

        if (template.getBedroom() != null) {
            builder.withBedrooms(template.getBedroom().doubleValue());
        }

        // Media mapping
        if (template.getMedia().size() > 1) {
            List<LevelImage> levelImages = template.getMedia().stream()
                    .skip(1)
                    .map(this::createLevelImage)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
            if (!levelImages.isEmpty()) {
                builder.withLevelImages(levelImages);
            }
        }

        // Property Type Mapping
        if (template.getType() != null && !template.getType().isBlank()) {
            AdModel.PropertyTypeModel propertyType = AroCommon.PROPERTY_TYPE_MAPPING.getOrDefault(
                    template.getType(),
                    AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK
            );
            builder.withPropertyType(propertyType);
        }

        // Area Mapping
        if (template.getArea() != null) {
            mapArea(builder, template.getArea());
        }

        // Level and Title Mapping
        if (template.getFloor() != null) {
            mapLevelsAndTitle(builder, template.getFloor());
        }

        // Additional Data Mapping
        mapAdditionalData(builder, template);

        return Optional.of(builder.build());
    }

    private void mapArea(FloorPlanModel.FloorPlanModelBuilderBase<?> builder, UnitTemplate.AreaRange area) {
        Double fromSqft = (area.getFrom() != null) ? area.getFrom().getValue() : null;
        Double toSqft = (area.getTo() != null) ? area.getTo().getValue() : null;

        if (fromSqft == null && toSqft == null) {
            return;
        }

        double areaFromSqm = (fromSqft != null) ? UnitConversionUtils.sqftToSqm(fromSqft, 2) : 0;
        double areaToSqm = (toSqft != null) ? UnitConversionUtils.sqftToSqm(toSqft, 2) : 0;

        AreaModel.AreaModelBuilderBase<?> areaBuilder = AreaModel.builder();
        if (areaFromSqm > 0 && areaToSqm > 0 && Math.abs(areaFromSqm - areaToSqm) > 1e-9) {
            areaBuilder.withMinTotalAreaSqm(Math.min(areaFromSqm, areaToSqm));
            areaBuilder.withMaxTotalAreaSqm(Math.max(areaFromSqm, areaToSqm));
            areaBuilder.withTotalAreaSqm((areaFromSqm + areaToSqm) / 2); // Or some other logic for total
        } else {
            double totalArea = Math.max(areaFromSqm, areaToSqm);
            if (totalArea > 0) {
                areaBuilder.withTotalAreaSqm(totalArea);
            }
        }
        builder.withArea(areaBuilder.build());
    }

    private void mapLevelsAndTitle(FloorPlanModel.FloorPlanModelBuilderBase<?> builder, UnitTemplate.FloorRange floor) {
        Integer from = floor.getFrom();
        Integer to = floor.getTo();

        if (from == null || to == null) {
            return;
        }

        // Special case: "Townhouse" often has 0-0, meaning G+1, so 1 level is not quite right.
        // Spec: "if both zero -> levels=1"
        if (from == 0 && to == 0) {
            builder.withLevels(1);
            // No title for single level as per spec logic interpretation
        } else {
            int levels = Math.abs(to - from) + 1;
            builder.withLevels(levels);
            if (levels > 1) {
                builder.withTitle(String.format("F%d-%d", from, to));
            }
        }
    }

    private void mapAdditionalData(FloorPlanModel.FloorPlanModelBuilderBase<?> builder, UnitTemplate template) {
        if (template.getPriceFrom() != null && template.getPriceFrom().getAmount() != null) {
            builder.withAdditionalProperty("price_from_aed", template.getPriceFrom().getAmount().doubleValue());
            // Per spec, only store currency code if not AED (784 is ISO 4217 for AED)
            if (template.getPriceFrom().getCurrencyCode() != null && template.getPriceFrom().getCurrencyCode() != 784) {
                builder.withAdditionalProperty("price_from_currency_code", template.getPriceFrom().getCurrencyCode());
            }
        }
        if (template.getAvailabilities() != null) {
            builder.withAdditionalProperty("availability_count", template.getAvailabilities());
        }
        if (template.getBathroom() != null) {
            builder.withAdditionalProperty("bathrooms", template.getBathroom());
        }
        if (template.getFloor() != null) {
            if (template.getFloor().getFrom() != null) {
                builder.withAdditionalProperty("floor_from", template.getFloor().getFrom());
            }
            if (template.getFloor().getTo() != null) {
                builder.withAdditionalProperty("floor_to", template.getFloor().getTo());
            }
        }
    }

    private Optional<ImageModel> createImageModel(String url) {
        if (url == null || url.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(ImageModel.builder().withUrl(new URI(url)).build());
        } catch (URISyntaxException e) {
            return Optional.empty();
        }
    }

    private Optional<LevelImage> createLevelImage(String url) {
        return createImageModel(url)
                .map(image -> LevelImage.builder().withImage(image).build());
    }
} 