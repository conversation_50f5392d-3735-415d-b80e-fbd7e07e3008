package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.DefaultCrawlerSettings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Component
@ConfigurationProperties(prefix = "crawler.uae.aro")
public class AroSettings extends DefaultCrawlerSettings {

    /** Base URL for ARO REST API */
    private String baseUrl = "https://aro.ae";

    /** Default page size for listing endpoints */
    private int pageSize = 30;
} 