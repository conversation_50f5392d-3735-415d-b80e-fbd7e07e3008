package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.etl.model.AdModel;
import com.realmond.etl.model.AmenityModel;
import com.realmond.etl.model.CoordinatesModel;
import com.realmond.etl.model.ImageModel;
import com.realmond.etl.model.PriceModel;
import com.realmond.etl.model.ProjectModel;
import com.realmond.etl.model.ProjectStats;
import com.realmond.etl.model.UnitStats;
import com.realmond.etl.model.Video;
import com.realmond.etl.model.common.BrochureModel;
import com.realmond.etl.model.common.FloorPlanModel;
import com.realmond.etl.model.common.LocationModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.common.StringUtils;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.property_finder.model.NextJsWrapper;
import com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectDetails;
import com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectSummary;
import com.realmond.temporal_service.crawler.uae.property_finder.model.SearchResultPageProps;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Service layer orchestrating API interactions and model conversion for the
 * Property&nbsp; Finder crawler.
 *
 * <p>
 * All business logic will be implemented incrementally according to the tasks
 * defined in <code>backlog.md</code>. At this stage only the scaffolding exists to
 * satisfy compilation.
 * </p>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PropertyFinderApiService {

    private final PropertyFinderFeignRestClient feignClient;

    /**
     * Fetches a single paginated list of projects and converts them into
     * {@link ProjectModel}s wrapped in {@link CrawlRecord}s.
     *
     * @param page Page number starting from 1
     *
     * @return list of crawl records for the requested page
     *
     * @throws RetryableCrawlerException transient issues calling the remote API
     */
    public List<CrawlRecord<ProjectModel>> fetchProjectsPage(int page) {
        log.info("Fetching PropertyFinder page: {}", page);

        // Placeholder result – mapping will be added in later tasks.
        List<CrawlRecord<ProjectModel>> result = new ArrayList<>();

        try {
            // Call remote endpoint and unwrap project summaries
            var wrapper = feignClient.getNewProjects(page);

            List<ProjectSummary> projects = null;
            if (wrapper != null &&
                  wrapper.getProps() != null &&
                  wrapper.getProps().getPageProps() != null) {
                var searchResult = wrapper.getProps().getPageProps().getSearchResult();
                if (searchResult != null &&
                      searchResult.getData() != null &&
                      searchResult.getData().getProjects() != null) {
                    projects = searchResult.getData().getProjects().getDeveloper();
                }
            }

            if (projects == null || projects.isEmpty()) {
                log.warn("No projects found on page {}", page);
                return result; // empty list indicates end for caller
            }

            // Spec §17 – success logging of page enumeration
            log.info("Found {} projects on page {}", projects.size(), page);

            // Log each summary's developerSlug/projectSlug (parsed from shareUrl)
            for (ProjectSummary summary : projects) {
                String devSlug = null;
                String projectSlug = null;
                if (summary != null && summary.getShareUrl() != null) {
                    String share = summary.getShareUrl(); // e.g. /en/new-projects/emaar-properties/albero
                    String[] parts = share.split("/");
                    // ["", "en", "new-projects", "{developerSlug}", "{projectSlug}"] or with leading slash maybe 5 parts
                    // Actually from sample: /en/new-projects/emaar-properties/albero -> parts[0]="", 1="en", 2="new-projects", 3="emaar-properties", 4="albero"
                    if (parts.length >= 5) {
                        devSlug = parts[3];
                        projectSlug = parts[4];
                    }
                }
                log.info("Discovered PropertyFinder project summary: {}/{}", devSlug, projectSlug);
            }

            // Mapping to ProjectModel deferred to later tasks.
            // ------------------------------------------------------------------
            // Task 21 – Integrate Project Details Fetching
            // ------------------------------------------------------------------
            for (ProjectSummary summary : projects) {
                if (summary == null) {
                    continue;
                }

                String developerSlug = null;
                String projectSlug = null;

                // Attempt to extract slugs from the shareUrl – fallback when unavailable
                try {
                    String shareUrl = summary.getShareUrl();
                    if (shareUrl != null && !shareUrl.isBlank()) {
                        // If a full URL is provided, strip domain part to get the path
                        String path;
                        if (shareUrl.startsWith("http")) {
                            java.net.URI uri = java.net.URI.create(shareUrl);
                            path = uri.getPath();
                        } else {
                            path = shareUrl;
                        }

                        String[] segments = path.split("/");
                        for (int i = 0; i < segments.length; i++) {
                            if ("new-projects".equals(segments[i]) && i + 2 < segments.length) {
                                developerSlug = segments[i + 1];
                                projectSlug = segments[i + 2];
                                break;
                            }
                        }
                    }
                } catch (Exception ex) {
                    log.debug("Unable to parse shareUrl for summary – proceeding without slugs: {}", ex.getMessage());
                }

                if (developerSlug == null || projectSlug == null) {
                    log.warn("Could not determine developer / project slug for summary – skipping detail fetch");
                    continue;
                }

                try {
                    var detailWrapper = feignClient.getProjectDetails(developerSlug, projectSlug);
                    ProjectDetails detail = null;
                    if (detailWrapper != null &&
                        detailWrapper.getProps() != null &&
                        detailWrapper.getProps().getPageProps() != null) {
                        detail = detailWrapper.getProps().getPageProps().getDetailResult();
                    }

                    if (detail == null) {
                        log.warn("Detail payload was null for PropertyFinder project: {}/{}", developerSlug, projectSlug);
                        // Fallback to summary-only conversion
                        convertSummaryToProjectModel(summary).ifPresent(model -> {
                            Map<String, Object> meta = createMetadata(summary, null);
                            result.add(wrapInCrawlRecord(model, meta));
                        });
                        continue;
                    }

                    log.info("Successfully fetched detail payload for PropertyFinder project: {}/{}", developerSlug, projectSlug);

                    // ----------------------------------------------------------
                    // Task 22 – Integrate Conversion with Error Handling
                    // ----------------------------------------------------------
                    try {
                        Optional<ProjectModel> converted = convertToProjectModel(detail);
                        if (converted.isPresent()) {
                            Map<String, Object> meta = createMetadata(summary, detail);
                            result.add(wrapInCrawlRecord(converted.get(), meta));

                            // Spec §17 – log success for individual project
                            log.info("Successfully processed PropertyFinder project: {}/{}", developerSlug, projectSlug);
                        } else {
                            log.warn("Failed to convert project to ProjectModel: {}/{}", developerSlug, projectSlug);
                        }
                    } catch (Exception convEx) {
                        log.error("Conversion error for project {}/{}: {}", developerSlug, projectSlug, convEx.getMessage(), convEx);
                    }

                } catch (feign.FeignException.NotFound notFound) {
                    // Individual project might have been removed – fallback to summary conversion
                    log.warn("Project detail returned 404 for {}/{} – using summary as fallback", developerSlug, projectSlug);
                    convertSummaryToProjectModel(summary).ifPresent(model -> {
                        Map<String, Object> meta = createMetadata(summary, null);
                        result.add(wrapInCrawlRecord(model, meta));
                    });
                } catch (Exception ex) {
                    log.error("Error fetching project details {}/{}: {}", developerSlug, projectSlug, ex.getMessage(), ex);
                    // Attempt summary fallback conversion
                    convertSummaryToProjectModel(summary).ifPresent(model -> {
                        Map<String, Object> meta = createMetadata(summary, null);
                        result.add(wrapInCrawlRecord(model, meta));
                    });
                }
            }

            return result;

        } catch (FeignException.NotFound notFound) {
            // 404 indicates end of pagination – return empty list
            log.info("Page {} not found – treating as end of pagination", page);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error fetching PropertyFinder page {}: {}", page, e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching page: " + page, e);
        } finally {
            // Log page completion regardless of outcome
            log.info("Successfully processed page {} with {} projects", page, result.size());
        }
    }

    /**
     * Retrieves the total number of pages available from the Property&nbsp; Finder
     * "new projects" endpoint by probing page&nbsp;1 only.
     *
     * @return total page count or {@code null} when the information is absent
     * (caller decides how to handle).
     *
     * @throws RetryableCrawlerException on transient/network errors.
     */
    public Integer getTotalPages() {
        try {
            var wrapper = feignClient.getNewProjects(1);

            return Optional.ofNullable(wrapper)
                  .map(NextJsWrapper::getProps)
                  .map(NextJsWrapper.Props::getPageProps)
                  .map(SearchResultPageProps::getSearchResult)
                  .map(SearchResultPageProps.SearchResult::getMeta)
                  .map(SearchResultPageProps.Meta::getPagination)
                  .map(SearchResultPageProps.Pagination::getTotal)
                  .orElse(null);
        } catch (FeignException.NotFound notFound) {
            // Page 1 not found – treat as empty catalogue
            log.info("PropertyFinder page 1 returned 404 – no projects available");
            return 0;
        } catch (Exception e) {
            log.error("Error probing total pages: {}", e.getMessage(), e);
            throw new RetryableCrawlerException("Error probing pagination", e);
        }
    }

    /* --------------------------------------------------------------------- */
    /* Placeholder conversion helpers – will be filled in future tasks       */
    /* --------------------------------------------------------------------- */

    /**
     * Converts the raw {@link ProjectDetails} payload into the canonical
     * {@link ProjectModel}.
     * <p>
     * This method will be fleshed-out in the upcoming Phase&nbsp;3 mapping tasks.
     * For <strong>Task&nbsp;8</strong> we only provide a minimal stub that
     * instantiates a new {@link ProjectModel} via the generated builder and
     * returns it wrapped inside an {@link Optional}.  This allows the
     * surrounding code and unit tests to compile and run while the detailed
     * field-by-field mapping is implemented incrementally.
     * </p>
     *
     * @param details full project payload returned by the Property&nbsp;Finder API
     *
     * @return Optional containing an (empty) {@link ProjectModel} instance when
     * {@code details} is not {@code null}; otherwise {@link Optional#empty()}.
     */
    private Optional<ProjectModel> convertToProjectModel(ProjectDetails details) {
        if (details == null) {
            return Optional.empty();
        }

        // -------------------------------------------------------------
        // Task 9 – Identifiers & URNs mapping
        // -------------------------------------------------------------

        // Constant source URN defined in common helper
        final String sourceUrn = PropertyFinderCommon.SOURCE_URN;

        // Developer slug – use provided slug or fall back to slugified developer name
        String developerSlug = Optional.ofNullable(details.getDeveloper())
              .map(ProjectDetails.Developer::getSlug)
              .filter(s -> s != null && !s.isBlank())
              .orElseGet(() -> Optional.ofNullable(details.getDeveloper())
                    .map(ProjectDetails.Developer::getName)
                    .map(PropertyFinderApiService::slugify)
                    .orElse(null));

        if (developerSlug != null) {
            developerSlug = developerSlug.toLowerCase();
        }

        // Project slug derived from top-level slug or fallback to slugified title
        String fullSlug = details.getSlug();
        String projectSlug = null;
        if (fullSlug != null && !fullSlug.isBlank()) {
            int slashIdx = fullSlug.indexOf('/');
            projectSlug = (slashIdx >= 0 && slashIdx < fullSlug.length() - 1) ? fullSlug.substring(
                  slashIdx + 1) : fullSlug;
        }

        if ((projectSlug == null || projectSlug.isBlank()) && details.getTitle() != null) {
            projectSlug = slugify(details.getTitle());
        }

        // Construct URNs following spec §5
        String developerUrn = developerSlug != null ? sourceUrn +
              ":developer:" +
              developerSlug : null;
        String projectUrn = (developerUrn != null && projectSlug != null) ? developerUrn +
              ":project:" +
              projectSlug : null;

        // -------------------------------------------------------------
        // Task 10 – Basic attributes
        // -------------------------------------------------------------

        String title = details.getTitle();
        if (title == null || title.isBlank()) {
            // Title is mandatory according to ProjectModel schema – skip conversion
            log.warn("ProjectDetails.id={} has no title – skipping conversion", details.getId());
            return Optional.empty();
        }

        String description = StringUtils.stripHtml(details.getDescription());

        // Initialise builder and populate identifier & basic attribute fields
        ProjectModel.ProjectModelBuilderBase<?> modelBuilder = ProjectModel.builder()
              .withSourceUrn(sourceUrn)
              .withDeveloperUrn(developerUrn)
              .withProjectUrn(projectUrn)
              .withExternalId(details.getId())
              .withTitle(title)
              .withDescription(description)
              .withCurrency("AED")
              .withProjectSlug(projectSlug);

        // -------------------------------------------------------------
        // Task 11 – Location & Geo mapping
        // -------------------------------------------------------------

        ProjectDetails.Location loc = details.getLocation();
        List<ProjectDetails.LocationNode> locationTree = details.getLocationTree();

        // Build LocationModel if we have at least the address or city information
        if (loc != null || (locationTree != null && !locationTree.isEmpty())) {
            LocationModel.LocationModelBuilderBase<?> locBuilder = LocationModel.builder();

            // Address – use fullName if available, else fallback to loc.name
            String address = loc != null ? (loc.getFullName() !=
                  null ? loc.getFullName() : loc.getName()) : null;
            if (address != null) {
                locBuilder.withAddress(address);
            }

            String city = null;
            String cityDistrict = null;
            if (locationTree != null && !locationTree.isEmpty()) {
                city = locationTree.get(0) != null ? locationTree.get(0).getName() : null;
                if (locationTree.size() > 1) {
                    cityDistrict = locationTree.get(1) != null ? locationTree.get(1)
                          .getName() : null;
                }
            }

            if (city != null) {
                locBuilder.withCity(city);
                // state mirrors city per spec
                locBuilder.withState(city);
            }

            if (cityDistrict != null) {
                locBuilder.withCityDistrict(cityDistrict);
            }

            // Country constant
            locBuilder.withCountry("AE");

            // Build the LocationModel and attach when we have at least address or city
            LocationModel locationModel = locBuilder.build();
            modelBuilder.withLocation(locationModel);
        }

        // Coordinates – map when at least lat OR lng is present
        Double lat = null;
        Double lng = null;
        if (loc != null && loc.getCoordinates() != null) {
            lat = loc.getCoordinates().getLat();
            Double lon = loc.getCoordinates().getLon();
            Double lngField = loc.getCoordinates().getLng();
            if (lon != null) {
                lng = lon;
            } else if (lngField != null) {
                lng = lngField;
            }
        }

        if (lat != null && lng != null) {
            CoordinatesModel coords = CoordinatesModel.builder()
                  .withLat(lat)
                  .withLng(lng)
                  .build();
            modelBuilder.withCoordinates(coords);
        }

        // Polygon is explicitly set to null per spec – no action required

        // -------------------------------------------------------------
        // Task 12 – Media mapping (images, videos, master-plan)
        // -------------------------------------------------------------

        List<ImageModel> imageModels = new ArrayList<>();
        List<Video> videoModels = new ArrayList<>();

        String masterPlanImageUrl = null;

        if (details.getImages() != null) {
            for (ProjectDetails.GalleryItem item : details.getImages()) {
                if (item == null ||
                      item.getType() == null ||
                      item.getSource() == null ||
                      item.getSource().isBlank()) {
                    continue;
                }

                String type = item.getType().trim().toLowerCase();
                try {
                    java.net.URI uri = java.net.URI.create(item.getSource());
                    switch (type) {
                        case "image" -> {
                            var imgBuilder = ImageModel.builder()
                                  .withUrl(uri);
                            imageModels.add(imgBuilder.build());
                        }
                        case "video" -> {
                            var vidBuilder = Video.builder().withUrl(uri);
                            videoModels.add(vidBuilder.build());
                        }
                        case "master-plan" -> {
                            if (masterPlanImageUrl == null) {
                                masterPlanImageUrl = item.getSource();
                            }
                        }
                        default -> {
                            // ignore other types for now
                        }
                    }
                } catch (IllegalArgumentException iae) {
                    log.warn("Invalid URI in gallery item – skipping: {}", item.getSource());
                }
            }
        }

        if (!imageModels.isEmpty()) {
            modelBuilder.withImages(imageModels);
            // First image becomes cover image
            modelBuilder.withCoverImage(imageModels.get(0));
        }

        if (!videoModels.isEmpty()) {
            modelBuilder.withVideos(videoModels);
        }

        // Additional data map (master plan)
        Map<String, Object> additionalData = new HashMap<>();

        if (masterPlanImageUrl != null) {
            additionalData.put("masterPlanImage", masterPlanImageUrl);
        }

        if (details.getMasterPlan() != null && details.getMasterPlan().getDescription() != null) {
            String mpDesc = StringUtils.stripHtml(details.getMasterPlan().getDescription());
            if (mpDesc != null && !mpDesc.isBlank()) {
                additionalData.put("masterPlanDescription", mpDesc);
            }
        }

        if (!additionalData.isEmpty()) {
            modelBuilder.withAdditionalData(additionalData);
        }

        // -------------------------------------------------------------
        // Task 12.5 – Amenities mapping
        // -------------------------------------------------------------

        if (details.getAmenities() != null && !details.getAmenities().isEmpty()) {
            List<AmenityModel> amenityModels = new ArrayList<>();
            for (ProjectDetails.Amenity amenity : details.getAmenities()) {
                if (amenity == null || amenity.getName() == null || amenity.getName().isBlank()) {
                    continue;
                }
                AmenityModel amenityModel = AmenityModel.builder()
                      .withLabel(amenity.getName())
                      .build();
                amenityModels.add(amenityModel);
            }
            if (!amenityModels.isEmpty()) {
                modelBuilder.withAmenities(amenityModels);
            }
        }

        // -------------------------------------------------------------
        // Task 13 – Construction status mapping
        // -------------------------------------------------------------

        String phaseRaw = details.getConstructionPhase();
        if (phaseRaw != null && !phaseRaw.isBlank()) {
            String phase = phaseRaw.trim().toLowerCase();
            AdModel.ConstructionStatusModel status = null;
            switch (phase) {
                case "not_started", "off_plan" ->
                      status = AdModel.ConstructionStatusModel.NOT_STARTED;
                case "under_construction" ->
                      status = AdModel.ConstructionStatusModel.ACTIVE;
                case "completed", "ready" ->
                      status = AdModel.ConstructionStatusModel.FINISHED;
                default -> {
                    // unknown enumeration – leave null
                }
            }
            if (status != null) {
                modelBuilder.withProjectStatus(status);
            }
        }

        // -------------------------------------------------------------
        // Task 14 – ProjectStats (scalar fields) mapping
        // -------------------------------------------------------------

        ProjectStats.ProjectStatsBuilderBase<?> statsBuilder = ProjectStats.builder();
        boolean hasStats = false;

        // priceMin – starting price in AED
        if (details.getStartingPrice() != null) {
            PriceModel price = PriceModel.builder()
                  .withValue(details.getStartingPrice().doubleValue())
                  .withCurrency("AED")
                  .build();
            statsBuilder.withPriceMin(price);
            hasStats = true;
        }

        // launchDate – salesStartDate -> yyyy-MM-dd
        if (details.getSalesStartDate() != null) {
            String ld = parseIsoDate(details.getSalesStartDate().toString());
            if (ld != null) {
                statsBuilder.withLaunchDate(ld);
                hasStats = true;
            }
        }

        // completionDate – deliveryDate -> yyyy-MM-dd
        if (details.getDeliveryDate() != null) {
            String cd = parseIsoDate(details.getDeliveryDate().toString());
            if (cd != null) {
                statsBuilder.withCompletionDate(cd);
                hasStats = true;
            }
        }

        // -------------------------------------------------------------
        // Task 15 – ProjectStats propertyTypes mapping
        // -------------------------------------------------------------

        List<AdModel.PropertyTypeModel> propertyTypeList = null;

        if (details.getPropertyTypes() != null && !details.getPropertyTypes().isEmpty()) {
            Set<AdModel.PropertyTypeModel> types = new LinkedHashSet<>();
            for (String raw : details.getPropertyTypes()) {
                if (raw == null || raw.isBlank()) continue;
                String enumName = raw.trim().toUpperCase().replace('-', '_');
                AdModel.PropertyTypeModel pt;
                try {
                    pt = AdModel.PropertyTypeModel.valueOf(enumName);
                } catch (IllegalArgumentException ex) {
                    pt = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                }
                types.add(pt);
            }
            if (!types.isEmpty()) {
                propertyTypeList = new ArrayList<>(types);
                statsBuilder.withPropertyTypes(propertyTypeList);
                hasStats = true;
            }
        }

        // -------------------------------------------------------------
        // Task 16 – ProjectStats UnitStats mapping
        // -------------------------------------------------------------

        if (details.getUnits() != null && !details.getUnits().isEmpty()) {
            List<UnitStats> unitStatsList = new ArrayList<>();

            for (ProjectDetails.BuildingGroup group : details.getUnits()) {
                if (group == null || group.getUnits() == null) {
                    continue;
                }
                for (ProjectDetails.UnitSet unitSet : group.getUnits()) {
                    if (unitSet == null || unitSet.getList() == null) {
                        continue;
                    }

                    // Determine property type enum for this set
                    AdModel.PropertyTypeModel propertyTypeEnum;
                    if (unitSet.getPropertyType() != null) {
                        String enumName = unitSet.getPropertyType()
                              .trim()
                              .toUpperCase()
                              .replace('-', '_');
                        try {
                            propertyTypeEnum = AdModel.PropertyTypeModel.valueOf(
                                  enumName);
                        } catch (IllegalArgumentException ex) {
                            propertyTypeEnum = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                        }
                    } else {
                        propertyTypeEnum = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                    }

                    for (ProjectDetails.Unit unit : unitSet.getList()) {
                        if (unit == null) {
                            continue;
                        }

                        UnitStats.UnitStatsBuilderBase<?> usBuilder = UnitStats.builder()
                              .withPropertyType(propertyTypeEnum);

                        // Bedrooms
                        if (unit.getBedrooms() != null) {
                            usBuilder.withBedrooms(unit.getBedrooms().doubleValue());
                        }

                        // PriceMin
                        if (unit.getStartingPrice() != null) {
                            PriceModel price = PriceModel.builder()
                                  .withValue(unit.getStartingPrice().doubleValue())
                                  .withCurrency("AED")
                                  .build();
                            usBuilder.withPriceMin(price);
                        }

                        // Areas conversion (sqft to sqm) – areaFrom mandatory >0
                        Double areaMinSqm = null;
                        if (unit.getAreaFrom() != null && unit.getAreaFrom() > 0) {
                            areaMinSqm = com.realmond.temporal_service.crawler.common.UnitConversionUtils.sqftToSqm(
                                  unit.getAreaFrom(),
                                  2
                            );
                            usBuilder.withAreaMinSqm(areaMinSqm);
                            // Deprecated range field mirrors min
                            usBuilder.withAreaRangeSqm(areaMinSqm);
                        }

                        if (unit.getAreaTo() != null && unit.getAreaTo() > 0) {
                            Double areaMaxSqm = com.realmond.temporal_service.crawler.common.UnitConversionUtils.sqftToSqm(
                                  unit.getAreaTo(),
                                  2
                            );
                            usBuilder.withAreaMaxSqm(areaMaxSqm);
                        }

                        UnitStats stats = usBuilder.build();
                        unitStatsList.add(stats);
                    }
                }
            }

            if (!unitStatsList.isEmpty()) {
                statsBuilder.withUnits(unitStatsList);
                hasStats = true;
            }
        }

        // -------------------------------------------------------------
        // Task 17.5 – Floor plan mapping (updated to FloorPlanModel)
        // -------------------------------------------------------------

        Map<String, FloorPlanModel> floorPlanByUrl = new LinkedHashMap<>();

        if (details.getUnits() != null) {
            for (ProjectDetails.BuildingGroup group : details.getUnits()) {
                if (group == null || group.getUnits() == null) {
                    continue;
                }
                for (ProjectDetails.UnitSet unitSet : group.getUnits()) {
                    if (unitSet == null || unitSet.getList() == null) {
                        continue;
                    }

                    // Property type enum for this unit set
                    AdModel.PropertyTypeModel ptEnum;
                    if (unitSet.getPropertyType() != null) {
                        String enumName = unitSet.getPropertyType()
                              .trim()
                              .toUpperCase()
                              .replace('-', '_');
                        try {
                            ptEnum = AdModel.PropertyTypeModel.valueOf(
                                  enumName);
                        } catch (IllegalArgumentException ex) {
                            ptEnum = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                        }
                    } else {
                        ptEnum = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                    }

                    for (ProjectDetails.Unit unit : unitSet.getList()) {
                        if (unit == null || unit.getLayouts() == null) {
                            continue;
                        }
                        for (ProjectDetails.Layout layout : unit.getLayouts()) {
                            if (layout == null || layout.getFloorPlans() == null) {
                                continue;
                            }
                            for (String urlStr : layout.getFloorPlans()) {
                                if (urlStr == null || urlStr.isBlank()) {
                                    continue;
                                }
                                if (floorPlanByUrl.containsKey(urlStr)) {
                                    continue; // dedupe by URL
                                }

                                String externalId = generateSha1Hash(urlStr);
                                String floorplanUrn = sourceUrn + ":floorplan:" + externalId;

                                FloorPlanModel.FloorPlanModelBuilderBase<?> fpBuilder = FloorPlanModel.builder()
                                      .withExternalId(externalId)
                                      .withFloorplanUrn(floorplanUrn)
                                      .withProjectUrn(projectUrn)
                                      .withSourceUrn(sourceUrn)
                                      .withPropertyType(ptEnum);

                                if (layout.getBedrooms() != null) {
                                    fpBuilder.withBedrooms(layout.getBedrooms().doubleValue());
                                }
                                if (layout.getLayoutType() != null) {
                                    fpBuilder.withTitle(layout.getLayoutType());
                                }

                                try {
                                    java.net.URI imgUri = java.net.URI.create(urlStr);
                                    ImageModel img = ImageModel.builder()
                                          .withUrl(imgUri)
                                          .build();
                                    fpBuilder.withImage(img);
                                } catch (Exception ignored) {
                                }

                                FloorPlanModel fp = fpBuilder.build();
                                floorPlanByUrl.put(urlStr, fp);
                            }
                        }
                    }
                }
            }
        }

        if (!floorPlanByUrl.isEmpty()) {
            modelBuilder.withFloorPlans(new ArrayList<>(floorPlanByUrl.values()));
        }

        // -------------------------------------------------------------
        // Task 18 – Brochures & Payment Plans mapping
        // -------------------------------------------------------------

        // 18.1  Brochures ---------------------------------------------------
        if (details.getBrochureUrl() != null && !details.getBrochureUrl().isBlank()) {
            try {
                java.net.URI brochureUri = java.net.URI.create(details.getBrochureUrl());
                BrochureModel brochure = BrochureModel.builder()
                      // Property Finder does not expose a brochure title – keep null
                      .withUrl(brochureUri).build();
                List<BrochureModel> brochures = List.of(
                      brochure);
                modelBuilder.withBrochures(brochures);
            } catch (IllegalArgumentException iae) {
                log.warn("Invalid brochure URL – skipping: {}", details.getBrochureUrl());
            }
        }

        // 18.2  Payment plans ----------------------------------------------
        if (details.getPaymentPlans() != null && !details.getPaymentPlans().isEmpty()) {
            List<Object> paymentPlanModels = new ArrayList<>();

            for (ProjectDetails.PaymentPlan srcPlan : details.getPaymentPlans()) {
                if (srcPlan == null) {
                    continue;
                }

                // Build phases list --------------------------------------------------
                List<com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPhaseModel> phaseModels = new ArrayList<>();
                if (srcPlan.getPhases() != null) {
                    for (ProjectDetails.Phase srcPhase : srcPlan.getPhases()) {
                        if (srcPhase == null) {
                            continue;
                        }

                        List<com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentMileModel> mileModels = new ArrayList<>();
                        if (srcPhase.getMiles() != null) {
                            for (ProjectDetails.Mile srcMile : srcPhase.getMiles()) {
                                if (srcMile == null) {
                                    continue;
                                }
                                com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentMileModel mile = com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentMileModel.builder()
                                      .label(srcMile.getLabel())
                                      .value(srcMile.getValue() == null ? null : srcMile.getValue()
                                            .doubleValue())
                                      .build();
                                mileModels.add(mile);
                            }
                        }

                        com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPhaseModel phase = com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPhaseModel.builder()
                              .label(srcPhase.getLabel())
                              .value(srcPhase.getValue() == null ? null : srcPhase.getValue()
                                    .doubleValue())
                              .miles(mileModels.isEmpty() ? null : mileModels)
                              .build();
                        phaseModels.add(phase);
                    }
                }

                com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel planModel = com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel.builder()
                      .title(srcPlan.getTitle())
                      .phases(phaseModels.isEmpty() ? null : phaseModels)
                      .build();

                paymentPlanModels.add(planModel);
            }

            if (!paymentPlanModels.isEmpty()) {
                modelBuilder.withPaymentPlans(paymentPlanModels);
            }
        }
        // -------------------------------------------------------------

        if (hasStats) {
            modelBuilder.withProjectStats(statsBuilder.build());
        }

        // -------------------------------------------------------------
        // Task 19 – Additional data mapping
        // -------------------------------------------------------------

        // 19.1 developerId ---------------------------------------------------
        if (details.getDeveloper() != null && details.getDeveloper().getId() != null) {
            additionalData.put("developerId", details.getDeveloper().getId());
        }

        // 19.2 FAQs ----------------------------------------------------------
        if (details.getFaqs() != null && !details.getFaqs().isEmpty()) {
            List<Map<String, String>> faqList = new ArrayList<>();
            for (ProjectDetails.Faq f : details.getFaqs()) {
                if (f == null) {
                    continue;
                }
                String q = f.getQuestion();
                String aRaw = f.getAnswer();
                String a = aRaw == null ? null : StringUtils.stripHtml(aRaw);
                Map<String, String> faqMap = new LinkedHashMap<>();
                faqMap.put("question", q);
                faqMap.put("answer", a);
                faqList.add(faqMap);
            }
            if (!faqList.isEmpty()) {
                additionalData.put("faqs", faqList);
            }
        }

        // 19.3 ownershipType -----------------------------------------------
        String rawOwnership = details.getOwnershipType();
        String ownershipEnum;
        if (rawOwnership == null || rawOwnership.isBlank()) {
            ownershipEnum = "UNKNOWN";
        } else {
            switch (rawOwnership.trim().toLowerCase()) {
                case "freehold" -> ownershipEnum = "FREEHOLD";
                case "leasehold" -> ownershipEnum = "LEASEHOLD";
                default -> ownershipEnum = "UNKNOWN";
            }
        }
        additionalData.put("ownershipType", ownershipEnum);

        // 19.4 salesPhase raw ----------------------------------------------
        if (details.getSalesPhase() != null && !details.getSalesPhase().isBlank()) {
            String rawSalesPhase = details.getSalesPhase();
            additionalData.put("salesPhase", rawSalesPhase);

            // Task 19.5 – derived enum
            additionalData.put("ADDITIONAL_SALES_PHASE", rawSalesPhase.trim().toUpperCase());
        }

        // 19.5 lastInspectionDate -----------------------------------------
        if (details.getLastInspectionDate() != null) {
            String date = parseIsoDate(details.getLastInspectionDate().toString());
            if (date != null) {
                additionalData.put("lastInspectionDate", date);
            }
        }

        // 19.6 timelinePhases raw list ------------------------------------
        if (details.getTimelinePhases() != null && !details.getTimelinePhases().isEmpty()) {
            additionalData.put("timelinePhases", details.getTimelinePhases());
        }

        // Ensure additionalData attached (might be already, but safe)
        if (!additionalData.isEmpty()) {
            modelBuilder.withAdditionalData(additionalData);
        }

        // -------------------------------------------------------------
        // Task 20 – Project URLs mapping
        // -------------------------------------------------------------

        if (developerSlug != null && projectSlug != null) {
            try {
                java.net.URI url = new java.net.URI(
                      "https",
                      "www.propertyfinder.ae",
                      "/en/new-projects/" +
                            developerSlug +
                            "/" +
                            projectSlug,
                      null
                );
                modelBuilder.withUrls(List.of(url));
            } catch (java.net.URISyntaxException e) {
                log.warn(
                      "Unable to build canonical URL for project: {}/{}",
                      developerSlug,
                      projectSlug
                );
            }
        }

        ProjectModel model = modelBuilder.build();

        return Optional.of(model);
    }

    private CrawlRecord<ProjectModel> wrapInCrawlRecord(
          ProjectModel model,
          Map<String, Object> metadata
    ) {
        return new CrawlRecord<>(model, metadata, model.getSourceUrn(), model.getProjectUrn());
    }

    private Map<String, Object> createMetadata(
          ProjectSummary summary,
          ProjectDetails detail
    ) {
        Map<String, Object> meta = new HashMap<>();
        Map<String, Object> raw = new HashMap<>();
        raw.put("project_summary", summary);
        if (detail != null) {
            raw.put("project_detail", detail);
        }
        meta.put("raw_data", raw);
        meta.put("conversion_timestamp", java.time.Instant.now().toString());
        meta.put("source_api", PropertyFinderCommon.SOURCE_URN);
        meta.put("api_version", "v1");
        return meta;
    }

    /**
     * Fallback conversion using only the listing {@link ProjectSummary} when the
     * detailed API call fails.
     */
    private Optional<ProjectModel> convertSummaryToProjectModel(ProjectSummary summary) {
        if (summary == null) {
            return Optional.empty();
        }
        try {
            final String sourceUrn = PropertyFinderCommon.SOURCE_URN;

            // Slug extraction from shareUrl
            String developerSlug = null;
            String projectSlug = null;
            String shareUrl = summary.getShareUrl();
            if (shareUrl != null && !shareUrl.isBlank()) {
                String path = shareUrl.startsWith("http") ? java.net.URI.create(shareUrl).getPath() : shareUrl;
                String[] segments = path.split("/");
                for (int i = 0; i < segments.length; i++) {
                    if ("new-projects".equals(segments[i]) && i + 2 < segments.length) {
                        developerSlug = segments[i + 1];
                        projectSlug = segments[i + 2];
                        break;
                    }
                }
            }

            // Fallback slug derivation
            if (developerSlug == null && summary.getDeveloper() != null && summary.getDeveloper().getName() != null) {
                developerSlug = slugify(summary.getDeveloper().getName());
            }
            if (projectSlug == null && summary.getTitle() != null) {
                projectSlug = slugify(summary.getTitle());
            }

            if (developerSlug != null) {
                developerSlug = developerSlug.toLowerCase();
            }

            String developerUrn = developerSlug != null ? sourceUrn + ":developer:" + developerSlug : null;
            String projectUrn = (developerUrn != null && projectSlug != null) ? developerUrn + ":project:" + projectSlug : null;

            String title = summary.getTitle();
            if (title == null || title.isBlank()) {
                return Optional.empty();
            }

            ProjectModel.ProjectModelBuilderBase<?> builder = ProjectModel.builder()
                  .withSourceUrn(sourceUrn)
                  .withDeveloperUrn(developerUrn)
                  .withProjectUrn(projectUrn)
                  .withExternalId(summary.getId())
                  .withTitle(title)
                  .withCurrency("AED")
                  .withProjectSlug(projectSlug);

            // Construction phase mapping (same rules)
            if (summary.getConstructionPhase() != null) {
                switch (summary.getConstructionPhase().trim().toLowerCase()) {
                    case "not_started", "off_plan" -> builder.withProjectStatus(AdModel.ConstructionStatusModel.NOT_STARTED);
                    case "under_construction" -> builder.withProjectStatus(AdModel.ConstructionStatusModel.ACTIVE);
                    case "completed", "ready" -> builder.withProjectStatus(AdModel.ConstructionStatusModel.FINISHED);
                    default -> {
                    }
                }
            }

            // Stats (priceMin)
            boolean hasStats = false;
            ProjectStats.ProjectStatsBuilderBase<?> statsBuilder = ProjectStats.builder();
            if (summary.getStartingPrice() != null) {
                PriceModel price = PriceModel.builder()
                      .withValue(summary.getStartingPrice().doubleValue())
                      .withCurrency("AED")
                      .build();
                statsBuilder.withPriceMin(price);
                hasStats = true;
            }

            // completionDate from deliveryDate
            if (summary.getDeliveryDate() != null) {
                String completionDate = parseIsoDate(summary.getDeliveryDate().toString());
                if (completionDate != null) {
                    statsBuilder.withCompletionDate(completionDate);
                    hasStats = true;
                }
            }

            // Property types
            List<AdModel.PropertyTypeModel> propertyTypeList = null;

            if (summary.getPropertyTypes() != null && !summary.getPropertyTypes().isEmpty()) {
                Set<AdModel.PropertyTypeModel> types = new LinkedHashSet<>();
                for (String raw : summary.getPropertyTypes()) {
                    if (raw == null || raw.isBlank()) continue;
                    String enumName = raw.trim().toUpperCase().replace('-', '_');
                    AdModel.PropertyTypeModel pt;
                    try {
                        pt = AdModel.PropertyTypeModel.valueOf(enumName);
                    } catch (IllegalArgumentException ex) {
                        pt = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                    }
                    types.add(pt);
                }
                if (!types.isEmpty()) {
                    propertyTypeList = new ArrayList<>(types);
                    statsBuilder.withPropertyTypes(propertyTypeList);
                    hasStats = true;
                }
            }

            // Bedrooms → minimal UnitStats list
            if (summary.getBedrooms() != null && !summary.getBedrooms().isEmpty()) {
                List<UnitStats> unitStatsList = new ArrayList<>();

                // Choose primary property type (first if any) else fallback
                AdModel.PropertyTypeModel primaryPt;
                if (propertyTypeList != null && !propertyTypeList.isEmpty()) {
                    primaryPt = propertyTypeList.get(0);
                } else {
                    primaryPt = AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                }

                for (String bedStr : summary.getBedrooms()) {
                    if (bedStr == null || bedStr.isBlank()) continue;
                    try {
                        double beds = Double.parseDouble(bedStr.replaceAll("[^0-9.]", ""));
                        UnitStats.UnitStatsBuilderBase<?> usBuilder = UnitStats.builder()
                              .withPropertyType(primaryPt)
                              .withBedrooms(beds);
                        unitStatsList.add(usBuilder.build());
                    } catch (NumberFormatException ignored) {
                    }
                }

                if (!unitStatsList.isEmpty()) {
                    statsBuilder.withUnits(unitStatsList);
                    hasStats = true;
                }
            }

            if (hasStats) {
                builder.withProjectStats(statsBuilder.build());
            }

            // ------------------ Media: images list ---------------------------
            if (summary.getImages() != null && !summary.getImages().isEmpty()) {
                List<ImageModel> imgs = new ArrayList<>();
                for (String urlStr : summary.getImages()) {
                    if (urlStr == null || urlStr.isBlank()) continue;
                    try {
                        java.net.URI uri = java.net.URI.create(urlStr);
                        imgs.add(ImageModel.builder().withUrl(uri).build());
                    } catch (Exception ignored) {
                    }
                }
                if (!imgs.isEmpty()) {
                    builder.withImages(imgs);
                    builder.withCoverImage(imgs.get(0));
                }
            }

            // ------------------ Amenities ------------------------------------
            if (summary.getAmenities() != null && !summary.getAmenities().isEmpty()) {
                List<AmenityModel> amModels = new ArrayList<>();
                for (ProjectSummary.Amenity am : summary.getAmenities()) {
                    if (am == null || am.getName() == null || am.getName().isBlank()) continue;
                    amModels.add(AmenityModel.builder().withLabel(am.getName()).build());
                }
                if (!amModels.isEmpty()) {
                    builder.withAmenities(amModels);
                }
            }

            // ------------------ Location ------------------------------------
            if (summary.getLocation() != null) {
                String address = summary.getLocation().getFullName();
                Double lat = summary.getLocation().getCoordinates() != null ? summary.getLocation().getCoordinates().getLat() : null;
                Double lng = null;
                if (summary.getLocation().getCoordinates() != null) {
                    Double lon = summary.getLocation().getCoordinates().getLon();
                    Double lngField = summary.getLocation().getCoordinates().getLng();
                    lng = lon != null ? lon : lngField;
                }

                LocationModel.LocationModelBuilderBase<?> locBuilder = LocationModel.builder();
                if (address != null && !address.isBlank()) {
                    locBuilder.withAddress(address);
                }
                // Attempt to guess city by first part of address comma separation
                if (address != null && address.contains(",")) {
                    String city = address.split(",")[0].trim();
                    if (!city.isBlank()) {
                        locBuilder.withCity(city);
                        locBuilder.withState(city);
                    }
                }
                locBuilder.withCountry("AE");

                builder.withLocation(locBuilder.build());

                if (lat != null && lng != null) {
                    builder.withCoordinates(CoordinatesModel.builder().withLat(lat).withLng(lng).build());
                }
            }

            // Canonical URL
            if (developerSlug != null && projectSlug != null) {
                try {
                    java.net.URI url = new java.net.URI("https", "www.propertyfinder.ae", "/en/new-projects/" + developerSlug + "/" + projectSlug, null);
                    builder.withUrls(List.of(url));
                } catch (java.net.URISyntaxException ignored) {
                }
            }

            return Optional.of(builder.build());
        } catch (Exception ex) {
            log.error("Error converting ProjectSummary to ProjectModel: {}", ex.getMessage(), ex);
            return Optional.empty();
        }
    }



    /**
     * Creates a URL-safe slug from an arbitrary string.
     * Replaces all non-alphanumeric characters with hyphens, collapses
     * consecutive hyphens and trims leading/trailing hyphens.
     */
    private static String slugify(String input) {
        if (input == null) {
            return null;
        }
        // Replace non-alphanumeric with hyphen
        String slug = input.toLowerCase()
              .replaceAll("[^a-z0-9]+", "-")
              .replaceAll("-+", "-")
              .replaceAll("^-|-$", "");
        return slug.isBlank() ? null : slug;
    }

    /**
     * Parses an ISO-8601 date/time string and returns the date portion in
     * {@code yyyy-MM-dd} format. Returns {@code null} when the input is
     * {@code null}, blank or cannot be parsed.
     */
    private static String parseIsoDate(String iso) {
        if (iso == null || iso.isBlank()) {
            return null;
        }
        try {
            java.time.OffsetDateTime odt = java.time.OffsetDateTime.parse(
                  iso,
                  java.time.format.DateTimeFormatter.ISO_DATE_TIME
            );
            java.time.LocalDate date = odt.toLocalDate();
            return date.toString();
        } catch (Exception ex) {
            try {
                // Attempt plain date without time component
                java.time.LocalDate d = java.time.LocalDate.parse(
                      iso,
                      java.time.format.DateTimeFormatter.ISO_DATE
                );
                return d.toString();
            } catch (Exception ignored) {
                log.debug("Unable to parse ISO date: {}", iso);
                return null;
            }
        }
    }

    /**
     * Generates SHA-1 hex lowercase hash from input string.
     */
    private static String generateSha1Hash(String input) {
        if (input == null) {
            return null;
        }
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-1");
            byte[] hashBytes = md.digest(input.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            // Should never happen
            return null;
        }
    }
}
