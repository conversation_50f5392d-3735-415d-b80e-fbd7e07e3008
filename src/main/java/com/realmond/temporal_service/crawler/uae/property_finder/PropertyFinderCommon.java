package com.realmond.temporal_service.crawler.uae.property_finder;

/**
 * Shared constants and helper methods used by the Property&nbsp;Finder crawler.
 */
public final class PropertyFinderCommon {

    private PropertyFinderCommon() {
    }

    public static final String SOURCE_URN = "urn:source:propertyfinder:ae";

    public static final class Developer {
        public static String urn(String developerSlug) {
            return SOURCE_URN + ":developer:" + developerSlug;
        }
    }

    public static final class Project {
        public static String urn(String developerSlug, String projectSlug) {
            return Developer.urn(developerSlug) + ":project:" + projectSlug;
        }
    }
} 