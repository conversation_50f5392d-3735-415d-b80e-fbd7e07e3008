package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.common.StringUtils;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.property_finder.model.DeveloperDetails;
import com.realmond.temporal_service.crawler.uae.property_finder.model.DeveloperPageProps;
import com.realmond.temporal_service.crawler.uae.property_finder.model.DeveloperSummary;
import com.realmond.temporal_service.crawler.uae.property_finder.model.DevelopersPageProps;
import com.realmond.temporal_service.crawler.uae.property_finder.model.NextJsWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

/**
 * Service for processing Property Finder developer data using typed Next.js payloads.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PropertyFinderDeveloperApiService {

    private final PropertyFinderFeignRestClient client;
    private final PropertyFinderSettings settings;

    /**
     * Result of developer conversion including the DeveloperModel and raw API metadata.
     */
    private record DeveloperConversionResult(Optional<DeveloperModel> developerModel,
                                             Map<String, Object> rawApiMetadata) {
    }

    /**
     * Fetches the total number of developer pages from Property Finder.
     *
     * @return Total number of pages (minimum 1)
     */
    public int fetchDeveloperPagesCount() {
        log.info("Fetching Property Finder developer pages count");
        try {
            NextJsWrapper<DevelopersPageProps> wrapper = client.getDevelopers(1);
            DevelopersPageProps pageProps = wrapper.getProps().getPageProps();
            if (pageProps == null || pageProps.getDevelopersResult() == null
                    || pageProps.getDevelopersResult().getMeta() == null
                    || pageProps.getDevelopersResult().getMeta().getPagination() == null) {
                log.warn("Pagination info missing in Property Finder developer response – defaulting to 1 page");
                return 1;
            }
            Integer total = pageProps.getDevelopersResult().getMeta().getPagination().getTotal();
            int totalPages = total != null && total > 0 ? total : 1;
            log.info("Property Finder returned {} pages of developers", totalPages);
            return totalPages;
        } catch (Exception e) {
            log.error("Error fetching Property Finder developer pages count: {}", e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching Property Finder developer pages count", e);
        }
    }

    /**
     * Fetches and processes a single page of developers.
     *
     * @param page 1-based page index
     * @return List of CrawlRecords wrapping DeveloperModel instances
     */
    public List<CrawlRecord<DeveloperModel>> fetchDevelopersPage(int page) {
        log.info("Fetching Property Finder developer page: {}", page);
        List<CrawlRecord<DeveloperModel>> result = new ArrayList<>();

        try {
            NextJsWrapper<DevelopersPageProps> wrapper = client.getDevelopers(page);
            DevelopersPageProps pageProps = wrapper.getProps().getPageProps();
            if (pageProps == null || pageProps.getDevelopersResult() == null
                    || pageProps.getDevelopersResult().getData() == null) {
                log.warn("No developers found on page {}", page);
                return result;
            }

            List<DeveloperSummary> summaries = pageProps.getDevelopersResult().getData();
            log.info("Found {} developers on page {}", summaries.size(), page);

            for (DeveloperSummary summary : summaries) {
                try {
                    Optional<DeveloperDetails> details = Optional.empty();
                    if (Boolean.TRUE.equals(summary.getDevPageEnabled()) && summary.getSlug() != null) {
                        try {
                            NextJsWrapper<DeveloperPageProps> detailWrapper = client.getDeveloperDetails(summary.getSlug());
                            DeveloperDetails detail = null;
                            if (detailWrapper != null &&
                                detailWrapper.getProps() != null &&
                                detailWrapper.getProps().getPageProps() != null &&
                                detailWrapper.getProps().getPageProps().getDevResult() != null) {
                                detail = detailWrapper.getProps().getPageProps().getDevResult().getDeveloper();
                            }

                            if (detail == null) {
                                log.warn("Detail payload was null for PropertyFinder developer: {} – falling back to summary", summary.getSlug());
                                // Fallback to summary-only conversion
                                convertSummaryToDeveloperModel(summary).ifPresent(model -> {
                                    Map<String, Object> meta = createMetadata(summary, null);
                                    result.add(wrapInCrawlRecord(model, meta));
                                });
                                continue;
                            }

                            details = Optional.of(detail);
                            log.debug("Successfully fetched detail payload for PropertyFinder developer: {}", summary.getSlug());

                        } catch (feign.FeignException.NotFound nf) {
                            log.debug("Developer landing page not found for slug {} – falling back to summary", summary.getSlug());
                            // Fallback to summary-only conversion
                            convertSummaryToDeveloperModel(summary).ifPresent(model -> {
                                Map<String, Object> meta = createMetadata(summary, null);
                                result.add(wrapInCrawlRecord(model, meta));
                            });
                            continue;
                        } catch (Exception ex) {
                            log.error("Error fetching developer details for {}: {} – falling back to summary", summary.getSlug(), ex.getMessage(), ex);
                            // Attempt summary fallback conversion
                            convertSummaryToDeveloperModel(summary).ifPresent(model -> {
                                Map<String, Object> meta = createMetadata(summary, null);
                                result.add(wrapInCrawlRecord(model, meta));
                            });
                            continue;
                        }
                    }

                    DeveloperConversionResult convResult = convert(summary, details);
                    if (convResult.developerModel().isPresent()) {
                        DeveloperModel developer = convResult.developerModel().get();
                        CrawlRecord<DeveloperModel> record = new CrawlRecord<>(
                                developer,
                                convResult.rawApiMetadata(),
                                PropertyFinderCommon.SOURCE_URN,
                                developer.getDeveloperUrn()
                        );
                        result.add(record);

                        // Success log per spec §17
                        log.info("Successfully processed PropertyFinder developer: {} (id: {})", developer.getTitle(), developer.getExternalId());
                    } else {
                        log.warn("Failed to convert developer to DeveloperModel: {}", summary.getName());
                    }
                } catch (Exception ex) {
                    log.error("Error processing Property Finder developer {}: {}", summary.getName(), ex.getMessage(), ex);
                    // Final attempt with summary fallback conversion
                    try {
                        convertSummaryToDeveloperModel(summary).ifPresent(model -> {
                            Map<String, Object> meta = createMetadata(summary, null);
                            result.add(wrapInCrawlRecord(model, meta));
                        });
                    } catch (Exception fallbackEx) {
                        log.error("Even summary fallback conversion failed for developer {}: {}", summary.getName(), fallbackEx.getMessage(), fallbackEx);
                    }
                }
            }

            return result;

        } catch (feign.FeignException.NotFound nf) {
            // Page not found indicates end of pagination – return empty list
            log.info("Developer page {} not found – end of pagination", page);
            return java.util.Collections.emptyList();
        } catch (Exception e) {
            log.error("Error fetching Property Finder developer page {}: {}", page, e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching Property Finder developer page " + page, e);
        } finally {
            // Page-level completion log regardless of outcome
            log.info("Successfully processed developer page {} with {} developers", page, result.size());
        }
    }

    /**
     * Converts a developer summary (and optional details) to DeveloperModel.
     */
    private DeveloperConversionResult convert(DeveloperSummary summary, Optional<DeveloperDetails> detailsOpt) {
        Map<String, Object> metadata = new HashMap<>();
        Map<String, Object> raw = new HashMap<>();
        raw.put("developer_summary", summary);
        detailsOpt.ifPresent(d -> raw.put("developer_details", d));
        metadata.put("raw_data", raw);
        metadata.put("conversion_timestamp", Instant.now().toString());
        metadata.put("source_api", "propertyfinder");

        try {
            Optional<DeveloperModel> modelOpt = toDeveloperModel(summary, detailsOpt);
            return new DeveloperConversionResult(modelOpt, metadata);
        } catch (Exception e) {
            log.error("Error converting Property Finder developer {}: {}", summary.getName(), e.getMessage(), e);
            metadata.put("conversion_error", e.getMessage());
            return new DeveloperConversionResult(Optional.empty(), metadata);
        }
    }

    private Optional<DeveloperModel> toDeveloperModel(DeveloperSummary summary, Optional<DeveloperDetails> detailsOpt) {
        // Basic validation
        if (summary.getId() == null || summary.getName() == null || summary.getName().isBlank()) {
            log.warn("Developer summary incomplete – id or name missing, skipping");
            return Optional.empty();
        }

        DeveloperDetails details = detailsOpt.orElse(null);

        String slug = summary.getSlug() != null ? summary.getSlug() : slugify(summary.getName());
        String developerUrn = PropertyFinderCommon.Developer.urn(slug);

        var builder = DeveloperModel.builder();
        builder.withDeveloperUrn(developerUrn)
                .withSourceUrn(PropertyFinderCommon.SOURCE_URN)
                .withTitle(summary.getName())
                .withExternalId(summary.getId());

        // Prefer description from details, fallback to summary
        if (details != null && details.getDescription() != null && !details.getDescription().isBlank()) {
            String cleanDescription = StringUtils.stripHtml(details.getDescription());
            if (cleanDescription != null && !cleanDescription.isBlank()) {
                builder.withDescription(cleanDescription);
            }
        } else if (summary.getDescription() != null && !summary.getDescription().isBlank()) {
            String cleanDescription = StringUtils.stripHtml(summary.getDescription());
            if (cleanDescription != null && !cleanDescription.isBlank()) {
                builder.withDescription(cleanDescription);
            }
        }

        // Logo / cover image
        if (details != null && details.getLogoUrl() != null && !details.getLogoUrl().isBlank()) {
            builder.withLogoUrl(details.getLogoUrl());
        } else if (summary.getLogoUrl() != null && !summary.getLogoUrl().isBlank()) {
            builder.withLogoUrl(summary.getLogoUrl());
        }

        // Founded / established date
        if (summary.getEstablishedSince() != null) {
            LocalDate founded = summary.getEstablishedSince().toLocalDate();
            builder.withFounded(founded.toString());
        }

        // Developer stats – currently only numProjectsOnline
        if (summary.getNumProjectsOnline() != null) {
            var statsBuilder = com.realmond.etl.model.DeveloperStats.builder();
            statsBuilder.withNotCompletedProjectsCount(summary.getNumProjectsOnline());
            builder.withDeveloperStats(statsBuilder.build());
        }

        // Additional data
        Map<String, Object> additional = new HashMap<>();
        additional.put("dev_page_enabled", summary.getDevPageEnabled());
        additional.put("slug", slug);
        if (details != null && details.getContactOptions() != null) {
            additional.put("contact_options", details.getContactOptions());
        }
        if (!additional.isEmpty()) {
            builder.withAdditionalData(additional);
        }

        return Optional.of(builder.build());
    }

    /**
     * Fallback conversion using only the developer {@link DeveloperSummary} when the
     * detailed API call fails.
     */
    private Optional<DeveloperModel> convertSummaryToDeveloperModel(DeveloperSummary summary) {
        if (summary == null) {
            return Optional.empty();
        }

        try {
            // Basic validation
            if (summary.getId() == null || summary.getName() == null || summary.getName().isBlank()) {
                log.warn("Developer summary incomplete – id or name missing, skipping");
                return Optional.empty();
            }

            String slug = summary.getSlug() != null ? summary.getSlug() : slugify(summary.getName());
            String developerUrn = PropertyFinderCommon.Developer.urn(slug);

            var builder = DeveloperModel.builder();
            builder.withDeveloperUrn(developerUrn)
                    .withSourceUrn(PropertyFinderCommon.SOURCE_URN)
                    .withTitle(summary.getName())
                    .withExternalId(summary.getId());

            // Description from summary only
            if (summary.getDescription() != null && !summary.getDescription().isBlank()) {
                String cleanDescription = StringUtils.stripHtml(summary.getDescription());
                if (cleanDescription != null && !cleanDescription.isBlank()) {
                    builder.withDescription(cleanDescription);
                }
            }

            // Logo / cover image from summary only
            if (summary.getLogoUrl() != null && !summary.getLogoUrl().isBlank()) {
                builder.withLogoUrl(summary.getLogoUrl());
            }

            // Founded / established date
            if (summary.getEstablishedSince() != null) {
                LocalDate founded = summary.getEstablishedSince().toLocalDate();
                builder.withFounded(founded.toString());
            }

            // Developer stats – currently only numProjectsOnline
            if (summary.getNumProjectsOnline() != null) {
                var statsBuilder = com.realmond.etl.model.DeveloperStats.builder();
                statsBuilder.withNotCompletedProjectsCount(summary.getNumProjectsOnline());
                builder.withDeveloperStats(statsBuilder.build());
            }

            // Additional data
            Map<String, Object> additional = new HashMap<>();
            additional.put("dev_page_enabled", summary.getDevPageEnabled());
            additional.put("slug", slug);
            // Note: no contact_options since we don't have details
            if (!additional.isEmpty()) {
                builder.withAdditionalData(additional);
            }

            return Optional.of(builder.build());

        } catch (Exception ex) {
            log.error("Error converting DeveloperSummary to DeveloperModel: {}", ex.getMessage(), ex);
            return Optional.empty();
        }
    }

    private CrawlRecord<DeveloperModel> wrapInCrawlRecord(
            DeveloperModel model,
            Map<String, Object> metadata
    ) {
        return new CrawlRecord<>(model, metadata, model.getSourceUrn(), model.getDeveloperUrn());
    }

    private Map<String, Object> createMetadata(
            DeveloperSummary summary,
            DeveloperDetails detail
    ) {
        Map<String, Object> meta = new HashMap<>();
        Map<String, Object> raw = new HashMap<>();
        raw.put("developer_summary", summary);
        if (detail != null) {
            raw.put("developer_details", detail);
        }
        meta.put("raw_data", raw);
        meta.put("conversion_timestamp", Instant.now().toString());
        meta.put("source_api", "propertyfinder");
        meta.put("api_version", "v1");
        return meta;
    }

    /**
     * Creates a URL-safe slug from a name string.
     */
    private String slugify(String name) {
        if (name == null || name.isBlank()) {
            return "unknown";
        }
        return name.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")
                .replaceAll("\\s+", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");
    }
} 