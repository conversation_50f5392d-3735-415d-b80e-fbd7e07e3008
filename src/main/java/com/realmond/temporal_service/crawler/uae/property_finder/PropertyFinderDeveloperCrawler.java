package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.IntStream;

/**
 * Implementation of PropertyFinder Developer Crawler.
 * Uses {@link PropertyFinderDeveloperApiService} to fetch and parse developer data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PropertyFinderDeveloperCrawler implements Crawler<DeveloperModel> {

    private final PropertyFinderDeveloperApiService apiService;

    @Override
    public String getSourceUrn() {
        return PropertyFinderCommon.SOURCE_URN;
    }

    @Override
    public Boolean supportsPagination() {
        return true;
    }

    @Override
    public List<String> fetchAllPageIds() {
        int pagesCount = apiService.fetchDeveloperPagesCount();
        return IntStream.range(1, pagesCount + 1)
                .boxed()
                .map(Object::toString)
                .toList();
    }

    @Override
    public List<CrawlRecord<DeveloperModel>> parsePage(String pageNum) {
        log.info("Parsing Property Finder developer page with ID: {}", pageNum);
        int page;
        try {
            page = Integer.parseInt(pageNum);
        } catch (NumberFormatException e) {
            throw new NonRetryableCrawlerException("failed to parse page number: " + pageNum, e);
        }

        return apiService.fetchDevelopersPage(page);
    }

    @Override
    public List<CrawlRecord<DeveloperModel>> fetchAll() {
        throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
    }
} 