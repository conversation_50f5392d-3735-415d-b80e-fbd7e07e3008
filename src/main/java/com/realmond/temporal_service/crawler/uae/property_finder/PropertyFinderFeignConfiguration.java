package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.rate_limit.BucketRateLimiter;
import feign.RequestInterceptor;
import feign.Retryer;
import feign.codec.Decoder;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Random;

/**
 * Shared Feign configuration for the Property&nbsp;Finder HTTP client.
 * <p>
 * Adds realistic browser headers via {@link FingerprintGenerator}, provides a
 * rate-limiter and wires a custom {@link Decoder} that converts the HTML pages
 * to strongly typed POJOs.
 */
@Slf4j
@Configuration
@com.realmond.temporal_service.config.FeignModuleConfiguration
public class PropertyFinderFeignConfiguration {

    private final FingerprintGenerator fingerprintGenerator;
    private final PropertyFinderSettings settings;
    private final ObjectMapper objectMapper;
    private final Random random = new Random();
    private final BucketRateLimiter rateLimiter;

    public PropertyFinderFeignConfiguration(
            FingerprintGenerator fingerprintGenerator,
            PropertyFinderSettings settings,
            ObjectMapper objectMapper,
            ObjectProvider<ProxyManager<String>> proxyManagerProvider) {
        this.fingerprintGenerator = fingerprintGenerator;
        this.settings = settings;
        this.objectMapper = objectMapper;
        ProxyManager<String> proxyManager = proxyManagerProvider.getIfAvailable();
        this.rateLimiter = new BucketRateLimiter(settings.getRateLimiter(), proxyManager);
    }

    /**
     * Supplies the custom decoder that converts HTML pages to POJOs.
     */
    @Bean
    public Decoder propertyFinderDecoder() {
        return new PropertyFinderHtmlDecoder(objectMapper);
    }

    /**
     * Adds browser-like headers and applies client-level rate limiting.
     */
    @Bean
    public RequestInterceptor propertyFinderRequestInterceptor() {
        return template -> {
            if (rateLimiter != null) {
                rateLimiter.consume(PropertyFinderCommon.SOURCE_URN);
            }
            fingerprintGenerator.applyBrowserHeaders(template);
            addRandomDelay();
        };
    }

    /**
     * Configures an exponential back-off retry policy.
     */
    @Bean
    public Retryer propertyFinderRetryer() {
        return new Retryer.Default(
                settings.getRetry().getRetryDelayMs(),
                settings.getRetry().getMaxPeriodMs(),
                settings.getRetry().getMaxRetries());
    }

    private void addRandomDelay() {
        try {
            int delay = random.nextInt(settings.getRetry().getRandomDelayCapMs());
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Request delay interrupted", e);
        }
    }
} 