package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.temporal_service.crawler.uae.property_finder.model.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Typed Feign client for scraping Property&nbsp;Finder public pages. All
 * endpoints return a full HTML document which is converted to strongly typed
 * POJOs by {@link PropertyFinderHtmlDecoder}.
 * <p>
 * <strong>Developer pages:</strong> not every developer has a dedicated
 * "dev-lp" landing page. When the <code>/dev-lp/{developerSlug}</code>
 * endpoint returns HTTP&nbsp;404 the crawler should fall back to the
 * information embedded in the project-list payload ({@link ProjectSummary#developer}).
 * This client surfaces the 404 as a {@link feign.FeignException.NotFound};
 * callers are therefore expected to catch that exception and use the summary
 * information as a fallback.
 */
@FeignClient(
        name = "property-finder-web",
        url = "${crawler.uae.property-finder.base-url:https://www.propertyfinder.ae}",
        configuration = PropertyFinderFeignConfiguration.class
)
public interface PropertyFinderFeignRestClient {

    /**
     * Fetches a paginated list of new developer projects.
     *
     * Sample URL: <pre>https://www.propertyfinder.ae/en/new-projects?page=2</pre>
     *
     * @param pageNum 1-based page index
     * @return Wrapper containing the {@link SearchResultPageProps}
     */
    @GetMapping("/en/new-projects")
    NextJsWrapper<SearchResultPageProps> getNewProjects(@RequestParam("page") int pageNum);

    /**
     * Fetches project details for a given developer and project slug.
     *
     * Sample URL: <pre>https://www.propertyfinder.ae/en/new-projects/emaar/some-project</pre>
     */
    @GetMapping("/en/new-projects/{developerSlug}/{projectSlug}")
    NextJsWrapper<ProjectDetailsPageProps> getProjectDetails(
            @PathVariable("developerSlug") String developerSlug,
            @PathVariable("projectSlug") String projectSlug);

    /**
     * Fetches developer landing-page information.
     *
     * Sample URL: <pre>https://www.propertyfinder.ae/en/new-projects/dev-lp/emaar</pre>
     */
    @GetMapping("/en/new-projects/dev-lp/{developerSlug}")
    NextJsWrapper<DeveloperPageProps> getDeveloperDetails(
            @PathVariable("developerSlug") String developerSlug);

    /**
     * Fetches a paginated list of developers operating in the UAE.
     *
     * Sample URL: <pre>https://www.propertyfinder.ae/en/new-projects/dev-list/uae?page=2</pre>
     *
     * @param pageNum 1-based page index
     * @return Wrapper containing the {@link DevelopersPageProps}
     */
    @GetMapping("/en/new-projects/dev-list/uae")
    NextJsWrapper<DevelopersPageProps> getDevelopers(@RequestParam("page") int pageNum);
} 