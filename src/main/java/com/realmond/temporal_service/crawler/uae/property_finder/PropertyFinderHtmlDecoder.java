package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import feign.Response;
import feign.codec.Decoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * Feign {@link Decoder} that extracts the JSON payload embedded in the HTML
 * returned by Property&nbsp; Finder pages.
 * <p>
 * The HTML contains a <code>&lt;script id="__NEXT_DATA__"&gt;</code> element
 * whose text content holds the JSON representation generated by Next.js. This
 * decoder locates the script tag, parses its content and deserialises it to
 * the expected return type using the shared {@link ObjectMapper}.
 */
@Slf4j
@RequiredArgsConstructor
public class PropertyFinderHtmlDecoder implements Decoder {

    private final ObjectMapper objectMapper;

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        if (response.body() == null) {
            return null;
        }

        // Read full HTML response body
        String html;
        try (InputStream is = response.body().asInputStream()) {
            html = new String(is.readAllBytes(), StandardCharsets.UTF_8);
        }

        // Parse HTML and locate the Next.js script element
        Document document = Jsoup.parse(html);
        Element nextData = document.getElementById("__NEXT_DATA__");
        if (nextData == null) {
            log.warn("Decoder could not find __NEXT_DATA__ element in response");
            throw FeignException.errorStatus("Missing __NEXT_DATA__", response);
        }

        String json = nextData.data(); // <script type="application/json"> places JSON inside .data()
        if (json == null || json.isBlank()) {
            // Fallback – some versions embed JSON as HTML text
            json = nextData.html();
        }

        if (json == null || json.isBlank()) {
            log.warn("__NEXT_DATA__ element did not contain JSON payload");
            throw FeignException.errorStatus("Empty __NEXT_DATA__ payload", response);
        }

        // Map JSON to the expected Java type
        JavaType javaType = objectMapper.getTypeFactory().constructType(type);
        return objectMapper.readValue(json, javaType);
    }
} 