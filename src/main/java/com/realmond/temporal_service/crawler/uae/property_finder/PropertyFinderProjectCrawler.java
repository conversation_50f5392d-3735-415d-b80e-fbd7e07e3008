package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.property_finder.PropertyFinderCommon;
import com.realmond.temporal_service.crawler.uae.property_finder.PropertyFinderApiService;
import com.realmond.etl.model.ProjectModel;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Collections;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Entry point crawler for Property&nbsp;Finder project data.
 *
 * <p>
 * This class currently only contains method stubs. The implementation will be
 * completed in subsequent backlog tasks.
 * </p>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PropertyFinderProjectCrawler implements Crawler<ProjectModel> {

    private final PropertyFinderApiService apiService;

    @Override
    /**
     * Returns the constant URN identifying the Property&nbsp;Finder AE data source.
     * <p>
     * The value is defined in {@link PropertyFinderCommon#SOURCE_URN} and is
     * used across the crawler stack and downstream pipelines to label records that
     * originate from Property&nbsp;Finder.
     * </p>
     *
     * @return {@code "urn:source:propertyfinder:ae"}
     */
    public String getSourceUrn() {
        // Will be finalised in later tasks (§5 of spec)
        return PropertyFinderCommon.SOURCE_URN;
    }

    @Override
    /**
     * Indicates that the Property&nbsp;Finder catalogue endpoint supports page-based
     * navigation.  Always returns {@code true} so that the framework invokes
     * {@link #fetchAllPageIds()} prior to calling {@link #parsePage(String)}.
     *
     * @return {@code true}
     */
    public Boolean supportsPagination() {
        // PropertyFinder supports pagination, implementation to follow
        return Boolean.TRUE;
    }

    @Override
    /**
     * Discovers the total amount of available catalogue pages by delegating to
     * {@link PropertyFinderApiService#getTotalPages()}.  The method follows the
     * optimisation rules defined in spec&nbsp;§2.3:
     *
     * <ol>
     *   <li>Probe page&nbsp;1 once and read {@code pagination.total}.</li>
     *   <li>If the value is {@code null} or &lt;=1, return an empty list so the
     *       framework will crawl only page&nbsp;1.</li>
     *   <li>Otherwise return a list of stringified integers "1"…"n".</li>
     * </ol>
     *
     * All retryable exceptions are propagated unchanged so that the job runner
     * can apply exponential back-off.
     *
     * @return list of additional page identifiers or empty list.
     */
    public List<String> fetchAllPageIds() {
        try {
            Integer totalPages = apiService.getTotalPages();

            if (totalPages == null) {
                log.warn("pagination.total not present – assuming single-page catalogue – returning empty page list");
                return Collections.emptyList();
            }

            if (totalPages == null || totalPages <= 1) {
                // Either single page or unknown – returning empty list signals
                // "only page 1" (parsePage("1") will still work) or no pages.
                log.info("PropertyFinder reported {} total pages – no additional pages to crawl", totalPages);
                return Collections.emptyList();
            }

            log.info("Discovered {} total pages for PropertyFinder", totalPages);

            return IntStream.rangeClosed(1, totalPages)
                    .mapToObj(String::valueOf)
                    .collect(Collectors.toList());
        } catch (com.realmond.temporal_service.crawler.err.RetryableCrawlerException e) {
            // propagate retryable exceptions as-is
            throw e;
        } catch (Exception e) {
            throw new com.realmond.temporal_service.crawler.err.RetryableCrawlerException("Error discovering pagination", e);
        }
    }

    @Override
    /**
     * Parses a single catalogue page.
     * <p>
     * The crawler framework supplies the page identifier discovered via
     * {@link #fetchAllPageIds()}. The method validates that the identifier is
     * numeric, converts it to {@code int}, then delegates the heavy lifting to
     * {@link PropertyFinderApiService#fetchProjectsPage(int)}.
     * </p>
     *
     * @param pageIdentifier page number (1-based) as provided by the framework
     * @return list of {@link CrawlRecord}s containing {@link com.realmond.etl.model.ProjectModel}s
     * @throws NonRetryableCrawlerException when the identifier cannot be parsed
     */
    public List<CrawlRecord<ProjectModel>> parsePage(String pageIdentifier) {
        log.info("Parsing PropertyFinder page with ID: {}", pageIdentifier);

        int page;
        try {
            page = Integer.parseInt(pageIdentifier);
        } catch (NumberFormatException e) {
            throw new NonRetryableCrawlerException("failed to parse page number: " + pageIdentifier, e);
        }

        return apiService.fetchProjectsPage(page);
    }

    @Override
    /**
     * Bulk fetching is explicitly not supported by this crawler because the API
     * exposes a paginated catalogue only.  Invoking this method will therefore
     * always raise {@link NonRetryableCrawlerException#PARSE_ALL_PAGES_NOT_SUPPORTED}.
     *
     * @throws NonRetryableCrawlerException always
     */
    public List<CrawlRecord<ProjectModel>> fetchAll() {
        throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
    }
} 