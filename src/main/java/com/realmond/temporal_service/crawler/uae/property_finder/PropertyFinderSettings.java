package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.temporal_service.crawler.DefaultCrawlerSettings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for the Property&nbsp;Finder crawler module.
 * <p>
 * Values can be overridden in <code>application.yaml</code> using the prefix
 * <pre>crawler.uae.property-finder</pre>.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Component
@ConfigurationProperties(prefix = "crawler.uae.property-finder")
public class PropertyFinderSettings extends DefaultCrawlerSettings {

    /** Base URL of the public Property&nbsp;Finder website */
    private String baseUrl = "https://www.propertyfinder.ae";
} 