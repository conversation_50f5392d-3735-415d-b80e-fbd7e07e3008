package com.realmond.temporal_service.crawler.uae.property_finder.legacy;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.client.PropertyFinderClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ParseProjectDetailActivityImpl implements ParseProjectDetailActivity {

    private final ProjectDetailParser projectDetailParser;
    private final PropertyFinderClient propertyFinderClient;

    @Override
    public Optional<ProjectModel> parseProjectDetail(String htmlContent, String projectUrl) {
        log.info("Parsing project details for URL: {}", projectUrl);
        Optional<ProjectModel> projectModel = projectDetailParser.parseProjectDetails(htmlContent, projectUrl);
        if (projectModel.isPresent()) {
            log.info("Successfully parsed project details for URL: {}", projectUrl);
        } else {
            log.warn("Failed to parse project details for URL: {}", projectUrl);
        }
        return projectModel;
    }

    public Optional<ProjectModel> fetchAndParseProjectDetail(String shareUrl) {
        String htmlContent = propertyFinderClient.fetchProjectDetail(shareUrl);
        return parseProjectDetail(htmlContent, shareUrl);
    }
}
