package com.realmond.temporal_service.crawler.uae.property_finder.legacy;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.parser.common.ParserActivity;
import io.temporal.activity.ActivityInterface;

/**
 * Activity interface for parsing PropertyFinder projects.
 * Extends the common ParserActivity interface with ProjectModel as the parsed entity type.
 */
@ActivityInterface
public interface PropertyFinderParserActivity extends ParserActivity<ProjectModel> {
    // Inherits methods from ParserActivity
}
