package com.realmond.temporal_service.crawler.uae.property_finder.legacy;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.ProjectRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.ProjectRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PropertyFinderService {

    private final ParseProjectsListActivity parseProjectsListActivity;
    private final ParseProjectDetailActivity parseProjectDetailActivity;
    private final ProjectRepository projectRepository;

    public PropertyFinderService(ParseProjectsListActivity parseProjectsListActivity,
                                 ParseProjectDetailActivity parseProjectDetailActivity,
                                 ProjectRepository projectRepository) {

        this.parseProjectsListActivity = parseProjectsListActivity;
        this.parseProjectDetailActivity = parseProjectDetailActivity;
        this.projectRepository = projectRepository;
    }

    public void processAllProjects() {
        int pageNumber = 1;
        boolean hasMorePages = true;

        while (hasMorePages) {
            log.info("Processing page {}", pageNumber);

            // Fetch and parse projects list for current page
            List<String> shareUrls = parseProjectsListActivity.fetchAndParseProjectsList(pageNumber);

            if (shareUrls.isEmpty()) {
                log.info("No more projects found on page {}. Stopping pagination.", pageNumber);
                hasMorePages = false;
                continue;
            }

            log.info("Found {} projects on page {}", shareUrls.size(), pageNumber);

            // Process each project on the current page
            for (String shareUrl : shareUrls) {
                try {
                    Optional<ProjectModel> projectModel = parseProjectDetailActivity.fetchAndParseProjectDetail(shareUrl);
                    if (projectModel.isPresent()) {
                        log.info("Successfully processed project: {}", shareUrl);
                        ProjectRecord record = new ProjectRecord();
                        record.setUrn(projectModel.get().getProjectUrn());
                        record.setData(projectModel.get());
                        record.setEmittedAt(ZonedDateTime.now());
                        projectRepository.save(record);
                    } else {
                        log.warn("Failed to process project: {}", shareUrl);
                    }
                } catch (Exception e) {
                    log.error("Error processing project {}: {}", shareUrl, e.getMessage(), e);
                    // Continue with next project even if one fails
                }
            }

            pageNumber++;
        }

        log.info("Finished processing all projects");
    }
}
