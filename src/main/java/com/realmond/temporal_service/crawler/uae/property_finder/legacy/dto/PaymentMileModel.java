package com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Represents a milestone within a payment phase.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@SuperBuilder
@NoArgsConstructor
public class PaymentMileModel {

    @JsonProperty("label")
    private String label;

    @JsonProperty("value")
    // Using Double as the JSON value might represent percentages or amounts not always whole numbers.
    private Double value;

}
