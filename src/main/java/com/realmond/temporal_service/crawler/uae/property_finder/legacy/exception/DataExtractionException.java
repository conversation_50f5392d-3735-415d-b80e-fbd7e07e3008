package com.realmond.temporal_service.crawler.uae.property_finder.legacy.exception;

/**
 * Exception thrown when there is an error extracting data from HTML or JSON.
 */
public class DataExtractionException extends ParserException {

    /**
     * Creates a new DataExtractionException with the specified message.
     *
     * @param message The error message
     */
    public DataExtractionException(String message) {
        super(message);
    }

    /**
     * Creates a new DataExtractionException with the specified message and cause.
     *
     * @param message The error message
     * @param cause The cause of the exception
     */
    public DataExtractionException(String message, Throwable cause) {
        super(message, cause);
    }
}
