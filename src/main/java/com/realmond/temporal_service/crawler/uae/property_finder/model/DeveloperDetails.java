package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Represents the subset of developer information returned by the Property Finder
 * "developer-details" endpoint that is currently required by the crawler.
 * <p>
 * Only the observed fields are modelled – any additional/unknown properties in
 * the payload will be ignored by <PERSON> (via global ObjectMapper settings).
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeveloperDetails {

    /** Human-readable developer name (e.g. "Sobha Realty"). */
    private String name;

    /** Unique identifier as returned by the API (UUID). */
    private String id;

    /** HTML description/biography of the developer. */
    private String description;

    /** Flag indicating whether the dedicated developer landing page is enabled. */
    @JsonProperty("devPageEnabled")
    private Boolean devPageEnabled;

    /** Absolute URL of the cover image shown on the landing page. */
    private String coverImageUrl;

    /** Absolute URL of the developer logo. */
    private String logoUrl;

    /** Contact options provided by the platform (e-mail, WhatsApp, …). */
    private ContactOptions contactOptions;

    /* --------------------------------------------------------------------- */
    /*                               NESTED POJOs                            */
    /* --------------------------------------------------------------------- */

    /**
     * Encapsulates the supported contact channels for the developer.
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ContactOptions {
        private Boolean email;
        private String whatsapp;
    }
} 