package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Models the {@code props.pageProps} portion of the Property&nbsp;Finder
 * developer-detail Next.js payload.
 *
 * <pre>
 * {
 *   "props": {
 *     "pageProps": {
 *       "devResult": { ... }
 *     }
 *   }
 * }
 * </pre>
 *
 * Only the fields currently required by the crawler are exposed. Unknown/new
 * attributes are ignored by <PERSON>.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeveloperPageProps {

    @JsonProperty("devResult")
    private DevResult devResult;

    /** Container of the actual developer details object. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DevResult {
        private DeveloperDetails developer;
    }
} 