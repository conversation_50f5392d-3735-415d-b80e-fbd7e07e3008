package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * Represents a single developer entry as returned by the Property&nbsp;Finder
 * "developers" listing endpoint.
 * <p>
 * The class purposefully models <em>all</em> attributes currently present in the
 * observed JSON (see {@code src/test/resources/crawler/uae/propertyfinder/developers.json}).
 * Additional/unknown properties will be ignored by <PERSON> so that the crawler
 * remains resilient to front-end changes.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeveloperSummary {

    /* --------------------------------------------------------------------- */
    /*                              PRIMITIVES                               */
    /* --------------------------------------------------------------------- */

    /** Unique identifier as returned by the API (UUID). */
    private String id;

    /** Human-readable developer name (e.g. "Emaar"). */
    private String name;

    /** Slug used within the URL structure (e.g. "emaar"). */
    private String slug;

    /** Flag indicating whether the dedicated developer landing page is enabled. */
    @JsonProperty("devPageEnabled")
    private Boolean devPageEnabled;

    /** HTML description/biography of the developer. */
    private String description;

    /** Foundation date of the developer (ISO-8601 string). */
    private OffsetDateTime establishedSince;

    /** Absolute URL of the developer logo. */
    private String logoUrl;

    /** Number of projects that are currently online on the platform. */
    private Integer numProjectsOnline;
} 