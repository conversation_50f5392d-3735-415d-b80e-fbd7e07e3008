package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Models the {@code props.pageProps} portion of the Property&nbsp;Finder
 * developers-list Next.js payload.
 *
 * <pre>
 * {
 *   "props": {
 *     "pageProps": {
 *       "developersResult": { ... }
 *     }
 *   }
 * }
 * </pre>
 *
 * All currently observed fields are exposed to allow for full deserialization
 * while remaining forward-compatible thanks to {@link JsonIgnoreProperties}.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DevelopersPageProps {

    @JsonProperty("developersResult")
    private DevelopersResult developersResult;

    /* ----------------------------------------------------------------- */
    /*                              INTERNAL                             */
    /* ----------------------------------------------------------------- */

    /** Container holding the data array and meta information. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DevelopersResult {
        private List<DeveloperSummary> data;
        private Meta meta;
    }

    /** Meta information accompanying the response (total counts, paging). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Meta {
        private Integer count;
        private Pagination pagination;
    }

    /** Simple pagination descriptor. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Pagination {
        private Integer page;
        private Integer total;
    }
} 