package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * Generic wrapper for JSON payloads returned by Property Finder's Next.js
 * endpoints.
 * <p>
 * The typical structure of such responses is:
 * <pre>
 * {
 *   "props": {
 *     "pageProps": { ... }
 *   }
 * }
 * </pre>
 * <p>
 * The {@code pageProps} object may itself contain another level of indirection
 * (e.g. {@code devResult}, {@code searchResult}, …) which can be modelled by a
 * dedicated POJO that serves as the type parameter {@code P}.
 *
 * @param <P> Java type representing the object held under {@code props.pageProps}.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class NextJsWrapper<P> {

    private Props<P> props;

    /** Container for the {@code pageProps} field. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Props<P> {
        @JsonProperty("pageProps")
        private P pageProps;
    }
} 