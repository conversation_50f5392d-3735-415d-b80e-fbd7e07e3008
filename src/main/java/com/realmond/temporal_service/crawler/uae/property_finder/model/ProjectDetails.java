package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * Represents the complete payload returned by the Property&nbsp;Finder "project-detail"
 * endpoint (a.k.a. {@code detailResult}).
 * <p>
 * All attributes currently observed in the sample JSON fixtures are exposed so
 * that the object can be fully deserialized. Any additional / unknown
 * properties will be ignored by <PERSON> ensuring forwards compatibility.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectDetails {

    /* --------------------------------------------------------------------- */
    /*                              PRIMITIVES                               */
    /* --------------------------------------------------------------------- */

    private String id;
    private String title;
    private String description;
    private String slug;
    private String campaignType;
    private String constructionPhase;
    private String constructionProgress;
    private OffsetDateTime deliveryDate;
    private OffsetDateTime salesStartDate;
    private Integer startingPrice;
    private Integer minResalePrice;
    private String ownershipType;
    private String salesPhase;
    private String stockAvailability;
    private Integer hotnessLevel;
    private OffsetDateTime lastInspectionDate;

    /* --------------------------------------------------------------------- */
    /*                               COMPLEXES                               */
    /* --------------------------------------------------------------------- */

    private Agencies agencies;
    private List<Amenity> amenities;
    private List<ContactOption> contactOptions;
    private Developer developer;
    private List<Faq> faqs;
    private List<GalleryItem> images;
    private Location location;
    private List<LocationNode> locationTree;
    private MasterPlan masterPlan;
    private List<PaymentPlan> paymentPlans;
    private List<String> propertyTypes;
    private List<TimelinePhase> timelinePhases;
    private List<BuildingGroup> units;

    /** May carry additional information (e.g. serialized JSON object). */
    private Object leadCta;
    /** Optional URL pointing to a downloadable project brochure PDF. */
    private String brochureUrl;

    /* --------------------------------------------------------------------- */
    /*                               NESTED POJOs                            */
    /* --------------------------------------------------------------------- */

    /** Wrapper object holding agency lists. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Agencies {
        private List<Agency> random;
        private List<Agency> all;
    }

    /** Minimal representation of an agency – currently unknown structure. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Agency {
        // Intentionally left empty – will be populated once structure is known.
    }

    /** Simple id/name container used for the amenities list. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Amenity {
        private String id;
        private String name;
    }

    /** Supported contact channel for the project card/page (e.g. WhatsApp). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ContactOption {
        private String type;
        private String link;
    }

    /** In-depth developer object embedded inside the project detail payload. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Developer {
        private String id;
        private String name;
        private String slug;
        private String logoUrl;
        @JsonProperty("devPageEnabled")
        private Boolean devPageEnabled;
        private Integer numProjectsOnline;
    }

    /** Frequently asked question about the project. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Faq {
        private String question;
        private String answer;
    }

    /** Any item inside the gallery list – could be image, video, master-plan … */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GalleryItem {
        private String type;  // image, video, master-plan …
        private String source;
        private Variants variants; // May be null for video/master-plan
    }

    /** Holds resized image URLs (only present for type == image). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Variants {
        private String medium;
    }

    /** Geo-location and display name of the project. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        private Integer id;
        private String type;
        private String fullName;
        private String name;
        private Coordinates coordinates;
    }

    /** Latitude / longitude wrapper. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Coordinates {
        private Double lat;
        private Double lon; // "lon" duplicates "lng" in the payload.
        private Double lng;
    }

    /** Single breadcrumb of the location hierarchy. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LocationNode {
        private Integer id;
        private String type;
        private String path;
        private String name;
        private String slug;
    }

    /** Optional master plan description and illustrative image. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MasterPlan {
        private String description; // HTML
        private String image;       // Absolute URL (may be null)
    }

    /** Encapsulates a payment plan definition. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentPlan {
        private String title;
        private List<Phase> phases;
    }

    /** High-level payment phase (e.g. down-payment, handover, …). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Phase {
        private String label;
        private Integer value;
        private List<Mile> miles;
    }

    /** Detailed milestone inside a phase. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Mile {
        private String label;
        private Integer value;
    }

    /** Construction/launch timeline entry. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TimelinePhase {
        private String category;
        private Boolean completed;
        private String rawDate; // ISO-8601 provided by backend
        private String date;    // Formatted for frontend (dd/MM/yyyy)
        private String id;
        private String phaseKey;
        private Integer progressPercentage;
        private String title;
        private Boolean shouldShowProgressBar;
    }

    /** Top-level building group holding one or more unit sets. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BuildingGroup {
        private Object buildingInfo; // Not currently used – keep flexible.
        private List<UnitSet> units;
    }

    /** Set of units grouped by property type (apartment, villa, …). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UnitSet {
        private String propertyType;
        private List<Unit> list;
    }

    /** Individual bedroom/size aggregation entry. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Unit {
        private Integer bedrooms;
        private Integer areaFrom;
        private Integer areaTo;
        private Integer startingPrice;
        private List<Layout> layouts;
        private Integer totalUnits;
    }

    /** Floor-plan variant inside a unit. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Layout {
        private String layoutType; // e.g. "Type A", "apartment"
        private Integer bedrooms;
        private Integer area;
        private List<String> floorPlans; // Absolute URLs
    }
} 