package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Models the {@code props.pageProps} portion of the Property&nbsp;Finder
 * project-detail Next.js payload.
 *
 * <pre>
 * {
 *   "props": {
 *     "pageProps": {
 *       "detailResult": { ... }
 *     }
 *   }
 * }
 * </pre>
 *
 * The object held under {@code detailResult} is fully modelled by
 * {@link ProjectDetails}. Unknown / future attributes are ignored in order to
 * keep the crawler resilient against front-end changes.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectDetailsPageProps {

    /** Full project payload returned by the API. */
    @JsonProperty("detailResult")
    private ProjectDetails detailResult;
} 