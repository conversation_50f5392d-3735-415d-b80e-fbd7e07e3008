package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * Represents a single project entry as returned by the Property&nbsp;Finder
 * "new-projects" listing endpoint (developer list).
 * <p>
 * The class purposefully models <em>all</em> attributes currently present in the
 * observed JSON (see {@code src/test/resources/crawler/uae/propertyfinder/new-projects.json}).
 * Additional/unknown properties will be ignored by <PERSON> so that the crawler
 * remains resilient to front-end changes.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectSummary {

    /* --------------------------------------------------------------------- */
    /*                              PRIMITIVES                               */
    /* --------------------------------------------------------------------- */

    private String id;
    private String title;
    private String campaignType;
    private String constructionPhase;
    private String constructionProgress;
    private OffsetDateTime deliveryDate;
    private Integer hotnessLevel;
    private Integer weightRanking;
    private Integer startingPrice;
    private Integer minResalePrice;
    private Integer downPaymentPercentage;
    private String salesPhase;
    private OffsetDateTime salesStartDate;
    private String shareUrl;
    private String stockAvailability;

    /* --------------------------------------------------------------------- */
    /*                               COMPLEXES                               */
    /* --------------------------------------------------------------------- */

    private List<String> bedrooms;
    private List<String> images;
    private List<String> paymentPlans;
    private List<String> propertyTypes;

    @JsonProperty("leadCta")
    private Object leadCta; // Present but currently always null – keep as Object to remain flexible.

    private List<Amenity> amenities;
    private List<ContactOption> contactOptions;
    private DeveloperRef developer;
    private Location location;

    /* --------------------------------------------------------------------- */
    /*                               NESTED POJOs                            */
    /* --------------------------------------------------------------------- */

    /** Simple id/name container used for the amenities list. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Amenity {
        private String id;
        private String name;
    }

    /**
     * Encapsulates the supported contact channel for a project card (e.g. WhatsApp).
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ContactOption {
        private String type;
        private String link;
    }

    /** Shallow projection of the developer object inside the list payload. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeveloperRef {
        private String id;
        private String name;
        private String logoUrl;
    }

    /** Holds the geo-location and display name of the project. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        private Integer id;
        private String fullName;
        private Coordinates coordinates;
    }

    /** Latitude/longitude wrapper. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Coordinates {
        private Double lat;
        private Double lon; // "lon" duplicates "lng" in the payload.
        private Double lng;
    }
} 