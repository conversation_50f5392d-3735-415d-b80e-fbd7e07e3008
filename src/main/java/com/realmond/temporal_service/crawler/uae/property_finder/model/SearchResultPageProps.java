package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Models the {@code props.pageProps} portion of the "new-projects" list
 * endpoint returned by Property&nbsp;Finder.
 *
 * <pre>
 * {
 *   "props": {
 *     "pageProps": {
 *       "searchResult": { ... }
 *     }
 *   }
 * }
 * </pre>
 *
 * All currently observed fields are exposed to allow for full deserialization
 * while remaining forward-compatible thanks to {@link JsonIgnoreProperties}.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchResultPageProps {

    @JsonProperty("searchResult")
    private SearchResult searchResult;

    /* ----------------------------------------------------------------- */
    /*                              INTERNAL                             */
    /* ----------------------------------------------------------------- */

    /** Container holding the data array and meta information. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SearchResult {
        private SearchData data;
        private Meta meta;
    }

    /** Root data object ({@code data}). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SearchData {
        private Projects projects;
        private Object recommended; // Not used yet – keep raw for forward compatibility
    }

    /** Wrapper for the projects list, grouped by type. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Projects {
        private List<ProjectSummary> developer;
    }

    /** Meta information accompanying the response (total counts, paging). */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Meta {
        private Count count;
        private Pagination pagination;
    }

    /** Holds record counts. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Count {
        private Integer developer;
        private Integer resale;
        private Integer total;
    }

    /** Simple pagination descriptor. */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Pagination {
        private Integer page;
        private Integer total;
    }
} 