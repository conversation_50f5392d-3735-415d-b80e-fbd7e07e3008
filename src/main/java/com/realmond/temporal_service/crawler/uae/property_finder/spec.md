# Property Finder → ProjectModel Mapping Specification

> **Version:** 0.4  
> **Author:** Crawler team  
> **API Endpoint:** `GET /en/new-projects/{developerSlug}/{projectSlug}` (Next.js detailResult payload)

---

## 0  Relevant artefacts
The spec refers to the following existing classes / fixtures in the repository.

| Purpose | Path |
|---------|------|
| Source POJO (`detailResult`) | `src/main/java/com/realmond/temporal_service/crawler/uae/property_finder/model/ProjectDetails.java` |
| Target model | `target/generated-sources/jsonschema2pojo/com/realmond/etl/model/ProjectModel.java` |
| Feign client used for HTTP calls | `src/main/java/com/realmond/temporal_service/crawler/uae/property_finder/PropertyFinderFeignRestClient.java` |
| Legacy HTML-scraping implementation (reference) | `src/main/java/com/realmond/temporal_service/crawler/uae/property_finder/legacy/ProjectDetailParser.java` |
| ARO mapping template (reference for code style) | `src/main/java/com/realmond/temporal_service/crawler/uae/aro/AroApiService.java` |
| ARO crawler template (reference for architecture) | `src/main/java/com/realmond/temporal_service/crawler/uae/aro/AroProjectCrawler.java` |
| Floor-plan converter (reference for algorithm) | `src/main/java/com/realmond/temporal_service/crawler/uae/aro/AroFloorPlanConverter.java` |
| Unit conversion utilities (required for area calculations) | `src/main/java/com/realmond/temporal_service/crawler/common/UnitConversionUtils.java` |
| Sample payload #1 | `src/test/resources/crawler/uae/propertyfinder/project-details.json` |
| Sample payload #2 | `src/test/resources/crawler/uae/propertyfinder/project-details-2.json` |

These files **must not** be modified by the implementation – they serve as documentation, test fixtures or inspiration only.

---

## 1  Purpose
Implement a complete Property Finder project crawler that converts Property Finder *project-detail* JSON payloads into the canonical `com.realmond.etl.model.ProjectModel` following established crawler patterns.

---

## 2  Crawler Architecture

### 2.1  Component Structure
Following the ARO reference implementation, the PropertyFinder crawler shall consist of:

| Component | Purpose | Reference |
|-----------|---------|-----------|
| `PropertyFinderProjectCrawler` | Main crawler implementing `Crawler<ProjectModel>` | `AroProjectCrawler.java` |
| `PropertyFinderApiService` | Service layer for API calls and mapping | `AroApiService.java` |
| `PropertyFinderFeignRestClient` | Feign client for HTTP operations | Existing |

### 2.2  Crawler Interface Implementation
The `PropertyFinderProjectCrawler` must implement:

```java
@Override
public String getSourceUrn() {
    return "urn:source:propertyfinder:ae";
}

@Override
public Boolean supportsPagination() {
    return true;
}

@Override
public List<String> fetchAllPageIds() {
    // Implementation in §2.3
}

@Override
public List<CrawlRecord<ProjectModel>> parsePage(String pageNum) {
    // Implementation in §2.4
}

@Override
public List<CrawlRecord<ProjectModel>> fetchAll() {
    throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
}
```

### 2.3  Pagination Discovery

Property Finder exposes the total number of pages inside
`props.pageProps.searchResult.meta.pagination.total`. This value shall be
leveraged to avoid unnecessary HTTP round-trips.

1. **Initial Probe**: Fetch page 1 and extract `pagination.total`.
2. **Page List Construction**: Build a list containing the integers `1 … total`.
3. **Graceful Degradation**: When `pagination.total` is `null` or missing, the
   crawler SHALL assume that no additional pages are available and return an
   empty list. No sequential probing is performed.
4. **Error Handling**: Non-404 HTTP errors MUST throw
   `RetryableCrawlerException`.

```java
public List<String> fetchAllPageIds() {
    try {
        // Probe first page only once
        NextJsWrapper<SearchResultPageProps> wrapper = feignClient.getNewProjects(1);

        Integer totalPages = Optional.ofNullable(wrapper)
            .map(NextJsWrapper::getProps)
            .map(NextJsWrapper.Props::getPageProps)
            .map(SearchResultPageProps::getSearchResult)
            .map(SearchResultPageProps.SearchResult::getMeta)
            .map(SearchResultPageProps.Meta::getPagination)
            .map(SearchResultPageProps.Pagination::getTotal)
            .orElse(null);

        if (totalPages == null) {
            log.warn("pagination.total not present – assuming single-page catalogue – returning empty page list");
            return Collections.emptyList();
        }

        return IntStream.rangeClosed(1, totalPages)
                .mapToObj(String::valueOf)
                .collect(Collectors.toList());
    } catch (FeignException.NotFound notFound) {
        // Unlikely: page 1 not found – treat as empty catalogue
        log.info("PropertyFinder page 1 not found – no projects available");
        return Collections.emptyList();
    } catch (Exception e) {
        throw new RetryableCrawlerException("Error probing pagination", e);
    }
}
```

No additional probing logic is required.

### 2.4  Page Processing
The `parsePage(String pageNum)` method must:

1. **Parse Page Number**: Convert string to integer, throw `NonRetryableCrawlerException` on parse failure
2. **Delegate to Service**: Call `PropertyFinderApiService.fetchProjectsPage(int page)`
3. **Error Propagation**: Let service-level exceptions bubble up

```java
public List<CrawlRecord<ProjectModel>> parsePage(String pageNum) {
    log.info("Parsing PropertyFinder page with ID: {}", pageNum);
    int page;
    try {
        page = Integer.parseInt(pageNum);
    } catch (NumberFormatException e) {
        throw new NonRetryableCrawlerException("failed to parse page number: " + pageNum, e);
    }
    
    return apiService.fetchProjectsPage(page);
}
```

---

## 3  API Service Architecture

### 3.1  Service Layer Pattern
The `PropertyFinderApiService` follows the `AroApiService` pattern:

- **Error Handling**: Use `RetryableCrawlerException` for transient failures
- **Logging**: Structured logging with project identifiers
- **Metadata**: Wrap results in `CrawlRecord` with raw API metadata
- **Conversion**: Separate conversion logic from API interaction

### 3.2  Page Fetching Method
```java
public List<CrawlRecord<ProjectModel>> fetchProjectsPage(int page) {
    log.info("Fetching PropertyFinder page: {}", page);
    List<CrawlRecord<ProjectModel>> result = new ArrayList<>();
    
    try {
        // Fetch project list for page
        NextJsWrapper<SearchResultPageProps> wrapper = feignClient.getNewProjects(page);
        List<ProjectSummary> projects = wrapper.getProps().getPageProps()
            .getSearchResult().getData().getProjects().getDeveloper();
        
        if (projects == null || projects.isEmpty()) {
            log.warn("No projects found on page {}", page);
            return result;
        }
        
        log.info("Found {} projects on page {}", projects.size(), page);
        
        // Process each project
        for (ProjectSummary summary : projects) {
            try {
                // Fetch detailed project data
                ProjectDetails detail = feignClient.getProjectDetails(
                    summary.getDeveloperSlug(), 
                    summary.getProjectSlug()
                ).getProps().getPageProps().getDetailResult();
                
                // Convert to ProjectModel with metadata
                Optional<ProjectModel> projectModel = convertToProjectModel(detail);
                
                if (projectModel.isPresent()) {
                    CrawlRecord<ProjectModel> record = wrapInCrawlRecord(
                        projectModel.get(), 
                        createMetadata(summary, detail)
                    );
                    result.add(record);
                    
                    log.info("Successfully processed PropertyFinder project: {}/{}", 
                        summary.getDeveloperSlug(), summary.getProjectSlug());
                }
            } catch (Exception e) {
                log.error("Error processing project {}/{}: {}", 
                    summary.getDeveloperSlug(), summary.getProjectSlug(), 
                    e.getMessage(), e);
                // Continue with next project
            }
        }
        
    } catch (FeignException.NotFound e) {
        // 404 indicates end of pagination - return empty list
        log.info("Page {} not found - end of pagination", page);
        return result;
    } catch (Exception e) {
        log.error("Error fetching PropertyFinder page {}: {}", page, e.getMessage(), e);
        throw new RetryableCrawlerException("Error fetching page: " + page, e);
    }
    
    return result;
}
```

### 3.4  CrawlRecord Wrapping
Following the ARO pattern, wrap ProjectModel with metadata:

```java
private CrawlRecord<ProjectModel> wrapInCrawlRecord(
        ProjectModel model, 
        Map<String, Object> metadata) {
    return new CrawlRecord<>(
        model, 
        metadata, 
        model.getSourceUrn(), 
        model.getProjectUrn()
    );
}

private Map<String, Object> createMetadata(ProjectSummary summary, ProjectDetails detail) {
    Map<String, Object> metadata = new HashMap<>();
    Map<String, Object> rawData = new HashMap<>();
    
    rawData.put("project_summary", summary);
    rawData.put("project_detail", detail);
    
    metadata.put("raw_data", rawData);
    metadata.put("conversion_timestamp", Instant.now().toString());
    metadata.put("source_api", "urn:source:propertyfinder:ae");
    metadata.put("api_version", "v1");
    
    return metadata;
}
```

---

## 4  Source JSON
```
props → pageProps → detailResult ← source object
```
POJO: `uae.property_finder.model.ProjectDetails`.

---

## 5  Identifiers & URNs
| Field | Rule |
|-------|------|
| sourceUrn | `urn:source:propertyfinder:ae` |
| developerSlug | `detailResult.developer.slug`.lowercase() |
| projectSlug | substring after `developerSlug/` in `detailResult.slug` |
| developerUrn | `${sourceUrn}:developer:${developerSlug}` |
| projectUrn | `${developerUrn}:project:${projectSlug}` |
| externalId | `detailResult.id` (string) |

---

## 6  Basic attributes
| ProjectModel field | Source / rule |
|--------------------|---------------|
| title | `detailResult.title` (required) |
| description | `stripHtml(detailResult.description)` – remove HTML tags |
| currency | `"AED"` |
| projectSlug | see §5 |

---

## 7  Location & Geo
```
location.address      = location.fullName
location.city         = locationTree.length > 0 ? locationTree[0].name : null
location.cityDistrict = locationTree.length > 1 ? locationTree[1].name : null
location.state        = location.city
location.country      = "AE"
coordinates.lat       = location.coordinates.lat
coordinates.lng       = location.coordinates.lon ?? location.coordinates.lng
polygon               = null
```

---

## 8  Media
| images[*].type | Mapping |
|---------------|---------|
| image | → ProjectModel.images[] (first → coverImage) |
| video | → ProjectModel.videos[] |
| master-plan | URI stored in `additionalData.masterPlanImage` |

`stripHtml(masterPlan.description)` → `additionalData.masterPlanDescription`.

---

## 9  Amenities
`amenities[*].name` → `AmenityModel.label`.

---

## 10  Construction status
| constructionPhase | projectStatus |
|-------------------|--------------|
| not_started, off_plan | NOT_STARTED |
| under_construction | ACTIVE |
| completed, ready | FINISHED |
| null/unknown | null |

---

## 11  ProjectStats
* priceMin = startingPrice (AED)
* launchDate = salesStartDate (ISO → yyyy-MM-dd)
* completionDate = deliveryDate (ISO → yyyy-MM-dd)
* propertyTypes = `propertyTypes[*]` → enum `AdModel.PropertyTypeModel` (upper-case, '-' → '_', fallback RESIDENTIAL_FALLBACK)
* units: iterate over `units[*].units[*].list[*]` and build `UnitStats`:
  * propertyType = parent UnitSet mapping
  * bedrooms = (double) bedrooms – Integer to double conversion
  * priceMin = startingPrice (AED)
  * areaMinSqm = `UnitConversionUtils.sqftToSqm(areaFrom, 2)` – 2 decimal places
  * areaMaxSqm = `UnitConversionUtils.sqftToSqm(areaTo, 2)` if areaTo > 0, else null
  * areaRangeSqm = areaMinSqm (Double; deprecated field for backward compatibility)

---

## 12  Floor-plans
For each `layouts[*]`:
1. for every URL in `floorPlans[*]` build `FloorPlanModel` with:
   * externalId = **SHA-1(URL)** (hex-lower)  
   * floorplanUrn = `${sourceUrn}:floorplan:${externalId}`  
   * projectUrn, sourceUrn as usual  
   * propertyType = parent UnitSet mapping  
   * bedrooms = layouts[*].bedrooms  
   * title = layouts[*].layoutType  
   * image = first URL → ImageModel
2. Deduplicate by exact URL (case-sensitive; query string included), then attach the list to `ProjectModel.floorPlans`.

---

## 13  Brochures
If `brochureUrl` present → `BrochureModel` → `ProjectModel.brochures[]`.

---

## 14  Payment plans
For each `paymentPlans[*]`:
1. Create `PaymentPlanModel` with `title = paymentPlans[*].title`
2. For each `phases[*]` create `PaymentPhaseModel`:
   * `label = phases[*].label`
   * `value = phases[*].value`
3. For each `phases[*].miles[*]` create `PaymentMileModel`:
   * `label = miles[*].label` (may be empty string)
   * `value = miles[*].value`

Store as opaque list in `ProjectModel.paymentPlans`. All monetary values are assumed to be in AED.

---

## 15  Additional data
Key | Value
----|------
developerId | developer.id
masterPlanDescription | see §8
masterPlanImage | see §8
faqs | list of `{question: faqs[*].question, answer: stripHtml(faqs[*].answer)}`
ownershipType | enum FREEHOLD / LEASEHOLD / UNKNOWN (see below)
salesPhase | raw string (enum TBD)
lastInspectionDate | `parseIsoDate(lastInspectionDate) ?? null` (yyyy-MM-dd format, null if unparseable)
timelinePhases | raw list (enums TBD)

### 15.1  ownershipType normalisation
```
"freehold"  → FREEHOLD
"leasehold" → LEASEHOLD
null / "" / other → UNKNOWN
```

### 15.2  salesPhase enumeration (final)
Discovered via live crawl of 1 181 projects (2025-07-01):

| Raw value | Meaning |
|-----------|---------|
| `booking_started`           | Booking window open / EOI accepted |
| `sold_out`                  | Project fully sold |
| `waiting_for_sales_start`   | Announced; sales not yet launched |

Store **raw** string under `additionalData.salesPhase` *and* a normalised enum
`ADDITIONAL_SALES_PHASE` (generate only if a raw value is present) with the exact upper-snake casing of the
raw value (e.g. `BOOKING_STARTED`).

### 15.3  timelinePhases enumerations (final)

* **category** – one of `CONSTRUCTION`, `SALES` (case-insensitive)
* **phaseKey** – observed universe:
  - `booking_started`
  - `completed`
  - `not_started`
  - `prelaunch`
  - `sold_out`
  - `under_construction`
  - `waiting_for_sales_start`

These strings are kept verbatim under `additionalData.timelinePhases[*]`. No
further mapping is currently required.

---

## 16  URLs
`ProjectModel.urls[0]` = `https://www.propertyfinder.ae/en/new-projects/{developerSlug}/{projectSlug}`

---

## 17  Error Handling Strategy

### 17.1  Exception Classification
Following the ARO pattern:

| Error Type | Exception | Behavior |
|------------|-----------|----------|
| Page not found (404) | Return empty list | End pagination gracefully |
| Network/API errors | `RetryableCrawlerException` | Retry operation |
| Invalid page number | `NonRetryableCrawlerException` | Fail immediately |
| Project conversion errors | Log + continue | Skip project, continue page |

### 17.2  Logging Standards
Following the ARO pattern:

```java
// Success cases
log.info("Successfully processed PropertyFinder project: {}/{}", developerSlug, projectSlug);
log.info("Found {} projects on page {}", projects.size(), page);

// Warning cases  
log.warn("No projects found on page {}", page);
log.warn("Failed to convert project to ProjectModel: {}/{}", developerSlug, projectSlug);

// Error cases
log.error("Error processing project {}/{}: {}", developerSlug, projectSlug, e.getMessage(), e);
log.error("Error fetching PropertyFinder page {}: {}", page, e.getMessage(), e);
```

---

## 18  Implementation Notes

### 18.1  Required Dependencies
- `PropertyFinderFeignRestClient` (existing)
- `UnitConversionUtils` for area calculations
- HTML stripping utility for descriptions

### 18.2  Configuration
Follow ARO pattern with settings class:
- Base URL configuration
- Pagination settings
- Timeout configurations

### 18.3  Testing Strategy
- Unit tests for conversion logic
- Integration tests with sample payloads
- Mock tests for pagination edge cases
- 404 handling verification

---

## 19  Open points & Enumeration discovery *(closed 2025-07-01)*
All targeted fields have now been observed in the wild; no outstanding enums.
The exploration IT remains in place as an automated guard.

**Previous ambiguities resolved in v0.2:**
* Location tree null-safety handling
* Area calculation standardization using UnitConversionUtils
* Payment plan mapping algorithm clarification
* HTML processing specifications

---

## 20  Changelog
* **0.1 – 2025-06-30**  initial version.
* **0.2 – 2025-01-XX**  clarifications and improvements:
  * Added null-safety for `locationTree` array access
  * Specified use of `UnitConversionUtils.sqftToSqm()` for area calculations
  * Added `areaMaxSqm` mapping with proper null handling
  * Detailed payment plan mapping algorithm with sample validation
  * Clarified HTML processing for descriptions, FAQs, and master plan
  * Added explicit type conversions (Integer→double)
  * Enhanced error handling with default values and null-safety 
* **0.3 – 2025-01-XX**  crawler implementation specification:
  * Added complete crawler architecture following ARO patterns
  * Specified pagination discovery with 404 handling
  * Defined error handling strategy and exception classification  
  * Added API service layer architecture
  * Specified CrawlRecord wrapping with metadata
  * Added logging standards and implementation guidelines
  * Updated section numbering to accommodate new content 
* **0.4 – 2025-01-XX**  improved pagination efficiency:
  * Removed wasteful `verifyPageExists()` approach requiring separate HTTP calls
  * Updated pagination discovery to handle 404s directly in `fetchProjectsPage()`
  * Eliminated `PropertyFinderApiNotFoundException` – 404s now return empty list
  * Improved error handling pattern following standard pagination practices
  * Added proper NextJS response unwrapping paths in code examples

* **0.5 – 2025-01-XX**  pagination optimisation v2:
  * Leveraged `searchResult.meta.pagination.total` to compute page list in a
    single request and removed sequential fallback.
  * Updated §2.3 and provided reference implementation.