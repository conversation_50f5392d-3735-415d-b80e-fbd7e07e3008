package com.realmond.temporal_service.parser.workflow;

//import com.realmond.temporal_service.crawler.uae.alnair.AlnairParserActivity;
//import com.realmond.temporal_service.crawler.uae.aro.AroParserActivity;
import com.realmond.temporal_service.parser.common.ParserActivity;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import io.temporal.spring.boot.WorkflowImpl;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@WorkflowImpl(taskQueues = PropertyParsingWorkflow.TASK_QUEUE)
public class PropertyParsingWorkflowImpl implements PropertyParsingWorkflow {

    private static final Logger log = Workflow.getLogger(PropertyParsingWorkflowImpl.class);

    private final ActivityOptions alnairActivityOptions = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(10))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(3)
                    .setInitialInterval(Duration.ofSeconds(5))
                    .setMaximumInterval(Duration.ofMinutes(1))
                    .build())
            .build();

    private final ActivityOptions propertyFinderActivityOptions = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(10))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(3)
                    .setInitialInterval(Duration.ofSeconds(5))
                    .setMaximumInterval(Duration.ofMinutes(1))
                    .build())
            .build();

    private final ActivityOptions aroActivityOptions = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(10))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(3)
                    .setInitialInterval(Duration.ofSeconds(5))
                    .setMaximumInterval(Duration.ofMinutes(1))
                    .build())
            .build();

    private final ActivityOptions damacPropertiesActivityOptions = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(30)) // Longer timeout for single activity
            .setHeartbeatTimeout(Duration.ofMinutes(2))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(2)
                    .setInitialInterval(Duration.ofSeconds(10))
                    .setMaximumInterval(Duration.ofMinutes(2))
                    .build())
            .build();

    @Override
    public void parseAllProviders() {
        log.info("Starting property parsing workflow for all providers");

//        // Create activity stubs
//        AlnairParserActivity alnairActivity = Workflow.newActivityStub(
//                AlnairParserActivity.class, alnairActivityOptions);
//
//        PropertyFinderParserActivity propertyFinderActivity = Workflow.newActivityStub(
//                PropertyFinderParserActivity.class, propertyFinderActivityOptions);
//
//        AroParserActivity aroActivity = Workflow.newActivityStub(
//                AroParserActivity.class, aroActivityOptions);
//
//        // Start all parsers in parallel using Async
//        log.info("Starting all parsers in parallel");
//
//        // Start Alnair parsing asynchronously
//        Promise<Void> alnairPromise = Async.procedure(() -> {
//            log.info("Starting Alnair parsing");
//            try {
//                List<String> alnairPages = alnairActivity.fetchAllPages();
//                log.info("Found {} pages/projects for Alnair", alnairPages.size());
//
//                // Process pages in parallel with a limit on concurrency
//                processProjectsInParallel(alnairPages, alnairActivity, "Alnair", 10);
//            } catch (Exception e) {
//                log.error("Error during Alnair pagination: {}", e.getMessage());
//            }
//        });
//
//        // Start PropertyFinder parsing asynchronously
//        Promise<Void> propertyFinderPromise = Async.procedure(() -> {
//            log.info("Starting PropertyFinder parsing");
//            try {
//                List<String> propertyFinderPages = propertyFinderActivity.fetchAllPages();
//                log.info("Found {} pages/projects for PropertyFinder", propertyFinderPages.size());
//
//                // Process pages in parallel with a limit on concurrency
//                processProjectsInParallel(propertyFinderPages, propertyFinderActivity, "PropertyFinder", 10);
//            } catch (Exception e) {
//                log.error("Error during PropertyFinder pagination: {}", e.getMessage());
//            }
//        });
//
//        // Start ARO.ae parsing asynchronously
//        Promise<Void> aroPromise = Async.procedure(() -> {
//            log.info("Starting ARO.ae parsing");
//            try {
//                List<String> aroPages = aroActivity.fetchAllPages();
//                log.info("Found {} pages/projects for ARO.ae", aroPages.size());
//
//                // Process pages in parallel with a limit on concurrency
//                processProjectsInParallel(aroPages, aroActivity, "ARO.ae", 10);
//            } catch (Exception e) {
//                log.error("Error during ARO.ae pagination: {}", e.getMessage());
//            }
//        });
//
//        // Wait for all parsers to complete
//        Promise.allOf(alnairPromise, propertyFinderPromise, aroPromise).get();
//
//        log.info("Property parsing workflow completed for all providers");
    }

    /**
     * Process a list of projects in parallel with a limit on concurrency.
     *
     * @param projectIds List of project IDs or URLs to process
     * @param activity The parser activity to use
     * @param providerName The name of the provider (for logging)
     * @param maxConcurrency Maximum number of concurrent tasks
     */
    private <T> void processProjectsInParallel(List<String> projectIds, ParserActivity<T> activity,
                                              String providerName, int maxConcurrency) {
        if (projectIds.isEmpty()) {
            log.info("No projects to process for {}", providerName);
            return;
        }

        log.info("Processing {} projects for {} with max concurrency {}",
                projectIds.size(), providerName, maxConcurrency);

        // Track total parsed count
        AtomicInteger parsedCount = new AtomicInteger(0);

        // Process projects in batches to limit concurrency
        for (int i = 0; i < projectIds.size(); i += maxConcurrency) {
            int end = Math.min(i + maxConcurrency, projectIds.size());
            List<Promise<Void>> batchPromises = new ArrayList<>();

            // Start a batch of projects
            for (int j = i; j < end; j++) {
                final String projectId = projectIds.get(j);
                Promise<Void> promise = Async.procedure(() -> {
                    try {
                        List<T> projectsOnPage = activity.parsePage(projectId);
                        int count = projectsOnPage.size();
                        parsedCount.addAndGet(count);
                        log.info("Parsed page/project {} from {} with {} projects",
                                projectId, providerName, count);
                    } catch (Exception e) {
                        log.error("Error parsing {} project {}: {}",
                                providerName, projectId, e.getMessage());
                    }
                });
                batchPromises.add(promise);
            }

            // Wait for the current batch to complete before starting the next batch
            Promise.allOf(batchPromises.toArray(new Promise[0])).get();
        }

        log.info("Completed {} parsing. Parsed {} projects total",
                providerName, parsedCount.get());
    }
}
