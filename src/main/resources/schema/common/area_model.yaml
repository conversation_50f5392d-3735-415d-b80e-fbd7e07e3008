---
"$schema": http://json-schema.org/draft-07/schema#
title: area
type: object
description: "Area measurements for properties, including total, living, and balcony areas"
properties:
  total_area_sqm:
    type: number
    description: "Total area of the property in square meters"
    minimum: 0
    example: 120.5
  min_total_area_sqm:
    type: number
    description: "Minimum total area of the property in square meters, if it's a range"
    minimum: 0
    example: 110.0
  max_total_area_sqm:
    type: number
    description: "Maximum total area of the property in square meters, if it's a range"
    minimum: 0
    example: 130.0
  living_area_sqm:
    type: number
    description: "Living area of the property in square meters, excluding balconies and storage"
    minimum: 0
    example: 95.2
  balcony_area_sqm:
    type: number
    description: "Total balcony area in square meters"
    minimum: 0
    example: 25.3
additionalProperties: true