---
"$schema": http://json-schema.org/draft-07/schema#
title: payment plan
type: object
definitions:
  payment_step:
    type: object
    description: "Individual payment step within a payment plan"
    properties:
      name:
        type: string
        description: "Name or title of the payment step"
        example: "Down Payment"
      frequency:
        type: string
        description: "How often this payment is made"
        enum:
          - single
          - weekly
          - biweekly
          - monthly
          - bimonthly
          - quarterly
        example: "single"
      stage:
        type: string
        description: "At what stage of the purchase process this payment is due"
        enum:
          - on_booking
          - on_signing_spa
          - post_booking
          - on_handover
          - post_handover
        example: "on_booking"
      percentage:
        type: number
        description: "Percentage of total price for this payment step"
        minimum: 0
        maximum: 100
        example: 20
      description:
        type: string
        description: "Additional details about this payment step"
        example: "Initial down payment required upon booking"
    required:
      - name
      - percentage
      - frequency
properties:
  name:
    type: string
    description: The name of the payment plan.
    example: "Standard Payment Plan - 80/20"
  project_urn:
    type: string
    description: unique project urn the payment plan is applicable to
    example: "urn:project:marina-heights"
  payment_steps:
    type: array
    description: "Array of payment steps that make up this payment plan"
    items:
      $ref: "#/definitions/payment_step"
    example:
      - name: "Down Payment"
        frequency: "single"
        stage: "on_booking"
        percentage: 20
        description: "Initial down payment upon booking"
      - name: "Monthly Installments"
        frequency: "monthly"
        stage: "post_booking"
        percentage: 60
        description: "Monthly payments during construction"
      - name: "Final Payment"
        frequency: "single"
        stage: "on_handover"
        percentage: 20
        description: "Final payment upon handover"
required:
  - name
  - project_urn
