---
"$schema": http://json-schema.org/draft-07/schema#
type: object
properties:
  developer_urn:
    type: string
    description: "Unique resource name identifier for the developer"
    example: "urn:developer:emaar-properties"
  source_urn:
    type: string
    description: "Unique resource name identifier for the data source"
    example: "urn:source:dubai-land-department"
  title:
    type: string
    description: "Official name or title of the developer company"
    example: "Emaar Properties PJSC"
  location:
    $ref: common/location_model.yaml
    description: "Primary location or headquarters of the developer"
  website:
    type: string
    description: "Official website URL of the developer"
    example: "https://www.emaar.com"
  logo_url:
    type: string
    description: "URL to the developer's logo image"
    example: "https://cdn.example.com/logos/emaar-logo.png"
  description:
    type: string
    description: "Detailed description of the developer's business and activities"
    example: "Leading real estate developer in the UAE, known for iconic projects like Burj Khalifa and Dubai Mall"
  founded:
    type: string
    format: date
    description: "Date when the developer company was founded"
    example: "1997-07-15"
  employee_count:
    type: integer
    description: "Total number of employees working for the developer"
    example: 5000
  valuation:
    type: string
    description: "Current market valuation of the developer company"
    example: "15.2 billion AED"
  government_relation:
    type: string
    description: "Type of relationship or ownership structure with government entities"
    enum:
      - public
      - private
      - mixed
    example: "public"
  additional_data:
    additionalProperties: true
    description: "Additional flexible data fields for storing extra information about the developer"
    example:
      awards: ["Best Developer 2023", "Excellence in Construction"]
      certifications: ["ISO 9001", "LEED Certified"]
  external_id:
    type: string
    description: "External identifier used by the source system"
    example: "DEV_12345"
  developer_stats:
    $ref: "#/definitions/developer_stats"
    description: "Statistical information about the developer's portfolio performance"
required:
  - developer_urn
  - source_urn
  - title
  - external_id
definitions:
  developer_stats:
    type: object
    description: "Statistical information about the developer's project and building portfolio"
    properties:
      completed_projects_count:
        type: integer
        minimum: 0
        description: "Total number of projects that have been completed by the developer"
        example: 25
      completed_buildings_count:
        type: integer
        minimum: 0
        description: "Total number of individual buildings that have been completed by the developer"
        example: 150
      not_completed_projects_count:
        type: integer
        minimum: 0
        description: "Total number of projects that are currently in progress or planned but not yet completed"
        example: 8
      not_completed_buildings_count:
        type: integer
        minimum: 0
        description: "Total number of individual buildings that are currently in progress or planned but not yet completed"
        example: 45
      completed_buildings_evaluation:
        $ref: ./common/price_model.yaml
        description: "Total monetary evaluation of all completed buildings by the developer"
