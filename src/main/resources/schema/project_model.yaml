---
"$schema": http://json-schema.org/draft-07/schema#
title: project
description: Schema for real estate project models including detailed information
  about the project, units, and amenities.
type: object
definitions:
  project_stats:
    type: object
    description: Statistical information about the project, including unit details
      and project-wide statistics.
    properties:
      property_types:
        type: array
        description: An array of property types constructed in the project.
        items:
          $ref: common/property_type_model.yaml
      units:
        type: array
        description: An array of objects each representing a type of unit within the
          project.
        items:
          $ref: "#/definitions/unit_stats"
      total_no_of_residential_units:
        type: number
        description: The total number of residential units in the project.
        minimum: 0
      total_area:
        type: number
        description: The total area of the project in square meters.
        minimum: 0
      launch_date:
        type: string
        description: The date the project was launched.
        format: date
      completion_date:
        type: string
        description: The expected or actual completion date of the project.
        format: date
      completion_percent:
        type: number
        description: The completion state of the project in percent
        minimum: 0
        maximum: 100
      retail_space:
        type: number
        description: The amount of retail space available in the project in square
          meters.
        minimum: 0
      total_number_of_units:
        type: number
        description: The total number of units in the project.
        minimum: 0
      price_min:
        description: The starting price for units in the project.
        $ref: common/price_model.yaml
      price_max:
        description: Max price for units in the project.
        $ref: common/price_model.yaml
  unit_stats:
    type: object
    properties:
      property_type:
        $ref: common/property_type_model.yaml
      bedrooms:
        type: number
        description: The number of bedrooms in the unit.
        minimum: 0
      bedrooms_title:
        type: string
        description: A custom title for bedrooms setup
      area_range_sqm:
        type: number
        description: "(Deprecated) Lower bound of the unit's area in square meters. Use area_min_sqm and area_max_sqm instead."
        minimum: 0
        deprecated: true
      area_min_sqm:
        type: number
        description: Lower bound of unit area in square meters across all templates in the bucket.
        minimum: 0
      area_max_sqm:
        type: number
        description: Upper bound of unit area in square meters across all templates in the bucket.
        minimum: 0
      bathrooms:
        type: number
        description: Typical number of bathrooms for this bedroom/property-type bucket (mode or average).
        minimum: 0
      floor_from:
        type: integer
        description: Lowest floor where a unit of this bucket exists.
        minimum: 0
      floor_to:
        type: integer
        description: Highest floor where a unit of this bucket exists.
        minimum: 0
      availability_count:
        type: integer
        description: Aggregate number of units available across all templates in this bucket.
        minimum: 0
      media:
        type: array
        description: Representative set of media (e.g., floor-plan images) for this bucket.
        items:
          $ref: common/image_model.yaml
      price_min:
        description: The starting price for units in the project.
        $ref: common/price_model.yaml
      price_max:
        description: Max price for units in the project.
        $ref: common/price_model.yaml
    required:
      - property_type
  video:
    type: object
    properties:
      url:
        type: string
        description: The URL of the video.
        format: uri
properties:
  title:
    type: string
    description: The title or name of the real estate project.
  project_slug:
    type: string
    description: A URL-friendly identifier for the project, often used in web addresses.
  description:
    type: string
    description: A brief description of the project.
  external_id:
    type: string
    description: An identifier for the project used by external systems.
  location:
    $ref: common/location_model.yaml
  images:
    type: array
    description: A collection of images associated with the project.
    items:
      $ref: common/image_model.yaml
  cover_image:
    description: Cover Image
    $ref: common/image_model.yaml
  videos:
    type: array
    items:
      $ref: "#/definitions/video"
  project_stats:
    $ref: "#/definitions/project_stats"
  project_status:
    $ref: common/construction_status_model.yaml
  amenities:
    type: array
    description: A list of amenities available within the project.
    items:
      $ref: common/amenity_model.yaml
  payment_plans:
    type: array
    description: A list of payment plans available for purchasing units in the project.
    items:
      oneOf:
        - $ref: common/payment_plan_model.yaml
        - type: "object"
          title: payment plan reference
          properties:
            payment_plan_urn:
              type: string
          required:
            - payment_plan_urn
  eoi:
    type: object
    # todo
  brochures:
    type: array
    description: A collection of brochures for the project.
    items:
      $ref: common/brochure_model.yaml
  coordinates:
    $ref: common/coordinates_model.yaml
  polygon:
    $ref: common/polygon_model.yaml
  virtual_tour:
    type: string
    description: A URL to a virtual tour of the project.
    format: uri
  currency:
    type: string
    description: The currency in which the project's prices are listed.
    default: "AED"
  floor_plans:
    type: array
    description: A collection of floor plans for the project.
    items:
      $ref: common/floor_plan_model.yaml
  project_urn:
    type: string
    description: A unique project name used as a logical id
  developer_urn:
    type: string
    description: A unique developer name used as a logical id
  source_urn:
    type: string
    description: A unique source name
  urls:
    type: array
    description: project urls
    items:
      type: string
      format: uri
  additional_data:
    additionalProperties: true
required:
  - title
  - project_urn
  - developer_urn
  - source_urn
  - external_id
