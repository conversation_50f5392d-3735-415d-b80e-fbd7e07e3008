package com.realmond.temporal_service.crawler.common;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class UnitConversionUtilsTest {

    @Test
    public void testSqftToSqm_BasicConversion() {
        // 100 sqft should be 9.2903 sqm. Rounded to 2 decimal places, it's 9.29.
        assertEquals(9.29, UnitConversionUtils.sqftToSqm(100, 2));
    }

    @Test
    public void testSqftToSqm_RoundingUp() {
        // 15.678 sqft -> 1.45656... sqm. With scale 3 -> 1.457
        assertEquals(1.457, UnitConversionUtils.sqftToSqm(15.678, 3));
    }

    @Test
    public void testSqftToSqm_RoundingDown() {
        // 15.222 sqft -> 1.4141... sqm. With scale 3 -> 1.414
        assertEquals(1.414, UnitConversionUtils.sqftToSqm(15.222, 3));
    }

    @Test
    public void testSqftToSqm_ZeroScale() {
        // 125.5 sqft -> 11.659... sqm. Rounded to 0 decimal places -> 12.0
        assertEquals(12.0, UnitConversionUtils.sqftToSqm(125.5, 0));
    }

    @Test
    public void testSqftToSqm_ZeroInput() {
        assertEquals(0.0, UnitConversionUtils.sqftToSqm(0, 5));
    }

    @Test
    public void testSqftToSqm_NegativeScaleThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> {
            UnitConversionUtils.sqftToSqm(100, -2);
        });
    }

    @Test
    public void testSqftToSqm_HighPrecision() {
        // 1 sqft -> 0.092903 sqm
        assertEquals(0.092903, UnitConversionUtils.sqftToSqm(1, 6));
    }
} 