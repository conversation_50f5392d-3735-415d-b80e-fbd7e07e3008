package com.realmond.temporal_service.crawler.uae.alnair;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.alnair.api.AlnairApiFeignRestClient;
import com.realmond.temporal_service.crawler.uae.alnair.model.*;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictionaryService;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for {@link AlnairApiFeignRestClient} hitting the real https://api.alnair.ae service.
 * <p>
 * Disabled by default – enable by setting environment variable <code>RUN_INTEGRATION_TESTS=true</code>.
 */
@SpringBootTest(classes = {AlnairApiFeignRestClientIT.TestConfig.class}, webEnvironment = WebEnvironment.NONE, properties = {
    "crawler.uae.alnair.dict.base-url=https://alnair.ae",
    "spring.main.allow-bean-definition-overriding=true"
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
class AlnairApiFeignRestClientIT {

    @Configuration
    @EnableFeignClients(clients = {AlnairApiFeignRestClient.class, com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictFeignRestClient.class})
    @Import({FingerprintGenerator.class, AlnairSettings.class})
    @org.springframework.context.annotation.ComponentScan(basePackageClasses = com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictionaryService.class)
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {
    }

    @Autowired
    private AlnairApiFeignRestClient apiClient;

    private Integer sampleProjectId;
    private Integer sampleBuilderId;
    private String cityId;

    @Autowired
    private AlnairDictionaryService dictSvc;

    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeAll
    void init() {
        // Resolve city id (Dubai preferred)
        cityId = "1";
        try {
            var info = dictSvc.getReferenceData();
            if (info != null && info.getCities() != null) {
                cityId = info.getCities().stream()
                        .filter(c -> "Dubai".equalsIgnoreCase(c.getName()))
                        .map(c -> String.valueOf(c.getId()))
                        .findFirst()
                        .orElseGet(() -> String.valueOf(info.getCities().get(0).getId()));
            }
        } catch (Exception ignored) {}

        ProjectsResponse projects = apiClient.findProjects(30, 1, cityId);
        assertThat(projects).isNotNull();
        assertThat(projects.getData()).isNotNull();
        assertThat(projects.getData().getItems()).isNotEmpty();
        sampleProjectId = projects.getData().getItems().get(0).getId();

        DevelopersResponse developers = apiClient.getDevelopers(1, 30, cityId);
        if (developers != null && developers.getData() != null && !developers.getData().isEmpty()) {
            sampleBuilderId = developers.getData().get(0).getId();
        }

        Assumptions.assumeTrue(sampleProjectId != null, "Could not retrieve a project id from live API");
        Assumptions.assumeTrue(sampleBuilderId != null, "Could not retrieve a developer id from live API");
    }

    @Test
    void testFindProjects() {
        ProjectsResponse response = apiClient.findProjects(30, 1, cityId);
        assertThat(response).isNotNull();
        assertThat(response.getData()).isNotNull();
        assertThat(response.getData().getItems()).isNotEmpty();

        try {
            System.out.println("=== First project item ===\n" +
                    mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response.getData().getItems().get(0)));
        } catch (Exception ignored) {}
    }

    @Test
    void testGetDeveloperDetails() {
        Assumptions.assumeTrue(sampleBuilderId != null, "Sample developer id unavailable – skipping");
        DeveloperDetails details = apiClient.getDeveloperDetails(sampleBuilderId, cityId);
        assertThat(details).isNotNull();
        assertThat(details.getId()).isEqualTo(sampleBuilderId);
    }

    @Test
    void testGetLayoutUnits() {
        Assumptions.assumeTrue(sampleProjectId != null, "Sample project id unavailable – skipping");
        try {
            LayoutUnitsResponse unitsResp = apiClient.getLayoutUnits(sampleProjectId, cityId);
            assertThat(unitsResp).isNotNull();
            assertThat(unitsResp.getRooms()).isNotEmpty();
        } catch (feign.FeignException.NotFound nf) {
            // If units not available for the selected project, skip the test
            Assumptions.assumeTrue(false, "Units endpoint not available for selected project – skipping");
        }
    }
} 