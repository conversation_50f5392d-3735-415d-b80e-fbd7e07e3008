package com.realmond.temporal_service.crawler.uae.alnair;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.alnair.api.AlnairApiFeignRestClient;
import com.realmond.temporal_service.crawler.uae.alnair.model.ProjectsResponse;
import com.realmond.temporal_service.crawler.uae.alnair.model.ProjectDetailsResponse;
import com.realmond.temporal_service.crawler.uae.alnair.site.AlnairSiteFeignRestClient;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictionaryService;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for {@link AlnairSiteFeignRestClient} that hit the real https://alnair.ae website.
 * <p>
 * These tests are disabled by default – enable by setting <code>RUN_INTEGRATION_TESTS=true</code>.
 */
@SpringBootTest(classes = {AlnairSiteFeignRestClientIT.TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE, properties = {
    "crawler.uae.alnair.dict.base-url=https://alnair.ae",
    "spring.main.allow-bean-definition-overriding=true"
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
class AlnairSiteFeignRestClientIT {

    @Configuration
    @EnableFeignClients(clients = {AlnairApiFeignRestClient.class, AlnairSiteFeignRestClient.class, com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictFeignRestClient.class})
    @Import({FingerprintGenerator.class, AlnairSettings.class})
    @org.springframework.context.annotation.ComponentScan(basePackageClasses = com.realmond.temporal_service.crawler.uae.alnair.dict.AlnairDictionaryService.class)
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {

        @Bean
        @Primary
        public feign.Retryer testRetryer() {
            return new feign.Retryer.Default(1000, 3000, 3);
        }
    }

    @Autowired
    private AlnairApiFeignRestClient apiClient; // helper for project ids

    @Autowired
    private AlnairSiteFeignRestClient siteClient;

    private Integer sampleProjectId;
    private String cityId;
    @Autowired
    private AlnairDictionaryService dictSvc;

    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeAll
    void init() {
        // resolve cityId similar to other test
        cityId = "1";
        try {
            var info = dictSvc.getReferenceData();
            if (info != null && info.getCities() != null) {
                cityId = info.getCities().stream()
                        .filter(c -> "Dubai".equalsIgnoreCase(c.getName()))
                        .map(c -> String.valueOf(c.getId()))
                        .findFirst()
                        .orElseGet(() -> String.valueOf(info.getCities().get(0).getId()));
            }
        } catch (Exception ignored) {}

        ProjectsResponse projects = apiClient.findProjects(30, 1, cityId);
        if (projects != null && projects.getData() != null && projects.getData().getItems() != null && !projects.getData().getItems().isEmpty()) {
            sampleProjectId = projects.getData().getItems().get(0).getId();
        }
        Assumptions.assumeTrue(sampleProjectId != null, "Could not find a project id to test site endpoint");
    }

    @Test
    void testGetProjectDetails() {
        Assumptions.assumeTrue(sampleProjectId != null, "Project id unavailable – skipping");
        try {
            ProjectDetailsResponse response = siteClient.getProjectDetails(sampleProjectId);
            assertThat(response).isNotNull();
            assertThat(response.getProjectPage()).isNotNull();

            try {
                System.out.println("=== Project Details Response ===\n" +
                        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response.getProjectPage().getData().getProject()));
            } catch (Exception ignored) {}
        } catch (feign.FeignException.NotFound nf) {
            Assumptions.assumeTrue(false, "Selected project does not expose .data endpoint – skipping");
        }
    }
} 