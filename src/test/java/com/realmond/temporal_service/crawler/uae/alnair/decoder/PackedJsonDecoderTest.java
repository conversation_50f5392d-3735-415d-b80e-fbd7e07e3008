package com.realmond.temporal_service.crawler.uae.alnair.decoder;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for {@link PackedJsonDecoder} covering the minimal decoding
 * scenarios required by backlog Task&nbsp;3.5.
 */
class PackedJsonDecoderTest {

    @Test
    @DisplayName("Decodes object with reference keys (_n)")
    void decodesReferenceObject() {
        List<Object> packed = new ArrayList<>();
        packed.add("name");                 // 0 – property name

        Map<String, Object> refObj = new LinkedHashMap<>();
        refObj.put("_0", 2);               // key "_0" → property name at idx 0, value at idx 2
        packed.add(refObj);                  // 1 – root object we want to decode

        packed.add("John");                 // 2 – property value

        PackedJsonDecoder decoder = new PackedJsonDecoder(packed);
        Object decoded = decoder.decode(1);

        assertTrue(decoded instanceof Map, "Decoded value must be a Map");
        @SuppressWarnings("unchecked")
        Map<String, Object> map = (Map<String, Object>) decoded;
        assertEquals(1, map.size());
        assertEquals("John", map.get("name"));
    }

    @Test
    @DisplayName("Decodes array that references other indices")
    void decodesArray() {
        List<Object> packed = new ArrayList<>();

        // index 0 – root array referencing indices 1 and 2
        List<Object> rootArray = List.of(1, 2);
        packed.add(rootArray);

        packed.add("foo"); // 1
        packed.add("bar"); // 2

        Object decoded = new PackedJsonDecoder(packed).decode(0);

        assertTrue(decoded instanceof List);
        List<?> list = (List<?>) decoded;
        assertEquals(List.of("foo", "bar"), list);
    }

    @Test
    @DisplayName("Returns primitive value when no decoding is necessary")
    void returnsPrimitive() {
        List<Object> packed = List.of("hello");
        Object decoded = new PackedJsonDecoder(packed).decode(0);
        assertEquals("hello", decoded);
    }

    @Test
    @DisplayName("-5 sentinel decodes to null")
    void sentinelNull() {
        List<Object> packed = new ArrayList<>();
        packed.add("value");         // 0 property name
        Map<String, Object> obj = new LinkedHashMap<>();
        obj.put("_0", -5);           // -5 sentinel -> null
        packed.add(obj);              // 1 root

        Object decoded = new PackedJsonDecoder(packed).decode(1);
        assertTrue(decoded instanceof Map);
        @SuppressWarnings("unchecked")
        Map<String, Object> map = (Map<String, Object>) decoded;
        assertNull(map.get("value"));
    }

    @Test
    @DisplayName("Unknown sentinel throws IllegalStateException")
    void unknownSentinel() {
        List<Object> packed = new ArrayList<>();
        packed.add("key");
        Map<String, Object> obj = new LinkedHashMap<>();
        obj.put("_0", -42); // unsupported sentinel
        packed.add(obj);     // 1 root

        PackedJsonDecoder decoder = new PackedJsonDecoder(packed);
        assertThrows(IllegalStateException.class, () -> decoder.decode(1));
    }

    @Test
    @DisplayName("Fixture dictionary.encoded.json contains loaderData key")
    void fixtureDecodingProducesLoaderData() throws Exception {
        com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
        java.io.InputStream is = this.getClass().getClassLoader().getResourceAsStream("crawler/uae/alnair/dictionary.encoded.json");
        assertNotNull(is, "Fixture not found");
        List<Object> packed = mapper.readValue(is, List.class);

        Object decoded = new PackedJsonDecoder(packed).decode();
        assertTrue(decoded instanceof Map);
        @SuppressWarnings("unchecked")
        Map<String, Object> map = (Map<String, Object>) decoded;
        assertTrue(map.containsKey("loaderData"), "Decoded map should contain loaderData");
    }
} 