package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.alnair.model.Dictionary;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.nio.charset.StandardCharsets;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test that wires the {@link AlnairDictFeignRestClient} against a
 * local {@link MockWebServer}. The server returns a static HTML fixture that
 * contains the packed dictionary. The test asserts that the client successfully
 * decodes the response into a {@link Dictionary} instance.
 */
@SpringBootTest(classes = {AlnairDictFeignRestClientIT.TestConfig.class}, webEnvironment = WebEnvironment.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AlnairDictFeignRestClientIT {

    // ---------------------------------------------------------------------
    // Static test configuration enabling Feign & required beans
    // ---------------------------------------------------------------------
    @Configuration
    @EnableFeignClients(basePackageClasses = AlnairDictFeignRestClient.class)
    @Import({FingerprintGenerator.class, AlnairDictSettings.class, AlnairDictFeignConfiguration.class})
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {
    }

    private static MockWebServer mockWebServer;

    @DynamicPropertySource
    static void dynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("crawler.uae.alnair.dict.base-url", () -> mockWebServer.url("/").toString());
    }

    @Autowired
    private AlnairDictFeignRestClient client;

    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeAll
    static void setUp() throws Exception {
        mockWebServer = new MockWebServer();
        mockWebServer.start();

        // Enqueue HTML fixture response
        String html = new String(AlnairDictFeignRestClientIT.class
                .getResourceAsStream("/crawler/uae/alnair/app.html")
                .readAllBytes(), StandardCharsets.UTF_8);
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .addHeader("Content-Type", "text/html; charset=UTF-8")
                .setBody(html));
    }

    @AfterAll
    static void tearDown() throws Exception {
        mockWebServer.shutdown();
    }

    @Test
    void shouldDecodeDictionaryFromHtml() throws Exception {
        Dictionary dictionary = client.getDictionary();
        assertThat(dictionary).as("Dictionary decoded from packed HTML").isNotNull();
        assertThat(dictionary.getLoaderData()).isNotNull();

        // Optional diagnostic
        try {
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dictionary);
            System.out.println("=== Decoded Dictionary ===\n" + json);
        } catch (Exception ignored) {}
    }
} 