package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.realmond.temporal_service.crawler.RequestRetrySettings;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.net.URL;
import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies that {@link AlnairDictSettings} exposes the correct default values
 * as declared in the module specification (spec §4).
 */
@SpringBootTest(classes = AlnairDictSettingsTest.TestConfig.class, webEnvironment = WebEnvironment.NONE)
@ActiveProfiles("test")
class AlnairDictSettingsTest {

    @Configuration
    @Import(AlnairDictSettings.class)
    static class TestConfig {
        // No additional beans required
    }

    @Autowired
    private AlnairDictSettings settings;

    @Test
    void defaultsMatchSpecification() throws Exception {
        assertThat(settings).isNotNull();

        // Base URL
        assertThat(settings.getBaseUrl()).isEqualTo(new URL("https://alnair.ae"));

        // Path
        assertThat(settings.getDictionaryPath()).isEqualTo("/app");

        // Cache settings
        assertThat(settings.getCacheTtl()).isEqualTo(Duration.ofDays(1));
        assertThat(settings.getCacheMaxSize()).isEqualTo(100L);

        // Retry settings
        RequestRetrySettings retry = settings.getRetry();
        assertThat(retry).isNotNull();
        assertThat(retry.getMaxRetries()).isEqualTo(3);
        assertThat(retry.getRetryDelayMs()).isEqualTo(2000L);
        assertThat(retry.getMaxPeriodMs()).isEqualTo(10_000L);
        assertThat(retry.getRandomDelayCapMs()).isEqualTo(1_000);
    }
} 