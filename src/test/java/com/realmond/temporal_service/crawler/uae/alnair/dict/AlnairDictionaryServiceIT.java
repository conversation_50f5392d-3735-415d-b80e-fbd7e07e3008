package com.realmond.temporal_service.crawler.uae.alnair.dict;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.alnair.model.Dictionary;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import java.nio.charset.StandardCharsets;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test verifying that {@link AlnairDictionaryService} leverages the
 * Caffeine cache configured in {@link AlnairDictFeignConfiguration}. When the
 * service is invoked twice within the cache TTL, the underlying Feign client
 * should be hit exactly once while returning the reference data DTO.
 */
@SpringBootTest(classes = {AlnairDictionaryServiceIT.TestConfig.class}, webEnvironment = WebEnvironment.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AlnairDictionaryServiceIT {

    // ---------------------------------------------------------------------
    // Static test configuration enabling Feign, caching and required beans
    // ---------------------------------------------------------------------
    @Configuration
    @EnableCaching
    @EnableFeignClients(basePackageClasses = AlnairDictFeignRestClient.class)
    @Import({FingerprintGenerator.class,
            AlnairDictSettings.class,
            AlnairDictFeignConfiguration.class,
            AlnairDictionaryService.class})
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {
    }

    private static MockWebServer mockWebServer;

    @DynamicPropertySource
    static void dynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("crawler.uae.alnair.dict.base-url", () -> mockWebServer.url("/").toString());
    }

    @Autowired
    private AlnairDictionaryService service;

    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeAll
    static void setUp() throws Exception {
        mockWebServer = new MockWebServer();
        mockWebServer.start();

        // Prepare fixture HTML response
        String html = new String(AlnairDictionaryServiceIT.class
                .getResourceAsStream("/crawler/uae/alnair/app.html")
                .readAllBytes(), StandardCharsets.UTF_8);
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .addHeader("Content-Type", "text/html; charset=UTF-8")
                .setBody(html));
    }

    @AfterAll
    static void tearDown() throws Exception {
        mockWebServer.shutdown();
    }

    @Test
    void shouldCacheReferenceDataBetweenCalls() throws Exception {
        // First call – expected to hit MockWebServer (cache miss)
        Dictionary.Info first = service.getReferenceData();
        assertThat(first).as("First call returns reference data").isNotNull();

        // Sanity checks on contents
        assertThat(first.getCities()).isNotNull();
        assertThat(first.getCountries()).isNotNull();
        assertThat(first.getCatalogs()).isNotNull();

        // Second call – should be served from cache
        Dictionary.Info second = service.getReferenceData();
        assertThat(second).as("Second call returns reference data from cache").isNotNull();

        // Ensure we got the same instance (optional but makes behavior obvious)
        assertThat(second).isSameAs(first);

        // Verify only ONE HTTP request was made to the server
        assertThat(mockWebServer.getRequestCount()).as("Feign should be invoked once").isEqualTo(1);

        // Optional diagnostic output
        try {
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(first);
            System.out.println("=== Cached Reference Data ===\n" + json);
        } catch (Exception ignored) {}
    }
} 