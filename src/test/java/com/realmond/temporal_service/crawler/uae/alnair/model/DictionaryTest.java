package com.realmond.temporal_service.crawler.uae.alnair.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies correct deserialisation of the Alnair dictionary payload.
 */
class DictionaryTest {

    private ObjectMapper mapper;
    private Dictionary sut;

    @BeforeEach
    void setUp() throws Exception {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();

        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/alnair/dictionary.json")) {
            assertThat(in).as("fixture stream").isNotNull();

            sut = mapper.readValue(in, Dictionary.class);
        }
    }

    @Test
    void fields_shouldDeserializeCorrectly() {
        assertThat(sut).isNotNull();
        assertThat(sut.getLoaderData()).isNotNull();

        Dictionary.Catalogs catalogs = sut.getLoaderData()
                .getAppLayout()
                .getInfo()
                .getCatalogs();

        assertThat(catalogs).isNotNull();

        assertThat(catalogs.getCompoundVillaPlot()).isNotNull();
        assertThat(catalogs.getCompoundVillaPlot().getId()).isEqualTo(240);

        assertThat(catalogs.getPaymentPlanWhen()).isNotNull();
        assertThat(catalogs.getPaymentPlanWhen().getId()).isEqualTo(349);

        // Verify first option of "rooms" catalog deserialised properly
        assertThat(catalogs.getRooms()).isNotNull();
        assertThat(catalogs.getRooms().getOptions()).isNotEmpty();
        assertThat(catalogs.getRooms().getOptions().get(0).getValue()).isEqualTo("Studio");

        // -----------------------------------------------------------------
        // New assertions: cities and countries must be deserialised
        // -----------------------------------------------------------------

        var info = sut.getLoaderData()
                .getAppLayout()
                .getInfo();

        assertThat(info.getCities())
                .as("Cities list should be populated")
                .isNotNull()
                .isNotEmpty();

        assertThat(info.getCities().get(0).getName()).isEqualTo("Dubai");

        assertThat(info.getCountries())
                .as("Countries list should be populated")
                .isNotNull()
                .isNotEmpty();

        assertThat(info.getCountries().get(0).getTitle()).isEqualTo("United Arab Emirates");
    }
} 