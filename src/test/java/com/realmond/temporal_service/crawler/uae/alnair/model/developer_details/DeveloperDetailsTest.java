package com.realmond.temporal_service.crawler.uae.alnair.model.developer_details;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.alnair.model.DeveloperDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies deserialisation of developer details payload (Emaar Properties).
 */
class DeveloperDetailsTest {

    private DeveloperDetails sut;

    @BeforeEach
    void setUp() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/alnair/details.developer.json")) {
            sut = mapper.readValue(in, DeveloperDetails.class);
        }
    }

    @Test
    void basicFields() {
        assertThat(sut.getId()).isEqualTo(6);
        assertThat(sut.getTitle()).isEqualTo("Emaar Properties");
        assertThat(sut.getWebsite()).isEqualTo("https://www.emaar.com");
        assertThat(sut.getCities()).containsExactly(1, 5);
    }

    @Test
    void logo_and_photos() {
        assertThat(sut.getLogo()).isNotNull();
        assertThat(sut.getPhotos()).hasSize(4);
        assertThat(sut.getPhotos().get(0).getSrc()).contains("gallery_photo");
    }

    @Test
    void statsUnits_and_total() {
        assertThat(sut.getStatsUnits()).isNotEmpty();
        var studioStat = sut.getStatsUnits().stream()
                .filter(u -> u.getKey().equals("studio"))
                .findFirst().orElseThrow();
        assertThat(studioStat.getCount()).isEqualTo(9);

        assertThat(sut.getStatsTotal()).hasSize(2);
        assertThat(sut.getStatsTotal().get(0).getProjectsCount()).isEqualTo(198);
    }

    @Test
    void salesOffice_contact() {
        assertThat(sut.getSalesOffices()).hasSize(2);
        var office = sut.getSalesOffices().get(0);
        assertThat(office.getCommissionPercent()).isEqualTo("4.00");
    }
} 