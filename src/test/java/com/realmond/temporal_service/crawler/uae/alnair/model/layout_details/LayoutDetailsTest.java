package com.realmond.temporal_service.crawler.uae.alnair.model.layout_details;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.alnair.model.LayoutDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

class LayoutDetailsTest {

    private LayoutDetails sut;

    @BeforeEach
    void setup() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/alnair/details.layout.json")) {
            sut = mapper.readValue(in, LayoutDetails.class);
        }
    }

    @Test
    void coreFields() {
        assertThat(sut.getId()).isEqualTo(46265);
        assertThat(sut.getTitle()).contains("3 Bedroom");
        assertThat(sut.getArea()).isEqualTo("157.00");
        assertThat(sut.getCatalogNumberRoomsId()).isEqualTo(113);
    }

    @Test
    void levelsParsing() {
        assertThat(sut.getLevels()).singleElement()
                .satisfies(l -> {
                    assertThat(l.getLevel()).isEqualTo("1");
                    assertThat(l.getSrc()).contains("uploads/");
                });
    }

    @Test
    void unitsSummary() {
        UnitsSummary u = sut.getUnits();
        assertThat(u.getCount()).isEqualTo(2);
        assertThat(u.getPriceMin()).isEqualTo(4_147_560);
        assertThat(u.getCatalogUnitTypeRoomIds()).containsExactly(138);
    }

    @Test
    void paymentPlanChecks() {
        assertThat(sut.getPaymentPlans()).singleElement()
                .satisfies(p -> {
                    assertThat(p.getTitle()).contains("60/40");
                    assertThat(p.getInfo().getOnBookingPercent()).isEqualTo(10);
                    assertThat(p.getItems().get("booking")).hasSize(1);
                });
    }
}
