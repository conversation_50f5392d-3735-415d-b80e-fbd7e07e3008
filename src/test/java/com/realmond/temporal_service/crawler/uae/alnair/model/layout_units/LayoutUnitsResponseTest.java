package com.realmond.temporal_service.crawler.uae.alnair.model.layout_units;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.alnair.model.LayoutUnitsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

class LayoutUnitsResponseTest {

    private LayoutUnitsResponse sut;

    @BeforeEach
    void load() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/alnair/layout-units.project.json")) {
            sut = mapper.readValue(in, LayoutUnitsResponse.class);
        }
    }

    @Test
    void shouldParseGroups() {
        assertThat(sut.getRooms()).hasSize(3).containsKeys("111", "112", "113");
    }

    @Test
    void group111Assertions() {
        LayoutUnitGroup g111 = sut.getRooms().get("111");
        assertThat(g111.getItems()).hasSize(3);
        LayoutUnitTotal total = g111.getTotal();
        assertThat(total.getCount()).isEqualTo(7);
        assertThat(total.getPriceMax()).isEqualTo(2671200);
    }

    @Test
    void verifyLayoutInfoFields() {
        LayoutUnitItem first = sut.getRooms().get("112").getItems().get(0);
        assertThat(first.getLayout().getTitle()).contains("2 Bedroom");
        assertThat(first.getLayout().getLevels()).singleElement()
                .satisfies(url -> assertThat(url).contains("uploads/presentation"));
        assertThat(first.getAreaMax()).isGreaterThan(100);
    }
}
