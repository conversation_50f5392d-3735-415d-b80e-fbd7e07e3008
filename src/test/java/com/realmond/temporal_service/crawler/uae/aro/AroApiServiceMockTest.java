package com.realmond.temporal_service.crawler.uae.aro;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.AroCommon;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectSummary;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.realmond.temporal_service.crawler.uae.aro.AroBuildingApiService;
import com.realmond.temporal_service.crawler.uae.aro.AroFloorPlanConverter;
import com.realmond.etl.model.common.FloorPlanModel;
import com.realmond.etl.model.BuildingModel;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import java.util.stream.Collectors;

/**
 * Integration test for {@link AroApiService} using realistic mock data from the ARO.ae Postman collection.
 * 
 * This test simulates real API responses to ensure proper data transformation and error handling.
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = {AroApiServiceMockTest.TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
class AroApiServiceMockTest {

    // Test configuration that provides only the needed beans
    static class TestConfig {
        // Minimal configuration for this test
    }

    @Mock
    private AroFeignRestClient aroClient;

    @Mock
    private AroEnrichmentService enrichmentService;

    @Mock
    private AroSettings settings;

    @Mock
    private AroBuildingApiService buildingApiService;

    @Mock
    private AroFloorPlanConverter aroFloorPlanConverter;

    private AroApiService aroApiService;
    private ObjectMapper objectMapper;

    // Mock data based on the Postman collection
    private static final String MOCK_PROJECTS_RESPONSE = """
        {
            "data": [
                {
                    "id": 3088,
                    "title": "Alton by Nshama",
                    "slug": "alton-by-nshama",
                    "price_from": {
                        "currency_code": 784,
                        "amount": 1182888
                    },
                    "developer": {
                        "title": "Nshama",
                        "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
                        "id": 403
                    },
                    "images": [
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp"
                    ],
                    "is_favorite": false,
                    "handover_date": "2028-05-31T00:00:00.000Z",
                    "center": null,
                    "in_folder": false,
                    "pricing_category": null,
                    "rating": 0
                },
                {
                    "id": 3085,
                    "title": "Skyhills Residences 3",
                    "slug": "skyhills-residences-3",
                    "price_from": {
                        "currency_code": 784,
                        "amount": 1126510
                    },
                    "developer": {
                        "title": "HRE Development",
                        "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/7c79003f-07df-427c-8806-de5db5e8fcb1.webp",
                        "id": 28
                    },
                    "images": [
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/2b64c46a-85e8-44b1-9e23-b00b08cf78eb.webp"
                    ],
                    "is_favorite": false,
                    "handover_date": "2026-10-31T00:00:00.000Z",
                    "center": null,
                    "in_folder": false,
                    "pricing_category": "Mid-Range",
                    "rating": 0
                }
            ],
            "paging": {
                "total": 18,
                "size": 30,
                "number": 1
            },
            "total": 540,
            "left": 510,
            "count": 30
        }""";

    private static final String MOCK_PROJECT_DETAIL_RESPONSE = """
        {
            "id": 3088,
            "slug": "alton-by-nshama",
            "title": "Alton by Nshama",
            "description": "Welcome to Alton by Nshama, nestled in the vibrant community of Town Square, an area synonymous with dynamic growth and family-friendly vibes. This is where you can find your dream home, tailored to enhance your lifestyle.",
            "handover_date": "2028-05-31T00:00:00.000Z",
            "developer": {
                "title": "Nshama",
                "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
                "id": 403
            },
            "images": [
                "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp"
            ],
            "gallery": [
                {
                    "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                    "tags": ["exterior", "main"]
                },
                {
                    "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp",
                    "tags": ["exterior"]
                }
            ],
            "youtube_videos": [],
            "unit_description": "The Alton by Nshama project offers 0 units, ranging from studios to unspecified bedroom counts.",
            "floorplan_description": "The Alton by Nshama project offers 0 unique floor plans.",
            "is_favorite": false,
            "address": "Dubai, Dubailand, Town Square",
            "launch_date": null,
            "construction_start_date": null,
            "has_brochure": true,
            "is_exclusive": false,
            "in_folder": false,
            "pricing_category": null,
            "price_per_sqft": null,
            "rating": 0,
            "rating_count": 0,
            "rating_distribution": {
                "1": 0,
                "2": 0,
                "3": 0,
                "4": 0,
                "5": 0
            }
        }""";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // For Java 8 time support
        
        // Setup default settings behavior
        when(settings.getPageSize()).thenReturn(30);
        
        aroApiService = new AroApiService(aroClient, enrichmentService, settings, buildingApiService, aroFloorPlanConverter);
    }

    @Test
    void fetchProjectPagesCount_WithValidResponse_ShouldReturnCorrectPageCount() throws JsonProcessingException {
        // Given: Mock response with pagination info from Postman collection
        AroApiResponse<ProjectSummary> mockResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        when(aroClient.getProjects(1, 30)).thenReturn(mockResponse);

        // When: Fetch project pages count
        int pagesCount = aroApiService.fetchProjectPagesCount();

        // Then: Should return the total pages from the pagination info
        assertThat(pagesCount).isEqualTo(18);
    }

    @Test
    void fetchProjectPagesCount_WithNoPagingInfo_ShouldReturnOne() {
        // Given: Mock response without pagination info
        AroApiResponse<ProjectSummary> mockResponseNoPaging = new AroApiResponse<>();
        mockResponseNoPaging.setData(List.of());
        mockResponseNoPaging.setPaging(null);
        
        when(aroClient.getProjects(1, 30)).thenReturn(mockResponseNoPaging);

        // When: Fetch project pages count
        int pagesCount = aroApiService.fetchProjectPagesCount();

        // Then: Should return 1 as default
        assertThat(pagesCount).isEqualTo(1);
    }

    @Test
    void fetchProjectPagesCount_WhenClientThrowsException_ShouldThrowRetryableException() {
        // Given: Mock client throws exception
        when(aroClient.getProjects(1, 30)).thenThrow(new RuntimeException("API error"));

        // When & Then: Should wrap in RetryableCrawlerException
        assertThatThrownBy(() -> aroApiService.fetchProjectPagesCount())
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Error fetching pages count")
            .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void fetchProjectsPage_WithValidResponse_ShouldReturnCrawlRecords() throws JsonProcessingException {
        // Given: Mock successful responses from ARO API
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail1 = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        // Create second project detail for Skyhills
        String skyhillsDetailJson = """
            {
                "id": 3085,
                "slug": "skyhills-residences-3",
                "title": "Skyhills Residences 3",
                "description": "Discover luxury living at Skyhills Residences 3, a premium development offering modern amenities and elegant design.",
                "handover_date": "2026-10-31T00:00:00.000Z",
                "developer": {
                    "title": "HRE Development",
                    "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/7c79003f-07df-427c-8806-de5db5e8fcb1.webp",
                    "id": 28
                },
                "images": [
                    "https://d1rxmks6dvv2cz.cloudfront.net/project/2b64c46a-85e8-44b1-9e23-b00b08cf78eb.webp"
                ],
                "gallery": [
                    {
                        "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/2b64c46a-85e8-44b1-9e23-b00b08cf78eb.webp",
                        "tags": ["exterior", "main"]
                    }
                ],
                "youtube_videos": [],
                "unit_description": "The Skyhills Residences 3 project offers modern units with premium finishes.",
                "floorplan_description": "The Skyhills Residences 3 project offers various floor plans.",
                "is_favorite": false,
                "address": "Dubai, Al Furjan",
                "launch_date": null,
                "construction_start_date": null,
                "has_brochure": true,
                "is_exclusive": false,
                "in_folder": false,
                "pricing_category": "Mid-Range",
                "price_per_sqft": null,
                "rating": 0,
                "rating_count": 0,
                "rating_distribution": {
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0
                }
            }""";
        
        ProjectDetail mockProjectDetail2 = objectMapper.readValue(skyhillsDetailJson, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo1 = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail1)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();
            
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo2 = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail2)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup mocks - both projects should succeed with their specific details
        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(mockProjectDetail1);
        when(aroClient.getProjectBySlug("skyhills-residences-3")).thenReturn(mockProjectDetail2);
        when(enrichmentService.enrichProjectDetail(mockProjectDetail1)).thenReturn(mockEnrichedInfo1);
        when(enrichmentService.enrichProjectDetail(mockProjectDetail2)).thenReturn(mockEnrichedInfo2);

        // When: Fetch projects from page 1
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should return crawl records for all projects on the page
        assertThat(results).hasSize(2);
        
        // Validate first project (Alton by Nshama)
        CrawlRecord<ProjectModel> firstRecord = results.get(0);
        assertThat(firstRecord.data().getTitle()).isEqualTo("Alton by Nshama");
        assertThat(firstRecord.data().getExternalId()).isEqualTo("3088");
        assertThat(firstRecord.data().getSourceUrn()).isEqualTo(AroCommon.SOURCE_URN);
        assertThat(firstRecord.source()).isEqualTo(AroCommon.SOURCE_URN);
        
        // Validate metadata contains raw API data
        assertThat(firstRecord.metadata()).containsKey("raw_data");
        assertThat(firstRecord.metadata()).containsKey("conversion_timestamp");
        assertThat(firstRecord.metadata()).containsKey("source_api");
        assertThat(firstRecord.metadata().get("source_api")).isEqualTo(AroCommon.SOURCE_URN);
        
        // Validate second project (Skyhills Residences 3)
        CrawlRecord<ProjectModel> secondRecord = results.get(1);
        assertThat(secondRecord.data().getTitle()).isEqualTo("Skyhills Residences 3");
        assertThat(secondRecord.data().getExternalId()).isEqualTo("3085");
    }

    @Test
    void fetchProjectsPage_WithEmptyResponse_ShouldReturnEmptyList() {
        // Given: Mock empty response
        AroApiResponse<ProjectSummary> emptyResponse = new AroApiResponse<>();
        emptyResponse.setData(List.of());
        
        when(aroClient.getProjects(99, 30)).thenReturn(emptyResponse);

        // When: Fetch projects from page 99
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(99);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void fetchProjectsPage_WithNullData_ShouldReturnEmptyList() {
        // Given: Mock response with null data
        AroApiResponse<ProjectSummary> nullDataResponse = new AroApiResponse<>();
        nullDataResponse.setData(null);
        
        when(aroClient.getProjects(99, 30)).thenReturn(nullDataResponse);

        // When: Fetch projects from page 99
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(99);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void fetchProjectsPage_WhenProjectDetailFails_ShouldContinueWithOtherProjects() throws JsonProcessingException {
        // Given: Mock successful project list but failing project detail for first project
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup mocks: first project fails, second succeeds
        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug("alton-by-nshama"))
            .thenThrow(new RuntimeException("Project detail fetch failed"));
        when(aroClient.getProjectBySlug("skyhills-residences-3")).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class))).thenReturn(mockEnrichedInfo);

        // When: Fetch projects from page 1
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should return only the successful project (continues processing despite failure)
        assertThat(results).hasSize(1);
        assertThat(results.get(0).data().getTitle()).isEqualTo("Alton by Nshama"); // The mock always returns same detail
    }

    @Test
    void fetchProjectsPage_WhenProjectConversionFails_ShouldContinueWithOtherProjects() throws JsonProcessingException {
        // Given: Mock successful responses but enrichment service fails for first project
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup mocks: enrichment fails for first project, succeeds for second
        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug(any())).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class)))
            .thenThrow(new RuntimeException("Enrichment failed"))
            .thenReturn(mockEnrichedInfo);

        // When: Fetch projects from page 1
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should return both projects - one with enrichment failure handled gracefully, one with successful enrichment
        assertThat(results).hasSize(2);
    }

    @Test
    void fetchProjectsPage_WhenClientThrowsException_ShouldThrowRetryableException() {
        // Given: Mock client throws exception
        when(aroClient.getProjects(1, 30)).thenThrow(new RuntimeException("Network error"));

        // When & Then: Should wrap in RetryableCrawlerException
        assertThatThrownBy(() -> aroApiService.fetchProjectsPage(1))
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Error fetching page: 1")
            .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void fetchProjectsPage_WithDifferentPageSizes_ShouldHandleCorrectly() throws JsonProcessingException {
        // Given: Mock response that simulates different page sizes
        AroApiResponse<ProjectSummary> mockResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup all required mocks
        when(aroClient.getProjects(5, 30)).thenReturn(mockResponse);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(mockProjectDetail);
        when(aroClient.getProjectBySlug("skyhills-residences-3")).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class))).thenReturn(mockEnrichedInfo);

        // When: Fetch projects from page 5
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(5);

        // Then: Should process correctly regardless of page number
        assertThat(results).hasSize(2); // Based on mock data
    }

    @Test
    void fetchProjectsPage_WithRealProjectData_ShouldCreateValidUrns() throws JsonProcessingException {
        // Given: Mock realistic project data from Postman collection
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug(any())).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class))).thenReturn(mockEnrichedInfo);

        // When: Fetch projects
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should create proper URN structure based on ARO patterns
        assertThat(results).hasSize(2);
        
        for (CrawlRecord<ProjectModel> record : results) {
            ProjectModel project = record.data();
            
            // Validate URN structure follows ARO.ae patterns
            assertThat(project.getSourceUrn()).isEqualTo(AroCommon.SOURCE_URN);
            assertThat(project.getDeveloperUrn()).startsWith(AroCommon.SOURCE_URN + ":developer:");
            assertThat(project.getProjectUrn()).startsWith(project.getDeveloperUrn() + ":project:");
            assertThat(project.getCurrency()).isEqualTo("AED");
            
            // Validate CrawlRecord structure
            assertThat(record.source()).isEqualTo(AroCommon.SOURCE_URN);
            assertThat(record.urn()).isEqualTo(project.getProjectUrn());
        }
    }

    @Test
    void fetchProjectsPage_WithUnitTemplates_ShouldAggregateUnitStats() throws JsonProcessingException {
        // Prepare mock project list with one project
        AroApiResponse<ProjectSummary> projectsResp = objectMapper.readValue(MOCK_PROJECTS_RESPONSE,
                objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        projectsResp.setData(projectsResp.getData().subList(0,1)); // keep first project only

        ProjectDetail detail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);

        // --- build two templates ---
        UnitTemplate tpl1 = new UnitTemplate();
        tpl1.setUnitTemplateId(1);
        tpl1.setType("Apartment");
        tpl1.setBedroom(2);
        tpl1.setBathroom(2);
        tpl1.setAvailabilities(3);
        // price
        com.realmond.temporal_service.crawler.uae.aro.model.Price p1 = new com.realmond.temporal_service.crawler.uae.aro.model.Price();
        p1.setCurrencyCode(784);
        p1.setAmount(1200000L);
        tpl1.setPriceFrom(p1);
        // area
        UnitTemplate.AreaRange ar1 = new UnitTemplate.AreaRange();
        UnitTemplate.SizeInfo sFrom1 = new UnitTemplate.SizeInfo();
        sFrom1.setMeasurement("sqft");
        sFrom1.setValue(1000d);
        UnitTemplate.SizeInfo sTo1 = new UnitTemplate.SizeInfo();
        sTo1.setMeasurement("sqft");
        sTo1.setValue(1100d);
        ar1.setFrom(sFrom1);
        ar1.setTo(sTo1);
        tpl1.setArea(ar1);
        // floor
        UnitTemplate.FloorRange fr1 = new UnitTemplate.FloorRange();
        fr1.setFrom(1);
        fr1.setTo(10);
        tpl1.setFloor(fr1);
        tpl1.setMedia(List.of());

        UnitTemplate tpl2 = new UnitTemplate();
        tpl2.setUnitTemplateId(2);
        tpl2.setType("Apartment");
        tpl2.setBedroom(2);
        tpl2.setBathroom(2);
        tpl2.setAvailabilities(2);
        com.realmond.temporal_service.crawler.uae.aro.model.Price p2 = new com.realmond.temporal_service.crawler.uae.aro.model.Price();
        p2.setCurrencyCode(784);
        p2.setAmount(1500000L);
        tpl2.setPriceFrom(p2);
        UnitTemplate.AreaRange ar2 = new UnitTemplate.AreaRange();
        UnitTemplate.SizeInfo sFrom2 = new UnitTemplate.SizeInfo();
        sFrom2.setMeasurement("sqft");
        sFrom2.setValue(1200d);
        UnitTemplate.SizeInfo sTo2 = new UnitTemplate.SizeInfo();
        sTo2.setMeasurement("sqft");
        sTo2.setValue(1300d);
        ar2.setFrom(sFrom2);
        ar2.setTo(sTo2);
        tpl2.setArea(ar2);
        UnitTemplate.FloorRange fr2 = new UnitTemplate.FloorRange();
        fr2.setFrom(5);
        fr2.setTo(15);
        tpl2.setFloor(fr2);
        tpl2.setMedia(List.of());

        List<UnitTemplate> templates = List.of(tpl1, tpl2);

        AroEnrichmentService.EnrichedProjectInfo enriched = AroEnrichmentService.EnrichedProjectInfo.builder()
                .projectDetail(detail)
                .amenities(List.of())
                .unitStats(List.of()) // aggregated endpoint not used
                .unitTemplates(templates)
                .buildings(List.of())
                .build();

        when(aroClient.getProjects(1, 30)).thenReturn(projectsResp);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(detail);
        when(enrichmentService.enrichProjectDetail(detail)).thenReturn(enriched);

        List<CrawlRecord<ProjectModel>> records = aroApiService.fetchProjectsPage(1);
        assertThat(records).hasSize(1);

        ProjectModel project = records.get(0).data();
        assertThat(project.getProjectStats().getUnits()).isNotEmpty();
        com.realmond.etl.model.UnitStats unit = project.getProjectStats().getUnits().get(0);
        // validate aggregated values
        assertThat(unit.getBedrooms()).isEqualTo(2d);
        assertThat(unit.getAvailabilityCount()).isEqualTo(5); // 3+2
        assertThat(unit.getPriceMin().getValue()).isEqualTo(1200000d);
        assertThat(unit.getPriceMax().getValue()).isEqualTo(1500000d);
        assertThat(unit.getAreaMinSqm()).isNotNull();
        assertThat(unit.getAreaMaxSqm()).isNotNull();
    }

    @Test
    void projectTemplatesDedupAgainstBuilding() throws JsonProcessingException {
        // --- SETUP PROJECT & MOCKS -------------------------------------------------------
        // Limit projects data to the first project only
        AroApiResponse<ProjectSummary> projectsResp = objectMapper.readValue(MOCK_PROJECTS_RESPONSE,
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        projectsResp.setData(projectsResp.getData().subList(0, 1));

        ProjectDetail detail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);

        // Create duplicate & unique unit templates
        UnitTemplate duplicateTpl = new UnitTemplate();
        duplicateTpl.setUnitTemplateId(100);
        duplicateTpl.setType("Apartment");
        duplicateTpl.setBedroom(2);
        duplicateTpl.setMedia(List.of("https://example.com/dup-main.webp"));

        UnitTemplate uniqueTpl = new UnitTemplate();
        uniqueTpl.setUnitTemplateId(200);
        uniqueTpl.setType("Apartment");
        uniqueTpl.setBedroom(1);
        uniqueTpl.setMedia(List.of("https://example.com/uniq-main.webp"));

        List<UnitTemplate> projectTemplates = List.of(duplicateTpl, uniqueTpl);

        // Mock EnrichedProjectInfo with building list
        int buildingId = 1;
        Building building = new Building();
        building.setId(buildingId);

        AroEnrichmentService.EnrichedProjectInfo enrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
                .projectDetail(detail)
                .amenities(List.of())
                .unitStats(List.of())
                .unitTemplates(projectTemplates)
                .buildings(List.of(building))
                .build();

        // Mock external service interactions
        when(aroClient.getProjects(1, 30)).thenReturn(projectsResp);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(detail);
        when(enrichmentService.enrichProjectDetail(detail)).thenReturn(enrichedInfo);

        // Duplicate template returned at building level (used for de-duplication set)
        when(buildingApiService.fetchFloorPlansForBuilding(buildingId)).thenReturn(List.of(duplicateTpl));

        // Use a REAL converter for the service under test
        AroFloorPlanConverter realConverter = new AroFloorPlanConverter();
        AroApiService service = new AroApiService(aroClient, enrichmentService, settings, buildingApiService, realConverter);

        // When: Fetch projects
        List<CrawlRecord<ProjectModel>> results = service.fetchProjectsPage(1);

        // Then: Should create proper URN structure based on ARO patterns
        assertThat(results).hasSize(1);
        
        for (CrawlRecord<ProjectModel> record : results) {
            ProjectModel project = record.data();
            
            // Validate URN structure follows ARO.ae patterns
            assertThat(project.getSourceUrn()).isEqualTo(AroCommon.SOURCE_URN);
            assertThat(project.getDeveloperUrn()).startsWith(AroCommon.SOURCE_URN + ":developer:");
            assertThat(project.getProjectUrn()).startsWith(project.getDeveloperUrn() + ":project:");
            assertThat(project.getCurrency()).isEqualTo("AED");
            
            // Validate CrawlRecord structure
            assertThat(record.source()).isEqualTo(AroCommon.SOURCE_URN);
            assertThat(record.urn()).isEqualTo(project.getProjectUrn());

            // -----------------------------------
            // Assert de-duplication logic
            // -----------------------------------
            List<FloorPlanModel> projectPlans = ((List<?>) project.getFloorPlans()).stream()
                    .map(fp -> (FloorPlanModel) fp)
                    .collect(Collectors.toList());
            assertThat(projectPlans).hasSize(1);
            assertThat(projectPlans.get(0).getExternalId()).isEqualTo("200"); // unique only

            // Build a representative BuildingModel manually to assert duplicate presence
            String buildingUrn = AroCommon.Building.urn(buildingId);
            FloorPlanModel buildingFloorPlan = realConverter.convert(duplicateTpl, project.getProjectUrn(), buildingUrn).orElseThrow();
            BuildingModel buildingModel = BuildingModel.builder()
                    .withBuildingUrn(buildingUrn)
                    .withSourceUrn(AroCommon.SOURCE_URN)
                    .withExternalId(String.valueOf(buildingId))
                    .withProjectUrn(project.getProjectUrn())
                    .withAdditionalProperty("floor_plans", List.of(buildingFloorPlan))
                    .build();

            @SuppressWarnings("unchecked")
            List<FloorPlanModel> bldPlans = (List<FloorPlanModel>) buildingModel.getAdditionalProperties().get("floor_plans");
            assertThat(bldPlans).hasSize(1);
            assertThat(bldPlans.get(0).getExternalId()).isEqualTo("100");
            assertThat(bldPlans.get(0).getBuildingUrn()).isEqualTo(buildingUrn);
        }
    }

    @Test
    void urnHelpersProduceConsistentValues() throws JsonProcessingException {
        // --- Prepare single project page -----------------------------------------------
        AroApiResponse<ProjectSummary> projectsResp = objectMapper.readValue(MOCK_PROJECTS_RESPONSE,
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        projectsResp.setData(projectsResp.getData().subList(0, 1)); // first project only

        ProjectDetail detail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);

        // ---- Building & template setup -------------------------------------------------
        int buildingId = 123;
        Building building = new Building();
        building.setId(buildingId);

        UnitTemplate tpl = new UnitTemplate();
        tpl.setUnitTemplateId(555);
        tpl.setType("Apartment");
        tpl.setBedroom(1);
        tpl.setMedia(List.of("https://example.com/main.webp"));

        // Enriched info provides building list but leaves templates empty (they come from building API)
        AroEnrichmentService.EnrichedProjectInfo enrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
                .projectDetail(detail)
                .amenities(List.of())
                .unitStats(List.of())
                .unitTemplates(List.of())
                .buildings(List.of(building))
                .build();

        // --- Mock interactions ---------------------------------------------------------
        when(aroClient.getProjects(1, 30)).thenReturn(projectsResp);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(detail);
        when(enrichmentService.enrichProjectDetail(detail)).thenReturn(enrichedInfo);
        when(buildingApiService.fetchFloorPlansForBuilding(buildingId)).thenReturn(List.of(tpl));

        // Use real converter for accurate URN generation
        AroFloorPlanConverter realConverter = new AroFloorPlanConverter();
        AroApiService service = new AroApiService(aroClient, enrichmentService, settings, buildingApiService, realConverter);

        // --- Execute --------------------------------------------------------------------
        List<CrawlRecord<ProjectModel>> results = service.fetchProjectsPage(1);
        assertThat(results).hasSize(1);
        ProjectModel project = results.get(0).data();

        // --- Assertions -----------------------------------------------------------------
        String developerSlug = "nshama"; // slugify("Nshama")
        String expectedProjectUrn = AroCommon.Project.urn(developerSlug, detail.getSlug());
        assertThat(project.getProjectUrn()).isEqualTo(expectedProjectUrn);

        // Building URN should be as per helper
        String expectedBuildingUrn = AroCommon.Building.urn(buildingId);
        assertThat(expectedBuildingUrn).isNotBlank();

        // FloorPlan URN via converter
        FloorPlanModel plan = realConverter.convert(tpl, project.getProjectUrn(), expectedBuildingUrn).orElseThrow();
        String expectedFloorPlanUrn = AroCommon.FloorPlan.urn(tpl.getUnitTemplateId());
        assertThat(plan.getFloorplanUrn()).isEqualTo(expectedFloorPlanUrn);
        assertThat(plan.getBuildingUrn()).isEqualTo(expectedBuildingUrn);
    }
} 