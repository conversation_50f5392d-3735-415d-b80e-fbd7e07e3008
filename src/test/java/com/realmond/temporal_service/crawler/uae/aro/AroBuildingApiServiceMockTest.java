package com.realmond.temporal_service.crawler.uae.aro;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.BuildingModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.AroCommon;
import com.realmond.temporal_service.crawler.uae.aro.model.*;
import com.realmond.temporal_service.crawler.uae.aro.AroFloorPlanConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * Integration-style tests for {@link AroBuildingApiService} using mocked ARO API responses taken
 * from the public Postman collection.  The goal is to ensure correct page-id encoding, decoding and
 * building conversion logic without hitting the real network.
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = org.mockito.quality.Strictness.LENIENT)
@SpringBootTest(classes = {AroBuildingApiServiceMockTest.TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
class AroBuildingApiServiceMockTest {

    static class TestConfig {
        // no beans needed – we wire by hand via constructor
    }

    @Mock private AroFeignRestClient aroClient;
    @Mock private AroSettings settings;
    @Mock private AroFloorPlanConverter aroFloorPlanConverter;

    private AroBuildingApiService service;
    private ObjectMapper mapper;

    // ---------------------------------------------------------------------
    // Fixture JSON (trimmed from Postman collection)
    // ---------------------------------------------------------------------
    private static final String PROJECTS_PAGE_1 = """
        {
          "data": [
            {
              "id": 3088,
              "title": "Alton by Nshama",
              "slug": "alton-by-nshama",
              "developer": { "title": "Nshama", "id": 403 }
            },
            {
              "id": 3081,
              "title": "Pier Point 2",
              "slug": "pier-point-2",
              "developer": { "title": "Emaar Properties", "id": 397 }
            }
          ],
          "paging": { "total": 1, "size": 30, "number": 1 },
          "total": 60,
          "left": 30,
          "count": 30
        }
        """;

    private static final String BUILDINGS_FOR_3088 = """
        [
          {
            "id": 178100,
            "building_template_id": 4464,
            "number": "Tower A",
            "position": {"type":"Point","coordinates":[55.28,25.26,10]},
            "category": "Apartments",
            "price_from": {"currency_code":784,"amount":1182888},
            "availabilities_count": 1
          }
        ]
        """;

    private static final String BUILDING_DETAIL_178100 = """
        {
          "id": 178100,
          "center_position": {"type":"Point","coordinates":[55.28,25.26]},
          "geobox": {"type":"Polygon","coordinates":[[[55.28,25.26],[55.28,25.27],[55.29,25.27]]]},
          "scale": 0.025,
          "rotation": -2.16,
          "altitude": 3.5,
          "baked_urls": {"glb":{"32":"model/32.glb"}},
          "optimized_url": "https://cdn/model.glb"
        }
        """;

    @BeforeEach
    void setup() throws Exception {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        when(settings.getPageSize()).thenReturn(30);
        service = new AroBuildingApiService(aroClient, settings, aroFloorPlanConverter);

        // Mock project list
        AroApiResponse<ProjectSummary> projResp = mapper.readValue(PROJECTS_PAGE_1,
                mapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        when(aroClient.getProjects(anyInt(), eq(30))).thenReturn(projResp);

        // Mock building list for project 3088
        List<Building> bldgs3088 = mapper.readValue(BUILDINGS_FOR_3088, new TypeReference<>(){});
        when(aroClient.getProjectBuildings(eq(3088), anyString())).thenReturn(bldgs3088);

        // Mock detail
        BuildingDetail detail = mapper.readValue(BUILDING_DETAIL_178100, BuildingDetail.class);
        when(aroClient.getBuildingById(178100)).thenReturn(detail);

        // For second project (3081) return empty list to keep test small
        when(aroClient.getProjectBuildings(eq(3081), anyString())).thenReturn(List.of());
    }

    @Test
    void fetchAllProjectPageIds_shouldEncodeExpectedIds() {
        List<String> ids = service.fetchAllProjectPageIds();
        assertThat(ids).hasSize(2);
        // The encoded form should begin with id followed by delimiter
        assertThat(ids.get(0)).startsWith("3088|");
    }

    @Test
    void fetchBuildingsForProject_shouldReturnCrawlRecords() {
        String encodedId = service.fetchAllProjectPageIds().get(0); // first project (3088)
        List<CrawlRecord<BuildingModel>> records = service.fetchBuildingsForProject(encodedId);

        assertThat(records).hasSize(1);
        BuildingModel model = records.get(0).data();

        // URNs
        assertThat(model.getBuildingUrn()).isEqualTo(AroCommon.Building.urn(178100));
        assertThat(model.getProjectUrn()).startsWith(AroCommon.Developer.urn("nshama") + ":project:");

        // Property type mapping
        assertThat(model.getAdditionalProperties()).containsKey("category");
        assertThat(model.getAdditionalProperties()).containsEntry("category", "Apartments");
    }

    @Test
    void enumerationFailure_shouldThrowRetryable() {
        // Make the projects endpoint throw – should propagate as RetryableCrawlerException
        when(aroClient.getProjects(1, 30)).thenThrow(new RuntimeException("network"));
        assertThatThrownBy(() -> service.fetchAllProjectPageIds())
                .isInstanceOf(RetryableCrawlerException.class);
    }
} 