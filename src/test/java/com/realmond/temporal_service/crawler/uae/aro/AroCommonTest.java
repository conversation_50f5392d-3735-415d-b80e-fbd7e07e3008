package com.realmond.temporal_service.crawler.uae.aro;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;

class AroCommonTest {

    @Test
    void testDeveloperUrn() {
        String developerSlug = "emaar";
        String expectedUrn = "urn:source:aro:developer:emaar";
        assertEquals(expectedUrn, AroCommon.Developer.urn(developerSlug));
    }

    @Test
    void testProjectUrn() {
        String developerSlug = "emaar";
        String projectSlug = "downtown-dubai";
        String expectedUrn = "urn:source:aro:developer:emaar:project:downtown-dubai";
        assertEquals(expectedUrn, AroCommon.Project.urn(developerSlug, projectSlug));
    }

    @Test
    void testBuildingUrn() {
        int buildingId = 12345;
        String expectedUrn = "urn:source:aro:building:12345";
        assertEquals(expectedUrn, AroCommon.Building.urn(buildingId));
    }

    @Test
    void testFloorPlanUrn() {
        int templateId = 67890;
        String expectedUrn = "urn:source:aro:floorplan:67890";
        assertEquals(expectedUrn, AroCommon.FloorPlan.urn(templateId));
    }
} 