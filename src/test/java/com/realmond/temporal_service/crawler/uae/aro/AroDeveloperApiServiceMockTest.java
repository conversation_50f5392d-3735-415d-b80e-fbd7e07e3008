package com.realmond.temporal_service.crawler.uae.aro;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Developer;
import com.realmond.temporal_service.crawler.uae.aro.model.DeveloperDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Integration test for {@link AroDeveloperApiService} using realistic mock data from the ARO.ae Postman collection.
 * 
 * This test simulates real API responses to ensure proper data transformation and error handling for developers.
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = {AroDeveloperApiServiceMockTest.TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
class AroDeveloperApiServiceMockTest {

    // Test configuration that provides only the needed beans
    static class TestConfig {
        // Minimal configuration for this test
    }

    @Mock
    private AroFeignRestClient aroClient;

    @Mock
    private AroSettings settings;

    private AroDeveloperApiService aroDeveloperApiService;
    private ObjectMapper objectMapper;

    // Mock data based on the Postman collection for developers
    private static final String MOCK_DEVELOPERS_RESPONSE = """
        {
            "data": [
                {
                    "id": 403,
                    "title": "Nshama",
                    "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp"
                },
                {
                    "id": 1,
                    "title": "Emaar Properties",
                    "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/emaar-properties-logo.webp"
                },
                {
                    "id": 28,
                    "title": "HRE Development",
                    "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/7c79003f-07df-427c-8806-de5db5e8fcb1.webp"
                }
            ],
            "paging": {
                "total": 5,
                "size": 30,
                "number": 1
            },
            "total": 150,
            "left": 120,
            "count": 30
        }""";

    private static final String MOCK_DEVELOPER_DETAIL_NSHAMA = """
        {
            "id": 403,
            "slug": "nshama",
            "title": "Nshama",
            "description": "Nshama is a leading real estate developer in Dubai with a focus on creating vibrant communities that enhance the quality of life for residents. Known for innovative designs and sustainable development practices.",
            "address": "Dubai, Business Bay, Executive Heights",
            "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
            "founded": "2014-06-15T00:00:00.000Z",
            "employees_count": 250,
            "valuation": "2.5 billion AED",
            "government_relation": "private",
            "awards": [
                {
                    "name": "Best Developer Award 2023",
                    "nominee": "Dubai Real Estate Awards"
                },
                {
                    "name": "Innovation in Design 2022",
                    "nominee": "Arabian Property Awards"
                }
            ],
            "finished_projects_count": 15,
            "flagship_projects": [
                {
                    "name": "Town Square",
                    "description": "A family-oriented community with parks, retail, and entertainment"
                },
                {
                    "name": "The Pulse",
                    "description": "Modern residential community in South Dubai"
                }
            ],
            "presence": ["Dubai", "Abu Dhabi", "Sharjah"],
            "website_url": "https://www.nshama.com",
            "available_projects": 8
        }""";

    private static final String MOCK_DEVELOPER_DETAIL_EMAAR = """
        {
            "id": 1,
            "slug": "emaar-properties",
            "title": "Emaar Properties",
            "description": "Emaar Properties PJSC is a Public Joint Stock Company listed on the Dubai Financial Market and is one of the world's largest real estate companies.",
            "address": "Dubai, Downtown Dubai, Emaar Boulevard",
            "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/emaar-properties-logo.webp",
            "founded": "1997-07-15T00:00:00.000Z",
            "employees_count": 5000,
            "valuation": "15.2 billion AED",
            "government_relation": "public",
            "awards": [
                {
                    "name": "Developer of the Year 2023",
                    "nominee": "International Property Awards"
                }
            ],
            "finished_projects_count": 85,
            "flagship_projects": [
                {
                    "name": "Burj Khalifa",
                    "description": "World's tallest building"
                },
                {
                    "name": "Downtown Dubai",
                    "description": "Premier urban development"
                }
            ],
            "presence": ["Dubai", "Abu Dhabi", "Egypt", "Saudi Arabia", "India"],
            "website_url": "https://www.emaar.com",
            "available_projects": 25
        }""";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // For Java 8 time support
        
        // Setup default settings behavior
        when(settings.getPageSize()).thenReturn(30);
        
        aroDeveloperApiService = new AroDeveloperApiService(aroClient, settings);
    }

    @Test
    void fetchDeveloperPagesCount_WithValidResponse_ShouldReturnCorrectPageCount() throws JsonProcessingException {
        // Given: Mock response with pagination info from Postman collection
        AroApiResponse<Developer> mockResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        when(aroClient.getDevelopers(1, 30)).thenReturn(mockResponse);

        // When: Fetch developer pages count
        int pagesCount = aroDeveloperApiService.fetchDeveloperPagesCount();

        // Then: Should return the total pages from the pagination info
        assertThat(pagesCount).isEqualTo(5);
    }

    @Test
    void fetchDeveloperPagesCount_WithNoPagingInfo_ShouldReturnOne() {
        // Given: Mock response without pagination info
        AroApiResponse<Developer> mockResponseNoPaging = new AroApiResponse<>();
        mockResponseNoPaging.setData(List.of());
        mockResponseNoPaging.setPaging(null);
        
        when(aroClient.getDevelopers(1, 30)).thenReturn(mockResponseNoPaging);

        // When: Fetch developer pages count
        int pagesCount = aroDeveloperApiService.fetchDeveloperPagesCount();

        // Then: Should return 1 as default
        assertThat(pagesCount).isEqualTo(1);
    }

    @Test
    void fetchDeveloperPagesCount_WhenClientThrowsException_ShouldThrowRetryableException() {
        // Given: Mock client throws exception
        when(aroClient.getDevelopers(1, 30)).thenThrow(new RuntimeException("API error"));

        // When & Then: Should wrap in RetryableCrawlerException
        assertThatThrownBy(() -> aroDeveloperApiService.fetchDeveloperPagesCount())
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Error fetching developer pages count")
            .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void fetchDevelopersPage_WithValidResponse_ShouldReturnCrawlRecords() throws JsonProcessingException {
        // Given: Mock successful responses from ARO API
        AroApiResponse<Developer> mockDevelopersResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        DeveloperDetail mockDeveloperDetailNshama = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_NSHAMA, DeveloperDetail.class);
        DeveloperDetail mockDeveloperDetailEmaar = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_EMAAR, DeveloperDetail.class);
        
        // Create a specific DeveloperDetail for HRE Development with correct ID
        DeveloperDetail mockDeveloperDetailHRE = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_EMAAR, DeveloperDetail.class);
        mockDeveloperDetailHRE.setId(28);
        mockDeveloperDetailHRE.setTitle("HRE Development");
        mockDeveloperDetailHRE.setSlug("hre-development");
        
        // Setup mocks - developers should succeed with their specific details
        when(aroClient.getDevelopers(1, 30)).thenReturn(mockDevelopersResponse);
        when(aroClient.getDeveloperById(403)).thenReturn(mockDeveloperDetailNshama);
        when(aroClient.getDeveloperById(1)).thenReturn(mockDeveloperDetailEmaar);
        when(aroClient.getDeveloperById(28)).thenReturn(mockDeveloperDetailHRE);

        // When: Fetch developers from page 1
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(1);

        // Then: Should return crawl records for all developers on the page
        assertThat(results).hasSize(3);
        
        // Validate first developer (Nshama)
        CrawlRecord<DeveloperModel> firstRecord = results.get(0);
        assertThat(firstRecord.data().getTitle()).isEqualTo("Nshama");
        assertThat(firstRecord.data().getExternalId()).isEqualTo("403");
        assertThat(firstRecord.data().getSourceUrn()).isEqualTo(AroCommon.SOURCE_URN);
        assertThat(firstRecord.source()).isEqualTo(AroCommon.SOURCE_URN);
        
        // Validate metadata contains raw API data
        assertThat(firstRecord.metadata()).containsKey("raw_data");
        assertThat(firstRecord.metadata()).containsKey("conversion_timestamp");
        assertThat(firstRecord.metadata()).containsKey("source_api");
        assertThat(firstRecord.metadata().get("source_api")).isEqualTo("aro.ae");
        
        // Validate second developer (Emaar Properties)
        CrawlRecord<DeveloperModel> secondRecord = results.get(1);
        assertThat(secondRecord.data().getTitle()).isEqualTo("Emaar Properties");
        assertThat(secondRecord.data().getExternalId()).isEqualTo("1");
        
        // Validate third developer (HRE Development)
        CrawlRecord<DeveloperModel> thirdRecord = results.get(2);
        assertThat(thirdRecord.data().getTitle()).isEqualTo("HRE Development");
        assertThat(thirdRecord.data().getExternalId()).isEqualTo("28");
    }

    @Test
    void fetchDevelopersPage_WithEmptyResponse_ShouldReturnEmptyList() {
        // Given: Mock empty response
        AroApiResponse<Developer> emptyResponse = new AroApiResponse<>();
        emptyResponse.setData(List.of());
        
        when(aroClient.getDevelopers(99, 30)).thenReturn(emptyResponse);

        // When: Fetch developers from page 99
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(99);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void fetchDevelopersPage_WithNullData_ShouldReturnEmptyList() {
        // Given: Mock response with null data
        AroApiResponse<Developer> nullDataResponse = new AroApiResponse<>();
        nullDataResponse.setData(null);
        
        when(aroClient.getDevelopers(99, 30)).thenReturn(nullDataResponse);

        // When: Fetch developers from page 99
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(99);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void fetchDevelopersPage_WhenDeveloperDetailFails_ShouldContinueWithOtherDevelopers() throws JsonProcessingException {
        // Given: Mock successful developer list but failing developer detail for first developer
        AroApiResponse<Developer> mockDevelopersResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        DeveloperDetail mockDeveloperDetail = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_EMAAR, DeveloperDetail.class);

        // Setup mocks: first developer fails, second and third succeed
        when(aroClient.getDevelopers(1, 30)).thenReturn(mockDevelopersResponse);
        when(aroClient.getDeveloperById(403))
            .thenThrow(new RuntimeException("Developer detail fetch failed"));
        when(aroClient.getDeveloperById(1)).thenReturn(mockDeveloperDetail);
        when(aroClient.getDeveloperById(28)).thenReturn(mockDeveloperDetail);

        // When: Fetch developers from page 1
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(1);

        // Then: Should return only the successful developers (continues processing despite failure)
        assertThat(results).hasSize(2);
        assertThat(results.get(0).data().getTitle()).isEqualTo("Emaar Properties");
        assertThat(results.get(1).data().getTitle()).isEqualTo("Emaar Properties");
    }

    @Test
    void fetchDevelopersPage_WhenDeveloperConversionFails_ShouldContinueWithOtherDevelopers() throws JsonProcessingException {
        // Given: Mock successful responses but with invalid data that causes conversion failure
        AroApiResponse<Developer> mockDevelopersResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        DeveloperDetail mockValidDeveloperDetail = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_EMAAR, DeveloperDetail.class);
        
        // Create invalid developer detail that should cause conversion issues
        DeveloperDetail mockInvalidDeveloperDetail = new DeveloperDetail();
        mockInvalidDeveloperDetail.setId(null); // This should cause conversion to fail
        mockInvalidDeveloperDetail.setTitle(null);

        // Setup mocks: first developer has invalid data, others succeed
        when(aroClient.getDevelopers(1, 30)).thenReturn(mockDevelopersResponse);
        when(aroClient.getDeveloperById(403)).thenReturn(mockInvalidDeveloperDetail);
        when(aroClient.getDeveloperById(1)).thenReturn(mockValidDeveloperDetail);
        when(aroClient.getDeveloperById(28)).thenReturn(mockValidDeveloperDetail);

        // When: Fetch developers from page 1
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(1);

        // Then: Should return only the successfully converted developers
        assertThat(results).hasSize(2);
        assertThat(results.get(0).data().getTitle()).isEqualTo("Emaar Properties");
        assertThat(results.get(1).data().getTitle()).isEqualTo("Emaar Properties");
    }

    @Test
    void fetchDevelopersPage_WhenClientThrowsException_ShouldThrowRetryableException() {
        // Given: Mock client throws exception
        when(aroClient.getDevelopers(1, 30)).thenThrow(new RuntimeException("Network error"));

        // When & Then: Should wrap in RetryableCrawlerException
        assertThatThrownBy(() -> aroDeveloperApiService.fetchDevelopersPage(1))
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Error fetching developer page: 1")
            .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void fetchDevelopersPage_WithDifferentPageSizes_ShouldHandleCorrectly() throws JsonProcessingException {
        // Given: Mock response that simulates different page sizes
        AroApiResponse<Developer> mockResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        DeveloperDetail mockDeveloperDetail = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_NSHAMA, DeveloperDetail.class);

        // Setup all required mocks
        when(aroClient.getDevelopers(5, 30)).thenReturn(mockResponse);
        when(aroClient.getDeveloperById(anyInt())).thenReturn(mockDeveloperDetail);

        // When: Fetch developers from page 5
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(5);

        // Then: Should process correctly regardless of page number
        assertThat(results).hasSize(3); // Based on mock data
    }

    @Test
    void fetchDevelopersPage_WithRealDeveloperData_ShouldCreateValidUrns() throws JsonProcessingException {
        // Given: Mock realistic developer data from Postman collection
        AroApiResponse<Developer> mockDevelopersResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        DeveloperDetail mockDeveloperDetail = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_NSHAMA, DeveloperDetail.class);

        when(aroClient.getDevelopers(1, 30)).thenReturn(mockDevelopersResponse);
        when(aroClient.getDeveloperById(anyInt())).thenReturn(mockDeveloperDetail);

        // When: Fetch developers
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(1);

        // Then: Should create proper URN structure based on ARO patterns
        assertThat(results).hasSize(3);
        
        for (CrawlRecord<DeveloperModel> record : results) {
            DeveloperModel developer = record.data();
            
            // Validate URN structure follows ARO.ae patterns
            assertThat(developer.getSourceUrn()).isEqualTo(AroCommon.SOURCE_URN);
            assertThat(developer.getDeveloperUrn()).startsWith(AroCommon.SOURCE_URN + ":developer:");
            
            // Validate CrawlRecord structure
            assertThat(record.source()).isEqualTo(AroCommon.SOURCE_URN);
            assertThat(record.urn()).isEqualTo(developer.getDeveloperUrn());
        }
    }

    @Test
    void fetchDevelopersPage_WithComplexDeveloperData_ShouldMapAllFields() throws JsonProcessingException {
        // Given: Mock comprehensive developer data
        AroApiResponse<Developer> mockDevelopersResponse = objectMapper.readValue(MOCK_DEVELOPERS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, Developer.class));
        
        DeveloperDetail mockDeveloperDetail = objectMapper.readValue(MOCK_DEVELOPER_DETAIL_NSHAMA, DeveloperDetail.class);

        when(aroClient.getDevelopers(1, 30)).thenReturn(mockDevelopersResponse);
        when(aroClient.getDeveloperById(403)).thenReturn(mockDeveloperDetail);

        // When: Fetch developers
        List<CrawlRecord<DeveloperModel>> results = aroDeveloperApiService.fetchDevelopersPage(1);

        // Then: Should map all available fields correctly
        assertThat(results).isNotEmpty();
        
        // Find the Nshama developer record
        CrawlRecord<DeveloperModel> nshamaRecord = results.stream()
            .filter(r -> r.data().getExternalId().equals("403"))
            .findFirst()
            .orElseThrow();
        
        DeveloperModel nshama = nshamaRecord.data();
        
        // Validate basic fields
        assertThat(nshama.getTitle()).isEqualTo("Nshama");
        assertThat(nshama.getDescription()).contains("Nshama is a leading real estate developer");
        assertThat(nshama.getLogoUrl()).contains("24acfc98-183a-4172-b85f-1586044ba625.webp");
        assertThat(nshama.getWebsite()).isEqualTo("https://www.nshama.com");
        
        // Validate additional data contains complex structures
        @SuppressWarnings("unchecked")
        Map<String, Object> additionalData = (Map<String, Object>) nshama.getAdditionalData();
        assertThat(additionalData).containsKey("awards");
        assertThat(additionalData).containsKey("flagship_projects");
        assertThat(additionalData).containsKey("presence");
        assertThat(additionalData).containsKey("employee_count");
        assertThat(additionalData).containsKey("valuation");
        
        // Validate metadata structure
        assertThat(nshamaRecord.metadata()).containsKey("raw_data");
        @SuppressWarnings("unchecked")
        Map<String, Object> rawData = (Map<String, Object>) nshamaRecord.metadata().get("raw_data");
        assertThat(rawData).containsKey("developer_summary");
        assertThat(rawData).containsKey("developer_detail");
    }
} 