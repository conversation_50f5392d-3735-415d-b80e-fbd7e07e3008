package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

/**
 * Unit tests for {@link AroDeveloperCrawler} using mocked dependencies.
 * 
 * This test suite covers all public methods of AroDeveloperCrawler and uses
 * realistic data patterns from the ARO.ae API developer responses found in the Postman collection.
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class AroDeveloperCrawlerTest {

    @Mock
    private AroDeveloperApiService aroDeveloperApiService;

    @InjectMocks
    private AroDeveloperCrawler aroDeveloperCrawler;

    private CrawlRecord<DeveloperModel> sampleCrawlRecord;
    private DeveloperModel sampleDeveloper;

    @BeforeEach
    void setUp() {
        // Create a sample DeveloperModel based on the Postman collection data structure
        sampleDeveloper = createSampleDeveloperModel();
        
        // Create a sample CrawlRecord with metadata
        Map<String, Object> metadata = Map.of(
            "conversion_timestamp", "2025-01-14T10:00:00Z",
            "source_api", "aro.ae",
            "api_version", "v1",
            "raw_data", Map.of(
                "developer_summary", Map.of(
                    "id", 403,
                    "title", "Nshama",
                    "logo", "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp"
                ),
                "developer_detail", Map.of(
                    "id", 403,
                    "slug", "nshama",
                    "title", "Nshama",
                    "description", "Leading real estate developer in Dubai...",
                    "finished_projects_count", 15,
                    "available_projects", 8
                ),
                // Example payment plan data structure from Postman collection
                "payment_plan_example", Map.of(
                    "project", Map.of(
                        "title", "Alton by Nshama",
                        "handover_date", "2028-05-31T00:00:00.000Z",
                        "payment_plan", Map.of(
                            "title", "alton",
                            "description", "alton",
                            "fields", List.of(
                                Map.of(
                                    "title", "Down Payment",
                                    "description", "Down Payment",
                                    "type", "percent",
                                    "value", 10,
                                    "group", 0
                                ),
                                Map.of(
                                    "title", "2nd Installment",
                                    "description", "2nd Installment",
                                    "type", "percent",
                                    "value", 10,
                                    "group", 0
                                ),
                                Map.of(
                                    "title", "On Handover",
                                    "description", "On Handover",
                                    "type", "percent",
                                    "value", 50,
                                    "group", 0
                                )
                            )
                        )
                    ),
                    "towers", Collections.emptyList()
                )
            )
        );
        
        sampleCrawlRecord = new CrawlRecord<>(
            sampleDeveloper,
            metadata,
            "aro.ae",
            "aro.ae:developer:nshama"
        );
    }

    @Test
    void getSourceUrn_ShouldReturnCorrectUrn() {
        String sourceUrn = aroDeveloperCrawler.getSourceUrn();
        
        assertThat(sourceUrn).isEqualTo(AroCommon.SOURCE_URN);
    }

    @Test
    void supportsPagination_ShouldReturnTrue() {
        Boolean supportsPagination = aroDeveloperCrawler.supportsPagination();
        
        assertThat(supportsPagination).isTrue();
    }

    @Test
    void fetchAllPageIds_WithValidPagesCount_ShouldReturnCorrectPageIds() {
        // Given: Mock API service returns 5 pages (typical for developers)
        when(aroDeveloperApiService.fetchDeveloperPagesCount()).thenReturn(5);

        // When: Fetch all page IDs
        List<String> pageIds = aroDeveloperCrawler.fetchAllPageIds();

        // Then: Should return page numbers 1 through 5 as strings
        assertThat(pageIds).hasSize(5);
        assertThat(pageIds).containsExactly("1", "2", "3", "4", "5");

        verify(aroDeveloperApiService).fetchDeveloperPagesCount();
    }

    @Test
    void fetchAllPageIds_WithSinglePage_ShouldReturnSinglePageId() {
        // Given: Mock API service returns 1 page
        when(aroDeveloperApiService.fetchDeveloperPagesCount()).thenReturn(1);

        // When: Fetch all page IDs
        List<String> pageIds = aroDeveloperCrawler.fetchAllPageIds();

        // Then: Should return only page "1"
        assertThat(pageIds).hasSize(1);
        assertThat(pageIds).containsExactly("1");

        verify(aroDeveloperApiService).fetchDeveloperPagesCount();
    }

    @Test
    void fetchAllPageIds_WithZeroPages_ShouldReturnEmptyList() {
        // Given: Mock API service returns 0 pages
        when(aroDeveloperApiService.fetchDeveloperPagesCount()).thenReturn(0);

        // When: Fetch all page IDs
        List<String> pageIds = aroDeveloperCrawler.fetchAllPageIds();

        // Then: Should return empty list
        assertThat(pageIds).isEmpty();

        verify(aroDeveloperApiService).fetchDeveloperPagesCount();
    }

    @Test
    void fetchAllPageIds_WhenServiceThrowsRetryableException_ShouldPropagateException() {
        // Given: Mock API service throws RetryableCrawlerException
        when(aroDeveloperApiService.fetchDeveloperPagesCount())
            .thenThrow(new RetryableCrawlerException("API temporarily unavailable"));

        // When & Then: Should propagate the exception
        assertThatThrownBy(() -> aroDeveloperCrawler.fetchAllPageIds())
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("API temporarily unavailable");

        verify(aroDeveloperApiService).fetchDeveloperPagesCount();
    }

    @Test
    void parsePage_WithValidPageNumber_ShouldReturnDevelopersFromPage() {
        // Given: Mock API service returns developers for page 1 (based on Postman data)
        List<CrawlRecord<DeveloperModel>> expectedDevelopers = List.of(sampleCrawlRecord);
        when(aroDeveloperApiService.fetchDevelopersPage(1)).thenReturn(expectedDevelopers);

        // When: Parse page 1
        List<CrawlRecord<DeveloperModel>> result = aroDeveloperCrawler.parsePage("1");

        // Then: Should return the developers from the service
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(sampleCrawlRecord);
        assertThat(result.get(0).data().getTitle()).isEqualTo("Nshama");
        assertThat(result.get(0).source()).isEqualTo("aro.ae");

        verify(aroDeveloperApiService).fetchDevelopersPage(1);
    }

    @Test
    void parsePage_WithEmptyResults_ShouldReturnEmptyList() {
        // Given: Mock API service returns empty list for page 99
        when(aroDeveloperApiService.fetchDevelopersPage(99)).thenReturn(Collections.emptyList());

        // When: Parse page 99
        List<CrawlRecord<DeveloperModel>> result = aroDeveloperCrawler.parsePage("99");

        // Then: Should return empty list
        assertThat(result).isEmpty();

        verify(aroDeveloperApiService).fetchDevelopersPage(99);
    }

    @Test
    void parsePage_WithMultipleDevelopers_ShouldReturnAllDevelopers() {
        // Given: Mock API service returns multiple developers (simulating page with 30 developers from Postman)
        List<CrawlRecord<DeveloperModel>> multipleDevelopers = createMultipleCrawlRecords();
        when(aroDeveloperApiService.fetchDevelopersPage(1)).thenReturn(multipleDevelopers);

        // When: Parse page 1
        List<CrawlRecord<DeveloperModel>> result = aroDeveloperCrawler.parsePage("1");

        // Then: Should return all developers
        assertThat(result).hasSize(3);
        assertThat(result.get(0).data().getTitle()).isEqualTo("Nshama");
        assertThat(result.get(1).data().getTitle()).isEqualTo("Emaar Properties");
        assertThat(result.get(2).data().getTitle()).isEqualTo("HRE Development");

        verify(aroDeveloperApiService).fetchDevelopersPage(1);
    }

    @Test
    void parsePage_WithInvalidPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for invalid page number
        assertThatThrownBy(() -> aroDeveloperCrawler.parsePage("invalid"))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: invalid")
            .hasCauseInstanceOf(NumberFormatException.class);

        // Should not call the service for invalid input
        verifyNoMoreInteractions(aroDeveloperApiService);
    }

    @Test
    void parsePage_WithNullPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for null page number
        assertThatThrownBy(() -> aroDeveloperCrawler.parsePage(null))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: null")
            .hasCauseInstanceOf(NumberFormatException.class);

        verifyNoMoreInteractions(aroDeveloperApiService);
    }

    @Test
    void parsePage_WithEmptyPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for empty page number
        assertThatThrownBy(() -> aroDeveloperCrawler.parsePage(""))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: ")
            .hasCauseInstanceOf(NumberFormatException.class);

        verifyNoMoreInteractions(aroDeveloperApiService);
    }

    @Test
    void parsePage_WithNegativePageNumber_ShouldCallServiceWithNegativeNumber() {
        // Given: Mock API service can handle negative page numbers (may return empty or throw)
        when(aroDeveloperApiService.fetchDevelopersPage(-1)).thenReturn(Collections.emptyList());

        // When: Parse negative page number
        List<CrawlRecord<DeveloperModel>> result = aroDeveloperCrawler.parsePage("-1");

        // Then: Should pass negative number to service
        assertThat(result).isEmpty();
        verify(aroDeveloperApiService).fetchDevelopersPage(-1);
    }

    @Test
    void parsePage_WhenServiceThrowsRetryableException_ShouldPropagateException() {
        // Given: Mock API service throws RetryableCrawlerException
        when(aroDeveloperApiService.fetchDevelopersPage(anyInt()))
            .thenThrow(new RetryableCrawlerException("Network timeout"));

        // When & Then: Should propagate the exception
        assertThatThrownBy(() -> aroDeveloperCrawler.parsePage("1"))
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Network timeout");

        verify(aroDeveloperApiService).fetchDevelopersPage(1);
    }

    @Test
    void fetchAll_ShouldThrowNonRetryableException() {
        // When & Then: Should always throw NonRetryableCrawlerException
        assertThatThrownBy(() -> aroDeveloperCrawler.fetchAll())
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("fetching all entries is not supported");

        // Should not interact with the service
        verifyNoMoreInteractions(aroDeveloperApiService);
    }

    @Test
    void parsePage_WithLargePageNumber_ShouldHandleCorrectly() {
        // Given: Test with large page number that's still valid
        String largePageNumber = "999999";
        when(aroDeveloperApiService.fetchDevelopersPage(999999)).thenReturn(Collections.emptyList());

        // When: Parse large page number
        List<CrawlRecord<DeveloperModel>> result = aroDeveloperCrawler.parsePage(largePageNumber);

        // Then: Should handle correctly
        assertThat(result).isEmpty();
        verify(aroDeveloperApiService).fetchDevelopersPage(999999);
    }

    @Test
    void parsePage_WithPageNumberZero_ShouldCallServiceWithZero() {
        // Given: Mock API service can handle page 0
        when(aroDeveloperApiService.fetchDevelopersPage(0)).thenReturn(Collections.emptyList());

        // When: Parse page 0
        List<CrawlRecord<DeveloperModel>> result = aroDeveloperCrawler.parsePage("0");

        // Then: Should pass 0 to service
        assertThat(result).isEmpty();
        verify(aroDeveloperApiService).fetchDevelopersPage(0);
    }

    @Test
    void crawlRecordMetadata_ShouldContainPaymentPlanExampleStructure() {
        // Given: Sample crawl record with payment plan data in metadata
        CrawlRecord<DeveloperModel> record = sampleCrawlRecord;

        // When: Extract metadata
        Map<String, Object> metadata = record.metadata();
        Map<String, Object> rawData = (Map<String, Object>) metadata.get("raw_data");
        Map<String, Object> paymentPlanExample = (Map<String, Object>) rawData.get("payment_plan_example");

        // Then: Should have proper payment plan structure that matches ARO API response
        assertThat(paymentPlanExample).isNotNull();
        assertThat(paymentPlanExample).containsKey("project");
        assertThat(paymentPlanExample).containsKey("towers");

        Map<String, Object> project = (Map<String, Object>) paymentPlanExample.get("project");
        assertThat(project).containsKey("title");
        assertThat(project).containsKey("handover_date");
        assertThat(project).containsKey("payment_plan");

        Map<String, Object> paymentPlan = (Map<String, Object>) project.get("payment_plan");
        assertThat(paymentPlan).containsKey("title");
        assertThat(paymentPlan).containsKey("description");
        assertThat(paymentPlan).containsKey("fields");

        List<Map<String, Object>> fields = (List<Map<String, Object>>) paymentPlan.get("fields");
        assertThat(fields).hasSize(3);

        // Verify first payment field structure
        Map<String, Object> firstField = fields.get(0);
        assertThat(firstField).containsEntry("title", "Down Payment");
        assertThat(firstField).containsEntry("type", "percent");
        assertThat(firstField).containsEntry("value", 10);
        assertThat(firstField).containsEntry("group", 0);

        // Verify handover payment field
        Map<String, Object> handoverField = fields.get(2);
        assertThat(handoverField).containsEntry("title", "On Handover");
        assertThat(handoverField).containsEntry("value", 50);

        log.info("Payment plan example structure verified - ready for AroApiService conversion");
    }

    // Helper methods to create test data based on Postman collection structure

    private DeveloperModel createSampleDeveloperModel() {
        return DeveloperModel.builder()
            .withTitle("Nshama")
            .withDeveloperUrn("aro.ae:developer:nshama")
            .withSourceUrn("aro.ae")
            .withExternalId("403")
            .withLogoUrl("https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp")
            .withDescription("Leading real estate developer in Dubai with focus on community living...")
            .withWebsite("https://www.nshama.com")
            .build();
    }

    private List<CrawlRecord<DeveloperModel>> createMultipleCrawlRecords() {
        // Create multiple developers based on Postman collection sample data
        DeveloperModel developer1 = DeveloperModel.builder()
            .withTitle("Nshama")
            .withDeveloperUrn("aro.ae:developer:nshama")
            .withSourceUrn("aro.ae")
            .withExternalId("403")
            .withLogoUrl("https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp")
            .build();

        DeveloperModel developer2 = DeveloperModel.builder()
            .withTitle("Emaar Properties")
            .withDeveloperUrn("aro.ae:developer:emaar-properties")
            .withSourceUrn("aro.ae")
            .withExternalId("1")
            .withLogoUrl("https://d1rxmks6dvv2cz.cloudfront.net/developer/emaar-logo.webp")
            .build();

        DeveloperModel developer3 = DeveloperModel.builder()
            .withTitle("HRE Development")
            .withDeveloperUrn("aro.ae:developer:hre-development")
            .withSourceUrn("aro.ae")
            .withExternalId("28")
            .withLogoUrl("https://d1rxmks6dvv2cz.cloudfront.net/developer/7c79003f-07df-427c-8806-de5db5e8fcb1.webp")
            .build();

        Map<String, Object> metadata = Map.of(
            "conversion_timestamp", "2025-01-14T10:00:00Z",
            "source_api", "aro.ae",
            "api_version", "v1"
        );

        return List.of(
            new CrawlRecord<>(developer1, metadata, "aro.ae", developer1.getDeveloperUrn()),
            new CrawlRecord<>(developer2, metadata, "aro.ae", developer2.getDeveloperUrn()),
            new CrawlRecord<>(developer3, metadata, "aro.ae", developer3.getDeveloperUrn())
        );
    }
}