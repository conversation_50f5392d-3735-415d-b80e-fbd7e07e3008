package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Amenity;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import com.realmond.temporal_service.crawler.uae.aro.model.Developer;
import com.realmond.temporal_service.crawler.uae.aro.model.DeveloperDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.PaymentPlanResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectSummary;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitStats;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;

import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for {@link AroFeignRestClient} that hit the real ARO.ae endpoints.
 *
 * NOTE: These tests require internet access. They are disabled by default unless the
 * environment variable "RUN_INTEGRATION_TESTS" is set to "true".
 */
@SpringBootTest(classes = {AroFeignRestClientIT.TestConfig.class}, webEnvironment = WebEnvironment.NONE)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
class AroFeignRestClientIT {

    @Configuration
    @EnableFeignClients(basePackageClasses = AroFeignRestClient.class)
    @Import({
            FingerprintGenerator.class,
            com.realmond.temporal_service.crawler.uae.aro.AroSettings.class})
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {
        // AroFeignConfiguration now handles missing ProxyManager gracefully via ObjectProvider
    }

    @Autowired
    private AroFeignRestClient aroClient;

    private ProjectSummary firstProject;
    private Developer firstDeveloper;

    @BeforeAll
    void setup() {
        AroApiResponse<ProjectSummary> projects = aroClient.getProjects(1, 10);
        assertThat(projects).isNotNull();
        assertThat(projects.getData()).isNotEmpty();
        firstProject = projects.getData().get(0);

        // Setup first developer for developer tests
        AroApiResponse<Developer> developers = aroClient.getDevelopers(1, 10);
        assertThat(developers).isNotNull();
        assertThat(developers.getData()).isNotEmpty();
        firstDeveloper = developers.getData().get(0);
    }

    @Test
    void testGetProjects() {
        AroApiResponse<ProjectSummary> projects = aroClient.getProjects(1, 5);
        assertThat(projects).isNotNull();
        assertThat(projects.getData()).isNotEmpty();
        assertThat(projects.getPaging()).isNotNull();
    }

    @Test
    void testGetProjectDetail() {
        ProjectDetail detail = aroClient.getProjectBySlug(firstProject.getSlug());
        assertThat(detail).isNotNull();
        assertThat(detail.getId()).isEqualTo(firstProject.getId());
        assertThat(detail.getTitle()).isEqualTo(firstProject.getTitle());
    }

    @Test
    void testGetAmenitiesAndUnitStats() {
        List<Amenity> amenities = aroClient.getProjectAmenities(firstProject.getId());
        assertThat(amenities).isNotNull();
        // Amenities list may be empty for some projects

        List<UnitStats> stats = aroClient.getUnitStats(firstProject.getId(), 1, 9);
        assertThat(stats).isNotNull();
    }

    @Test
    void testGetProjectBuildings() {
        List<Building> buildings = aroClient.getProjectBuildings(firstProject.getId(), "sqft");
        assertThat(buildings).isNotNull();
        // API may return empty list for some projects
    }

    @Test
    void testGetBuildingById() {
        // Fetch buildings for the first project to obtain a valid building ID
        List<Building> buildings = aroClient.getProjectBuildings(firstProject.getId(), "sqft");
        assertThat(buildings).isNotNull();

        if (!buildings.isEmpty()) {
            Building firstBuilding = buildings.get(0);
            assertThat(firstBuilding.getId()).isNotNull();

            // Fetch detailed information for the selected building
            com.realmond.temporal_service.crawler.uae.aro.model.BuildingDetail detail =
                    aroClient.getBuildingById(firstBuilding.getId());

            assertThat(detail).isNotNull();
            assertThat(detail.getId()).isEqualTo(firstBuilding.getId());

            // Validate some critical fields (may be null for some buildings)
            assertThat(detail.getHeight()).isNotNull();
            assertThat(detail.getGeobox()).isNotNull();
        }
    }

    @Test
    void testGetDevelopers() {
        AroApiResponse<Developer> developers = aroClient.getDevelopers(1, 5);
        assertThat(developers).isNotNull();
        assertThat(developers.getData()).isNotEmpty();
        assertThat(developers.getPaging()).isNotNull();

        // Validate developer structure
        Developer developer = developers.getData().get(0);
        assertThat(developer.getId()).isNotNull();
        assertThat(developer.getTitle()).isNotNull();
        // Logo and slug may be null for some developers
    }

    @Test
    void testGetDeveloperById() {
        DeveloperDetail detail = aroClient.getDeveloperById(firstDeveloper.getId());
        assertThat(detail).isNotNull();
        assertThat(detail.getId()).isEqualTo(firstDeveloper.getId());
        assertThat(detail.getTitle()).isEqualTo(firstDeveloper.getTitle());

        // Validate detailed developer structure
        assertThat(detail.getSlug()).isNotNull();
        // Other fields may be null for some developers, so we don't assert them
    }

    @Test
    void testGetProjectPaymentPlan() {
        PaymentPlanResponse paymentPlan = aroClient.getProjectPaymentPlan(firstProject.getId());
        assertThat(paymentPlan).isNotNull();

        if (paymentPlan.getProject() != null) {
            assertThat(paymentPlan.getProject().getTitle()).isNotNull();
            // Payment plan may be null for some projects
        }

        // Towers list may be empty
        assertThat(paymentPlan.getTowers()).isNotNull();
    }

    @Test
    void testGetProjectUnitTemplates() {
        AroApiResponse<UnitTemplate> unitTemplates = aroClient.getProjectUnitTemplates(firstProject.getId(), 1, 9);
        assertThat(unitTemplates).isNotNull();
        assertThat(unitTemplates.getData()).isNotNull();
        assertThat(unitTemplates.getPaging()).isNotNull();

        // Unit templates may be empty for some projects
        if (!unitTemplates.getData().isEmpty()) {
            UnitTemplate template = unitTemplates.getData().get(0);
            assertThat(template.getUnitTemplateId()).isNotNull();
            // Other fields may be null for some unit templates
        }
    }

    @Test
    @Disabled
    @EnabledIfEnvironmentVariable(named = "MANUAL_INTEGRATION_TESTS", matches = "true")
    void testExploreAllProjectsAndPropertyTypes() {
        System.out.println("=== ARO.ae API Exploration ===");
        System.out.println("Fetching all projects and analyzing building data...\n");

        Set<String> allPropertyTypes = new HashSet<>();
        Set<String> allAmenityNames = new HashSet<>();
        Map<String, Integer> propertyTypeCount = new HashMap<>();
        Map<String, Integer> unitTypeCount = new HashMap<>();

        int totalProjects = 0;
        int projectsWithBuildings = 0;
        int totalBuildings = 0;

        try {
            // Start with first page to get total count
            AroApiResponse<ProjectSummary> firstPage = aroClient.getProjects(1, 30);
            assertThat(firstPage).isNotNull();

            if (firstPage.getPaging() == null) {
                System.out.println("No pagination info available, testing with first page only");
                return;
            }

            int totalPages = firstPage.getPaging().getTotal();
            System.out.printf("Found %d total projects across %d pages%n%n", firstPage.getPaging().getTotal(), totalPages);

            // Iterate through all pages
            for (int page = 1; page <= totalPages; page++) { // Analyze ALL projects, no limit
                System.out.printf("Processing page %d/%d...%n", page, totalPages);

                AroApiResponse<ProjectSummary> projects = aroClient.getProjects(page, 30);
                if (projects.getData() == null) continue;

                for (ProjectSummary project : projects.getData()) {
                    totalProjects++;

                    try {
                        // Small delay to be respectful to the API
                        Thread.sleep(100);

                        // Get building details for this project
                        List<Building> buildings = aroClient.getProjectBuildings(project.getId(), "sqft");

                        if (buildings != null && !buildings.isEmpty()) {
                            projectsWithBuildings++;
                            totalBuildings += buildings.size();

                            // Collect property types
                            for (Building building : buildings) {
                                if (building.getCategory() != null) {
                                    allPropertyTypes.add(building.getCategory());
                                    propertyTypeCount.merge(building.getCategory(), 1, Integer::sum);
                                }
                            }
                        }

                        // Get amenities for variety
                        try {
                            Thread.sleep(50);
                            List<Amenity> amenities = aroClient.getProjectAmenities(project.getId());
                            if (amenities != null) {
                                for (Amenity amenity : amenities) {
                                    if (amenity.getTitle() != null) {
                                        allAmenityNames.add(amenity.getTitle());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // Some projects might not have amenities, that's ok
                        }

                        // Get unit statistics
                        try {
                            Thread.sleep(50);
                            List<UnitStats> unitStats = aroClient.getUnitStats(project.getId(), 1, 10);
                            if (unitStats != null) {
                                for (UnitStats stat : unitStats) {
                                    String bedrooms = stat.getBedrooms() != null ? stat.getBedrooms() + " BR" : "Unknown BR";
                                    unitTypeCount.merge(bedrooms, stat.getCount() != null ? stat.getCount() : 1, Integer::sum);
                                }
                            }
                        } catch (Exception e) {
                            // Some projects might not have unit stats, that's ok
                        }

                    } catch (Exception e) {
                        System.out.printf("  Error processing project %s: %s%n", project.getTitle(), e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            System.out.printf("Error during exploration: %s%n", e.getMessage());
        }

        // Print comprehensive results
        System.out.println("\n=== EXPLORATION RESULTS ===");
        System.out.printf("Total Projects Analyzed: %d%n", totalProjects);
        System.out.printf("Projects with Buildings: %d%n", projectsWithBuildings);
        System.out.printf("Total Buildings Found: %d%n", totalBuildings);

        System.out.println("\n=== PROPERTY TYPES (BUILDING CATEGORIES) ===");
        allPropertyTypes.stream()
            .sorted()
            .forEach(type -> System.out.printf("- %s (%d buildings)%n", type, propertyTypeCount.get(type)));

        System.out.println("\n=== UNIT TYPES BY BEDROOMS ===");
        unitTypeCount.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> System.out.printf("- %s: %d units%n", entry.getKey(), entry.getValue()));

        if (!allAmenityNames.isEmpty()) {
            System.out.println("\n=== SAMPLE AMENITIES ===");
            allAmenityNames.stream()
                .sorted()
                .limit(20) // Show first 20 amenities
                .forEach(amenity -> System.out.printf("- %s%n", amenity));

            if (allAmenityNames.size() > 20) {
                System.out.printf("... and %d more amenities%n", allAmenityNames.size() - 20);
            }
        }

        System.out.println("\n=== TEST ASSERTIONS ===");
        assertThat(totalProjects).isGreaterThan(0);
        assertThat(allPropertyTypes).isNotEmpty();

        System.out.println("✅ All assertions passed!");
        System.out.println("=== END EXPLORATION ===\n");
    }

    @Test
    @Disabled
    @EnabledIfEnvironmentVariable(named = "MANUAL_INTEGRATION_TESTS", matches = "true")
    void testExploreAllProjectPaymentPlans() {
        System.out.println("=== ARO.ae PAYMENT PLAN API EXPLORATION ===");
        System.out.println("Fetching all projects and analyzing payment plan data...\n");

        Set<String> allPaymentPlanTitles = new HashSet<>();
        Set<String> allPaymentFieldTitles = new HashSet<>();
        Set<String> allPaymentFieldTypes = new HashSet<>();
        Map<String, Integer> paymentFieldTitleCount = new HashMap<>();
        Map<Integer, Integer> paymentFieldValueDistribution = new HashMap<>();
        Map<String, Integer> paymentPlanTitleCount = new HashMap<>();

        int totalProjects = 0;
        int projectsWithPaymentPlans = 0;
        int totalPaymentFields = 0;
        int projectsWithErrors = 0;

        try {
            // Start with first page to get total count
            AroApiResponse<ProjectSummary> firstPage = aroClient.getProjects(1, 30);
            assertThat(firstPage).isNotNull();

            if (firstPage.getPaging() == null) {
                System.out.println("No pagination info available, testing with first page only");
                return;
            }

            int totalPages = firstPage.getPaging().getTotal();
            System.out.printf("Found %d total projects, exploring first %d pages%n%n", firstPage.getTotal(), totalPages);

            // Iterate through pages
            for (int page = 1; page <= totalPages; page++) {
                System.out.printf("Processing page %d/%d...%n", page, totalPages);

                AroApiResponse<ProjectSummary> projects = aroClient.getProjects(page, 30);
                if (projects.getData() == null) continue;

                for (ProjectSummary project : projects.getData()) {
                    totalProjects++;

                    try {
                        // Small delay to be respectful to the API
                        Thread.sleep(150);

                        // Get payment plan for this project
                        PaymentPlanResponse paymentPlan = aroClient.getProjectPaymentPlan(project.getId());

                        if (paymentPlan != null && paymentPlan.getProject() != null &&
                            paymentPlan.getProject().getPaymentPlan() != null) {

                            projectsWithPaymentPlans++;

                            PaymentPlanResponse.PaymentPlan plan = paymentPlan.getProject().getPaymentPlan();

                            // Collect payment plan titles
                            if (plan.getTitle() != null && !plan.getTitle().trim().isEmpty()) {
                                allPaymentPlanTitles.add(plan.getTitle());
                                paymentPlanTitleCount.merge(plan.getTitle(), 1, Integer::sum);
                            }

                            // Analyze payment fields
                            if (plan.getFields() != null && !plan.getFields().isEmpty()) {
                                totalPaymentFields += plan.getFields().size();

                                for (PaymentPlanResponse.PaymentField field : plan.getFields()) {
                                    // Collect field titles
                                    if (field.getTitle() != null && !field.getTitle().trim().isEmpty()) {
                                        allPaymentFieldTitles.add(field.getTitle());
                                        paymentFieldTitleCount.merge(field.getTitle(), 1, Integer::sum);
                                    }

                                    // Collect field types
                                    if (field.getType() != null && !field.getType().trim().isEmpty()) {
                                        allPaymentFieldTypes.add(field.getType());
                                    }

                                    // Collect value distribution
                                    if (field.getValue() != null) {
                                        paymentFieldValueDistribution.merge(field.getValue(), 1, Integer::sum);
                                    }
                                }

                                // Print detailed info for first few payment plans
                                if (projectsWithPaymentPlans <= 3) {
                                    System.out.printf("  📋 Project: %s (ID: %d)%n", project.getTitle(), project.getId());
                                    System.out.printf("     Payment Plan: %s%n", plan.getTitle());
                                    System.out.printf("     Description: %s%n", plan.getDescription());
                                    System.out.printf("     Fields (%d):%n", plan.getFields().size());

                                    for (PaymentPlanResponse.PaymentField field : plan.getFields()) {
                                        System.out.printf("       - %s: %s%% (type: %s, group: %s)%n",
                                            field.getTitle(),
                                            field.getValue(),
                                            field.getType(),
                                            field.getGroup());
                                    }
                                    System.out.println();
                                }
                            }
                        }

                    } catch (Exception e) {
                        projectsWithErrors++;
                        if (projectsWithErrors <= 5) { // Log first few errors
                            System.out.printf("  ❌ Error processing project %s (ID: %d): %s%n",
                                project.getTitle(), project.getId(), e.getMessage());
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.printf("Error during exploration: %s%n", e.getMessage());
        }

        // Print comprehensive results
        System.out.println("\n=== PAYMENT PLAN EXPLORATION RESULTS ===");
        System.out.printf("Total Projects Analyzed: %d%n", totalProjects);
        System.out.printf("Projects with Payment Plans: %d%n", projectsWithPaymentPlans);
        System.out.printf("Projects with Errors: %d%n", projectsWithErrors);
        System.out.printf("Total Payment Fields Found: %d%n", totalPaymentFields);

        System.out.println("\n=== PAYMENT PLAN TITLES ===");
        paymentPlanTitleCount.entrySet().stream()
            .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue())) // Sort by count descending
            .forEach(entry -> System.out.printf("- \"%s\" (%d projects)%n", entry.getKey(), entry.getValue()));

        System.out.println("\n=== PAYMENT FIELD TITLES (Top 20) ===");
        paymentFieldTitleCount.entrySet().stream()
            .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue())) // Sort by count descending
            .limit(20)
            .forEach(entry -> System.out.printf("- \"%s\" (%d occurrences)%n", entry.getKey(), entry.getValue()));

        System.out.println("\n=== PAYMENT FIELD TYPES ===");
        allPaymentFieldTypes.stream()
            .sorted()
            .forEach(type -> System.out.printf("- %s%n", type));

        System.out.println("\n=== PAYMENT FIELD VALUE DISTRIBUTION (Top 15) ===");
        paymentFieldValueDistribution.entrySet().stream()
            .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue())) // Sort by count descending
            .limit(15)
            .forEach(entry -> System.out.printf("- %d%% (%d occurrences)%n", entry.getKey(), entry.getValue()));

        System.out.println("\n=== ANALYSIS INSIGHTS ===");
        double paymentPlanCoverage = totalProjects > 0 ? (double) projectsWithPaymentPlans / totalProjects * 100 : 0;
        System.out.printf("Payment Plan Coverage: %.1f%% of projects%n", paymentPlanCoverage);

        double avgFieldsPerPlan = projectsWithPaymentPlans > 0 ? (double) totalPaymentFields / projectsWithPaymentPlans : 0;
        System.out.printf("Average Fields per Payment Plan: %.1f%n", avgFieldsPerPlan);

        System.out.println("\n=== TEST ASSERTIONS ===");
        assertThat(totalProjects).isGreaterThan(0);
        System.out.printf("✅ Analyzed %d projects successfully%n", totalProjects);

        if (projectsWithPaymentPlans > 0) {
            assertThat(allPaymentFieldTitles).isNotEmpty();
            assertThat(allPaymentFieldTypes).isNotEmpty();
            System.out.printf("✅ Found payment plans in %d projects%n", projectsWithPaymentPlans);
        } else {
            System.out.println("⚠️  No payment plans found - this may indicate API changes or access restrictions");
        }

        System.out.println("=== END PAYMENT PLAN EXPLORATION ===\n");
    }

    @Test
    void testGetBuildingMapUnitTemplates() {
        // Obtain a building ID from the first project
        List<Building> buildings = aroClient.getProjectBuildings(firstProject.getId(), "sqft");
        assertThat(buildings).isNotNull();

        if (!buildings.isEmpty()) {
            int buildingId = buildings.get(0).getId();
            assertThat(buildingId).isNotNull();

            // Fetch geo-scoped floor plans (unit templates) for the selected building
            AroApiResponse<UnitTemplate> response = aroClient.getBuildingMapUnitTemplates(buildingId, 1, 30);
            assertThat(response).isNotNull();
            assertThat(response.getData()).isNotNull();
            assertThat(response.getPaging()).isNotNull();

            if (!response.getData().isEmpty()) {
                UnitTemplate template = response.getData().get(0);
                assertThat(template.getUnitTemplateId()).isNotNull();
                // Other fields may be null depending on availability
            }
        }
    }
}
