package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.common.FloorPlanModel;
import com.realmond.temporal_service.crawler.uae.aro.model.Price;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

public class AroFloorPlanConverterTest {

    private AroFloorPlanConverter converter;

    @BeforeEach
    void setUp() {
        converter = new AroFloorPlanConverter();
    }

    private UnitTemplate createBaseUnitTemplate() {
        UnitTemplate template = new UnitTemplate();
        template.setUnitTemplateId(123);
        template.setBedroom(2);
        template.setType("Apartments");
        template.setMedia(java.util.List.of("http://example.com/image.jpg"));
        return template;
    }

    @Test
    void testSuccessfulConversion_Basic() {
        UnitTemplate template = createBaseUnitTemplate();
        String projectUrn = "urn:source:aro:developer:dev-slug:project:proj-slug";
        String buildingUrn = "urn:source:aro:building:456";

        Optional<FloorPlanModel> result = converter.convert(template, projectUrn, buildingUrn);

        assertTrue(result.isPresent());
        FloorPlanModel model = result.get();

        assertEquals("123", model.getExternalId());
        assertEquals("urn:source:aro:floorplan:123", model.getFloorplanUrn());
        assertEquals(AroCommon.SOURCE_URN, model.getSourceUrn());
        assertEquals(projectUrn, model.getProjectUrn());
        assertEquals(buildingUrn, model.getBuildingUrn());
        assertEquals(2.0, model.getBedrooms());
        assertNotNull(model.getImage());
        assertEquals("http://example.com/image.jpg", model.getImage().getUrl().toString());
    }

    @Test
    void testNoMedia_ReturnsEmpty() {
        UnitTemplate template = createBaseUnitTemplate();
        template.setMedia(null);
        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isEmpty());

        template.setMedia(java.util.List.of());
        result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isEmpty());

        template.setMedia(java.util.List.of(" "));
        result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isEmpty());
    }

    @Test
    void testInvalidMediaUrl_ReturnsEmpty() {
        UnitTemplate template = createBaseUnitTemplate();
        template.setMedia(java.util.List.of("not a valid url"));
        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isEmpty());
    }

    @Test
    void testLevelMapping_ZeroToZero() {
        UnitTemplate template = createBaseUnitTemplate();
        template.setFloor(new UnitTemplate.FloorRange());
        template.getFloor().setFrom(0);
        template.getFloor().setTo(0);

        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isPresent());
        assertEquals(1, result.get().getLevels());
        assertNull(result.get().getTitle());
    }

    @Test
    void testLevelMapping_Range() {
        UnitTemplate template = createBaseUnitTemplate();
        template.setFloor(new UnitTemplate.FloorRange());
        template.getFloor().setFrom(5);
        template.getFloor().setTo(10);

        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isPresent());
        assertEquals(6, result.get().getLevels());
        assertEquals("F5-10", result.get().getTitle());
    }

    @Test
    void testAreaMapping_TotalOnly() {
        UnitTemplate template = createBaseUnitTemplate();
        UnitTemplate.AreaRange area = new UnitTemplate.AreaRange();
        area.setFrom(new UnitTemplate.SizeInfo());
        area.getFrom().setValue(1000.0);
        template.setArea(area);

        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isPresent());
        assertNotNull(result.get().getArea());
        assertEquals(92.90, result.get().getArea().getTotalAreaSqm());
        assertNull(result.get().getArea().getMinTotalAreaSqm());
        assertNull(result.get().getArea().getMaxTotalAreaSqm());
    }

    @Test
    void testAreaMapping_MinMax() {
        UnitTemplate template = createBaseUnitTemplate();
        UnitTemplate.AreaRange area = new UnitTemplate.AreaRange();
        area.setFrom(new UnitTemplate.SizeInfo());
        area.getFrom().setValue(1000.0);
        area.setTo(new UnitTemplate.SizeInfo());
        area.getTo().setValue(1200.0);
        template.setArea(area);

        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isPresent());
        assertNotNull(result.get().getArea());
        assertEquals(92.90, result.get().getArea().getMinTotalAreaSqm());
        assertEquals(111.48, result.get().getArea().getMaxTotalAreaSqm());
        assertNotNull(result.get().getArea().getTotalAreaSqm());
    }

    @Test
    void testAdditionalDataMapping() {
        UnitTemplate template = createBaseUnitTemplate();
        template.setAvailabilities(5);
        template.setBathroom(3);
        
        Price price = new Price();
        price.setAmount(500000L);
        template.setPriceFrom(price);

        UnitTemplate.FloorRange floor = new UnitTemplate.FloorRange();
        floor.setFrom(1);
        floor.setTo(1);
        template.setFloor(floor);

        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        assertTrue(result.isPresent());
        FloorPlanModel model = result.get();

        assertEquals(5, model.getAdditionalProperties().get("availability_count"));
        assertEquals(3, model.getAdditionalProperties().get("bathrooms"));
        assertEquals(500000.0, model.getAdditionalProperties().get("price_from_aed"));
        assertEquals(1, model.getAdditionalProperties().get("floor_from"));
        assertEquals(1, model.getAdditionalProperties().get("floor_to"));
    }
    
    @Test
    void testMultipleMedia_MapsToLevelImages() {
        UnitTemplate template = createBaseUnitTemplate();
        template.setMedia(java.util.List.of(
            "http://example.com/main.jpg",
            "http://example.com/level1.jpg",
            "http://example.com/level2.jpg"
        ));
        
        Optional<FloorPlanModel> result = converter.convert(template, "p-urn", "b-urn");
        
        assertTrue(result.isPresent());
        FloorPlanModel model = result.get();
        
        assertEquals("http://example.com/main.jpg", model.getImage().getUrl().toString());
        assertNotNull(model.getLevelImages());
        assertEquals(2, model.getLevelImages().size());
        assertEquals("http://example.com/level1.jpg", model.getLevelImages().get(0).getImage().getUrl().toString());
        assertEquals("http://example.com/level2.jpg", model.getLevelImages().get(1).getImage().getUrl().toString());
    }
} 