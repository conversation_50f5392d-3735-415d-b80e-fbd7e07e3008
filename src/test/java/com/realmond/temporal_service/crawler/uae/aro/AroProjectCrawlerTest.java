package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

/**
 * Unit tests for {@link AroProjectCrawler} using mocked dependencies.
 * 
 * This test suite covers all public methods of AroProjectCrawler and uses
 * realistic data patterns from the ARO.ae API responses found in the Postman collection.
 */
@ExtendWith(MockitoExtension.class)
class AroProjectCrawlerTest {

    @Mock
    private AroApiService aroApiService;

    @InjectMocks
    private AroProjectCrawler aroProjectCrawler;

    private CrawlRecord<ProjectModel> sampleCrawlRecord;
    private ProjectModel sampleProject;

    @BeforeEach
    void setUp() {
        // Create a sample ProjectModel based on the Postman collection data structure
        sampleProject = createSampleProjectModel();
        
        // Create a sample CrawlRecord with metadata
        Map<String, Object> metadata = Map.of(
            "conversion_timestamp", "2025-01-14T10:00:00Z",
            "source_api", "aro.ae",
            "api_version", "v1",
            "raw_data", Map.of(
                "project_summary", Map.of(
                    "id", 3088,
                    "title", "Alton by Nshama",
                    "slug", "alton-by-nshama"
                ),
                "project_detail", Map.of(
                    "id", 3088,
                    "slug", "alton-by-nshama",
                    "title", "Alton by Nshama",
                    "description", "Welcome to Alton by Nshama...",
                    "handover_date", "2028-05-31T00:00:00.000Z"
                )
            )
        );
        
        sampleCrawlRecord = new CrawlRecord<>(
            sampleProject,
            metadata,
            "aro.ae",
            "aro.ae:developer:nshama:project:alton-by-nshama"
        );
    }

    @Test
    void getSourceUrn_ShouldReturnCorrectUrn() {
        String sourceUrn = aroProjectCrawler.getSourceUrn();
        
        assertThat(sourceUrn).isEqualTo(AroCommon.SOURCE_URN);
    }

    @Test
    void supportsPagination_ShouldReturnTrue() {
        Boolean supportsPagination = aroProjectCrawler.supportsPagination();
        
        assertThat(supportsPagination).isTrue();
    }

    @Test
    void fetchAllPageIds_WithValidPagesCount_ShouldReturnCorrectPageIds() {
        // Given: Mock API service returns 18 pages (from Postman collection)
        when(aroApiService.fetchProjectPagesCount()).thenReturn(18);

        // When: Fetch all page IDs
        List<String> pageIds = aroProjectCrawler.fetchAllPageIds();

        // Then: Should return page numbers 1 through 18 as strings
        assertThat(pageIds).hasSize(18);
        assertThat(pageIds).containsExactly(
            "1", "2", "3", "4", "5", "6", "7", "8", "9", "10",
            "11", "12", "13", "14", "15", "16", "17", "18"
        );

        verify(aroApiService).fetchProjectPagesCount();
    }

    @Test
    void fetchAllPageIds_WithSinglePage_ShouldReturnSinglePageId() {
        // Given: Mock API service returns 1 page
        when(aroApiService.fetchProjectPagesCount()).thenReturn(1);

        // When: Fetch all page IDs
        List<String> pageIds = aroProjectCrawler.fetchAllPageIds();

        // Then: Should return only page "1"
        assertThat(pageIds).hasSize(1);
        assertThat(pageIds).containsExactly("1");

        verify(aroApiService).fetchProjectPagesCount();
    }

    @Test
    void fetchAllPageIds_WithZeroPages_ShouldReturnEmptyList() {
        // Given: Mock API service returns 0 pages
        when(aroApiService.fetchProjectPagesCount()).thenReturn(0);

        // When: Fetch all page IDs
        List<String> pageIds = aroProjectCrawler.fetchAllPageIds();

        // Then: Should return empty list
        assertThat(pageIds).isEmpty();

        verify(aroApiService).fetchProjectPagesCount();
    }

    @Test
    void fetchAllPageIds_WhenServiceThrowsRetryableException_ShouldPropagateException() {
        // Given: Mock API service throws RetryableCrawlerException
        when(aroApiService.fetchProjectPagesCount())
            .thenThrow(new RetryableCrawlerException("API temporarily unavailable"));

        // When & Then: Should propagate the exception
        assertThatThrownBy(() -> aroProjectCrawler.fetchAllPageIds())
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("API temporarily unavailable");

        verify(aroApiService).fetchProjectPagesCount();
    }

    @Test
    void parsePage_WithValidPageNumber_ShouldReturnProjectsFromPage() {
        // Given: Mock API service returns projects for page 1 (based on Postman data)
        List<CrawlRecord<ProjectModel>> expectedProjects = List.of(sampleCrawlRecord);
        when(aroApiService.fetchProjectsPage(1)).thenReturn(expectedProjects);

        // When: Parse page 1
        List<CrawlRecord<ProjectModel>> result = aroProjectCrawler.parsePage("1");

        // Then: Should return the projects from the service
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(sampleCrawlRecord);
        assertThat(result.get(0).data().getTitle()).isEqualTo("Alton by Nshama");
        assertThat(result.get(0).source()).isEqualTo("aro.ae");

        verify(aroApiService).fetchProjectsPage(1);
    }

    @Test
    void parsePage_WithEmptyResults_ShouldReturnEmptyList() {
        // Given: Mock API service returns empty list for page 99
        when(aroApiService.fetchProjectsPage(99)).thenReturn(Collections.emptyList());

        // When: Parse page 99
        List<CrawlRecord<ProjectModel>> result = aroProjectCrawler.parsePage("99");

        // Then: Should return empty list
        assertThat(result).isEmpty();

        verify(aroApiService).fetchProjectsPage(99);
    }

    @Test
    void parsePage_WithMultipleProjects_ShouldReturnAllProjects() {
        // Given: Mock API service returns multiple projects (simulating page with 30 projects from Postman)
        List<CrawlRecord<ProjectModel>> multipleProjects = createMultipleCrawlRecords();
        when(aroApiService.fetchProjectsPage(1)).thenReturn(multipleProjects);

        // When: Parse page 1
        List<CrawlRecord<ProjectModel>> result = aroProjectCrawler.parsePage("1");

        // Then: Should return all projects
        assertThat(result).hasSize(3);
        assertThat(result.get(0).data().getTitle()).isEqualTo("Alton by Nshama");
        assertThat(result.get(1).data().getTitle()).isEqualTo("Skyhills Residences 3");
        assertThat(result.get(2).data().getTitle()).isEqualTo("Pier Point 2");

        verify(aroApiService).fetchProjectsPage(1);
    }

    @Test
    void parsePage_WithInvalidPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for invalid page number
        assertThatThrownBy(() -> aroProjectCrawler.parsePage("invalid"))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: invalid")
            .hasCauseInstanceOf(NumberFormatException.class);

        // Should not call the service for invalid input
        verifyNoMoreInteractions(aroApiService);
    }

    @Test
    void parsePage_WithNullPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for null page number
        assertThatThrownBy(() -> aroProjectCrawler.parsePage(null))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: null")
            .hasCauseInstanceOf(NumberFormatException.class);

        verifyNoMoreInteractions(aroApiService);
    }

    @Test
    void parsePage_WithEmptyPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for empty page number
        assertThatThrownBy(() -> aroProjectCrawler.parsePage(""))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: ")
            .hasCauseInstanceOf(NumberFormatException.class);

        verifyNoMoreInteractions(aroApiService);
    }

    @Test
    void parsePage_WithNegativePageNumber_ShouldCallServiceWithNegativeNumber() {
        // Given: Mock API service can handle negative page numbers (may return empty or throw)
        when(aroApiService.fetchProjectsPage(-1)).thenReturn(Collections.emptyList());

        // When: Parse negative page number
        List<CrawlRecord<ProjectModel>> result = aroProjectCrawler.parsePage("-1");

        // Then: Should pass negative number to service
        assertThat(result).isEmpty();
        verify(aroApiService).fetchProjectsPage(-1);
    }

    @Test
    void parsePage_WhenServiceThrowsRetryableException_ShouldPropagateException() {
        // Given: Mock API service throws RetryableCrawlerException
        when(aroApiService.fetchProjectsPage(anyInt()))
            .thenThrow(new RetryableCrawlerException("Network timeout"));

        // When & Then: Should propagate the exception
        assertThatThrownBy(() -> aroProjectCrawler.parsePage("1"))
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Network timeout");

        verify(aroApiService).fetchProjectsPage(1);
    }

    @Test
    void fetchAll_ShouldThrowNonRetryableException() {
        // When & Then: Should always throw NonRetryableCrawlerException
        assertThatThrownBy(() -> aroProjectCrawler.fetchAll())
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("fetching all entries is not supported");

        // Should not interact with the service
        verifyNoMoreInteractions(aroApiService);
    }

    @Test
    void parsePage_WithLargePageNumber_ShouldHandleCorrectly() {
        // Given: Test with large page number that's still valid
        String largePageNumber = "999999";
        when(aroApiService.fetchProjectsPage(999999)).thenReturn(Collections.emptyList());

        // When: Parse large page number
        List<CrawlRecord<ProjectModel>> result = aroProjectCrawler.parsePage(largePageNumber);

        // Then: Should handle correctly
        assertThat(result).isEmpty();
        verify(aroApiService).fetchProjectsPage(999999);
    }

    @Test
    void parsePage_WithPageNumberZero_ShouldCallServiceWithZero() {
        // Given: Mock API service can handle page 0
        when(aroApiService.fetchProjectsPage(0)).thenReturn(Collections.emptyList());

        // When: Parse page 0
        List<CrawlRecord<ProjectModel>> result = aroProjectCrawler.parsePage("0");

        // Then: Should pass 0 to service
        assertThat(result).isEmpty();
        verify(aroApiService).fetchProjectsPage(0);
    }

    // Helper methods to create test data based on Postman collection structure

    private ProjectModel createSampleProjectModel() {
        return ProjectModel.builder()
            .withTitle("Alton by Nshama")
            .withProjectSlug("alton-by-nshama")
            .withExternalId("3088")
            .withProjectUrn("aro.ae:developer:nshama:project:alton-by-nshama")
            .withDeveloperUrn("aro.ae:developer:nshama")
            .withSourceUrn("aro.ae")
            .withCurrency("AED")
            .withDescription("Welcome to Alton by Nshama, nestled in the vibrant community of Town Square...")
            .build();
    }

    private List<CrawlRecord<ProjectModel>> createMultipleCrawlRecords() {
        // Create multiple projects based on Postman collection sample data
        ProjectModel project1 = ProjectModel.builder()
            .withTitle("Alton by Nshama")
            .withProjectSlug("alton-by-nshama")
            .withExternalId("3088")
            .withProjectUrn("aro.ae:developer:nshama:project:alton-by-nshama")
            .withDeveloperUrn("aro.ae:developer:nshama")
            .withSourceUrn("aro.ae")
            .withCurrency("AED")
            .build();

        ProjectModel project2 = ProjectModel.builder()
            .withTitle("Skyhills Residences 3")
            .withProjectSlug("skyhills-residences-3")
            .withExternalId("3085")
            .withProjectUrn("aro.ae:developer:hre-development:project:skyhills-residences-3")
            .withDeveloperUrn("aro.ae:developer:hre-development")
            .withSourceUrn("aro.ae")
            .withCurrency("AED")
            .build();

        ProjectModel project3 = ProjectModel.builder()
            .withTitle("Pier Point 2")
            .withProjectSlug("pier-point-2")
            .withExternalId("3081")
            .withProjectUrn("aro.ae:developer:emaar-properties:project:pier-point-2")
            .withDeveloperUrn("aro.ae:developer:emaar-properties")
            .withSourceUrn("aro.ae")
            .withCurrency("AED")
            .build();

        Map<String, Object> metadata = Map.of(
            "conversion_timestamp", "2025-01-14T10:00:00Z",
            "source_api", "aro.ae",
            "api_version", "v1"
        );

        return List.of(
            new CrawlRecord<>(project1, metadata, "aro.ae", project1.getProjectUrn()),
            new CrawlRecord<>(project2, metadata, "aro.ae", project2.getProjectUrn()),
            new CrawlRecord<>(project3, metadata, "aro.ae", project3.getProjectUrn())
        );
    }
} 