package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.property_finder.model.NextJsWrapper;
import com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectDetails;
import com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectDetailsPageProps;
import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.uae.property_finder.PropertyFinderCommon;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.jsoup.Jsoup;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Basic unit tests for {@link PropertyFinderApiService} focusing on the
 * <code>convertToProjectModel</code> method stub introduced in backlog
 * <strong>Task&nbsp;8</strong>.
 */
@ExtendWith(MockitoExtension.class)
class PropertyFinderApiServiceTest {

    private static final String PROJECT_DETAILS_JSON_PATH_1 = "/crawler/uae/propertyfinder/project-details.json";
    private static final String PROJECT_DETAILS_JSON_PATH_2 = "/crawler/uae/propertyfinder/project-details-2.json";
    private static final String NEW_PROJECTS_JSON_PATH = "/crawler/uae/propertyfinder/new-projects.json";

    // --- Mocks -----------------------------------------------------------------
    @Mock
    private PropertyFinderFeignRestClient feignClient;

    // --- System under test -----------------------------------------------------
    private PropertyFinderApiService apiService;

    private ObjectMapper mapper;
    private List<ProjectDetails> sampleDetailsList;

    @BeforeEach
    void setUp() throws IOException {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();

        sampleDetailsList = new java.util.ArrayList<>();

        // Load both JSON fixtures and unwrap to ProjectDetails ---------------
        loadFixture(PROJECT_DETAILS_JSON_PATH_1);
        loadFixture(PROJECT_DETAILS_JSON_PATH_2);

        apiService = new PropertyFinderApiService(feignClient);
    }

    @Test
    void convertToProjectModel_WithValidDetails_ShouldReturnOptionalPresent() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> result = invokeConvertToProjectModel(details);
            assertThat(result).as("convertToProjectModel result for fixture").isPresent();
            assertThat(result.get()).isNotNull();
        }
    }

    @Test
    void convertToProjectModel_WithNull_ShouldReturnEmptyOptional() throws Exception {
        Optional<ProjectModel> result = invokeConvertToProjectModel(null);
        assertThat(result).isEmpty();
    }

    @Test
    void convertToProjectModel_ShouldMapIdentifiersAndUrnsCorrectly() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            ProjectDetails.Developer dev = details.getDeveloper();
            String developerSlug = dev != null && dev.getSlug() != null ? dev.getSlug().toLowerCase() : null;

            String fullSlug = details.getSlug();
            String projectSlug = null;
            if (fullSlug != null) {
                int idx = fullSlug.indexOf('/');
                projectSlug = (idx >= 0 && idx < fullSlug.length() - 1) ? fullSlug.substring(idx + 1) : fullSlug;
            }

            String sourceUrn = PropertyFinderCommon.SOURCE_URN;
            String developerUrn = developerSlug != null ? sourceUrn + ":developer:" + developerSlug : null;
            String projectUrn = (developerUrn != null && projectSlug != null) ? developerUrn + ":project:" + projectSlug : null;

            Optional<ProjectModel> resultOpt = invokeConvertToProjectModel(details);
            assertThat(resultOpt).isPresent();
            ProjectModel model = resultOpt.get();

            assertThat(model.getSourceUrn()).isEqualTo(sourceUrn);
            assertThat(model.getDeveloperUrn()).isEqualTo(developerUrn);
            assertThat(model.getProjectUrn()).isEqualTo(projectUrn);
            assertThat(model.getExternalId()).isEqualTo(details.getId());
        }
    }

    @Test
    void convertToProjectModel_ShouldMapBasicAttributes() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> resultOpt = invokeConvertToProjectModel(details);
            assertThat(resultOpt).isPresent();
            ProjectModel model = resultOpt.get();

            // Title
            assertThat(model.getTitle()).isEqualTo(details.getTitle());

            // Description (HTML stripped)
            String expectedDesc = details.getDescription() == null ? null : Jsoup.parse(details.getDescription()).text();
            assertThat(model.getDescription()).isEqualTo(expectedDesc);

            // Currency constant
            assertThat(model.getCurrency()).isEqualTo("AED");

            // Project slug non-null
            assertThat(model.getProjectSlug()).isNotNull();
        }
    }

    @Test
    void convertToProjectModel_ShouldMapLocationAndCoordinates() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> resultOpt = invokeConvertToProjectModel(details);
            assertThat(resultOpt).isPresent();
            ProjectModel model = resultOpt.get();

            ProjectDetails.Location loc = details.getLocation();
            List<ProjectDetails.LocationNode> locationTree = details.getLocationTree();

            String expectedAddress = loc != null ? (loc.getFullName() != null ? loc.getFullName() : loc.getName()) : null;
            String expectedCity = null;
            String expectedCityDistrict = null;
            if (locationTree != null && !locationTree.isEmpty()) {
                expectedCity = locationTree.get(0) != null ? locationTree.get(0).getName() : null;
                if (locationTree.size() > 1) {
                    expectedCityDistrict = locationTree.get(1) != null ? locationTree.get(1).getName() : null;
                }
            }
            String expectedState = expectedCity; // mirrors city per spec
            String expectedCountry = "AE";

            if (expectedAddress != null || expectedCity != null) {
                assertThat(model.getLocation()).as("LocationModel should be present when address or city exists").isNotNull();
                if (expectedAddress != null) {
                    assertThat(model.getLocation().getAddress()).isEqualTo(expectedAddress);
                }
                if (expectedCity != null) {
                    assertThat(model.getLocation().getCity()).isEqualTo(expectedCity);
                    assertThat(model.getLocation().getState()).isEqualTo(expectedState);
                }
                if (expectedCityDistrict != null) {
                    assertThat(model.getLocation().getCityDistrict()).isEqualTo(expectedCityDistrict);
                }
                assertThat(model.getLocation().getCountry()).isEqualTo(expectedCountry);
            }

            Double expectedLat = null;
            Double expectedLng = null;
            if (loc != null && loc.getCoordinates() != null) {
                expectedLat = loc.getCoordinates().getLat();
                if (loc.getCoordinates().getLon() != null) {
                    expectedLng = loc.getCoordinates().getLon();
                } else {
                    expectedLng = loc.getCoordinates().getLng();
                }
            }

            if (expectedLat != null && expectedLng != null) {
                assertThat(model.getCoordinates()).isNotNull();
                assertThat(model.getCoordinates().getLat()).isEqualTo(expectedLat);
                assertThat(model.getCoordinates().getLng()).isEqualTo(expectedLng);
            } else {
                assertThat(model.getCoordinates()).isNull();
            }

            // Polygon should always be null for task 11 mapping
            assertThat(model.getPolygon()).isNull();
        }
    }

    @Test
    void convertToProjectModel_ShouldMapMedia() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> resultOpt = invokeConvertToProjectModel(details);
            assertThat(resultOpt).isPresent();
            ProjectModel model = resultOpt.get();

            // Count expected images/videos/master-plan
            int expectedImages = 0;
            int expectedVideos = 0;
            String expectedMasterPlanImage = null;
            if (details.getImages() != null) {
                for (ProjectDetails.GalleryItem gi : details.getImages()) {
                    if (gi == null || gi.getType() == null) continue;
                    String t = gi.getType().toLowerCase();
                    switch (t) {
                        case "image" -> expectedImages++;
                        case "video" -> expectedVideos++;
                        case "master-plan" -> {
                            if (expectedMasterPlanImage == null) expectedMasterPlanImage = gi.getSource();
                        }
                    }
                }
            }

            if (expectedImages > 0) {
                assertThat(model.getImages()).hasSize(expectedImages);
                assertThat(model.getCoverImage()).isNotNull();
                assertThat(model.getCoverImage().getUrl()).isEqualTo(model.getImages().get(0).getUrl());
            } else {
                assertThat(model.getImages()).isNullOrEmpty();
                assertThat(model.getCoverImage()).isNull();
            }

            if (expectedVideos > 0) {
                assertThat(model.getVideos()).hasSize(expectedVideos);
            } else {
                assertThat(model.getVideos()).isNullOrEmpty();
            }

            if (expectedMasterPlanImage != null) {
                assertThat(model.getAdditionalData()).isInstanceOf(java.util.Map.class);
                java.util.Map<?,?> add = (java.util.Map<?,?>) model.getAdditionalData();
                assertThat(add.get("masterPlanImage")).isEqualTo(expectedMasterPlanImage);
            }

            if (details.getMasterPlan() != null && details.getMasterPlan().getDescription() != null) {
                String stripped = Jsoup.parse(details.getMasterPlan().getDescription()).text();
                if (stripped != null && !stripped.isBlank()) {
                    java.util.Map<?,?> add = (java.util.Map<?,?>) model.getAdditionalData();
                    assertThat(add.get("masterPlanDescription")).isEqualTo(stripped);
                }
            }
        }
    }

    @Test
    void convertToProjectModel_ShouldMapAmenities() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> resultOpt = invokeConvertToProjectModel(details);
            assertThat(resultOpt).isPresent();
            ProjectModel model = resultOpt.get();

            int expected = details.getAmenities() == null ? 0 : details.getAmenities().size();

            if (expected > 0) {
                assertThat(model.getAmenities()).hasSize(expected);
                java.util.Set<String> expectedLabels = new java.util.HashSet<>();
                for (ProjectDetails.Amenity a : details.getAmenities()) {
                    expectedLabels.add(a.getName());
                }
                java.util.Set<String> actualLabels = new java.util.HashSet<>();
                for (com.realmond.etl.model.AmenityModel am : model.getAmenities()) {
                    actualLabels.add(am.getLabel());
                }
                assertThat(actualLabels).isEqualTo(expectedLabels);
            } else {
                assertThat(model.getAmenities()).isNullOrEmpty();
            }
        }
    }

    @Test
    void convertToProjectModel_ShouldMapConstructionStatus() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            String phase = details.getConstructionPhase();
            com.realmond.etl.model.AdModel.ConstructionStatusModel expected = null;
            if (phase != null) {
                switch (phase.toLowerCase()) {
                    case "not_started", "off_plan" -> expected = com.realmond.etl.model.AdModel.ConstructionStatusModel.NOT_STARTED;
                    case "under_construction" -> expected = com.realmond.etl.model.AdModel.ConstructionStatusModel.ACTIVE;
                    case "completed", "ready" -> expected = com.realmond.etl.model.AdModel.ConstructionStatusModel.FINISHED;
                }
            }

            assertThat(model.getProjectStatus()).isEqualTo(expected);
        }
    }

    @Test
    void convertToProjectModel_ShouldMapProjectStatsScalarFields() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            Integer startingPrice = details.getStartingPrice();
            if (startingPrice != null) {
                assertThat(model.getProjectStats()).isNotNull();
                assertThat(model.getProjectStats().getPriceMin()).isNotNull();
                assertThat(model.getProjectStats().getPriceMin().getValue()).isEqualTo(startingPrice.doubleValue());
                assertThat(model.getProjectStats().getPriceMin().getCurrency()).isEqualTo("AED");
            }

            String expectedLaunchDate = details.getSalesStartDate() == null ? null : details.getSalesStartDate().toLocalDate().toString();
            String expectedCompletionDate = details.getDeliveryDate() == null ? null : details.getDeliveryDate().toLocalDate().toString();

            if (expectedLaunchDate != null || expectedCompletionDate != null) {
                assertThat(model.getProjectStats()).isNotNull();
                if (expectedLaunchDate != null) {
                    assertThat(model.getProjectStats().getLaunchDate()).isEqualTo(expectedLaunchDate);
                }
                if (expectedCompletionDate != null) {
                    assertThat(model.getProjectStats().getCompletionDate()).isEqualTo(expectedCompletionDate);
                }
            } else {
                // When both dates and price are null, projectStats may be null
                if (startingPrice == null) {
                    assertThat(model.getProjectStats()).isNull();
                }
            }
        }
    }

    @Test
    void parseIsoDate_ShouldHandleValidAndInvalidInputs() throws Exception {
        Method m = PropertyFinderApiService.class.getDeclaredMethod("parseIsoDate", String.class);
        m.setAccessible(true);

        String iso = "2027-12-01T12:07:29.564Z";
        Object result = m.invoke(null, iso);
        assertThat(result).isEqualTo("2027-12-01");

        // Plain date without time
        String plain = "2024-05-10";
        assertThat(m.invoke(null, plain)).isEqualTo("2024-05-10");

        // Invalid date
        String invalid = "not-a-date";
        assertThat(m.invoke(null, invalid)).isNull();

        // Null input
        assertThat(m.invoke(null, new Object[]{null})).isNull();
    }

    @Test
    void convertToProjectModel_ShouldMapPropertyTypes() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            List<String> types = details.getPropertyTypes();
            if (types == null || types.isEmpty()) {
                // stats may still exist for other reasons but property types list should be empty
                if (model.getProjectStats() != null) {
                    assertThat(model.getProjectStats().getPropertyTypes()).isNullOrEmpty();
                }
                continue;
            }

            assertThat(model.getProjectStats()).as("ProjectStats should be present when propertyTypes exist").isNotNull();

            java.util.List<com.realmond.etl.model.AdModel.PropertyTypeModel> expectedEnums = new java.util.ArrayList<>();
            for (String raw : types) {
                if (raw == null || raw.isBlank()) continue;
                String enumName = raw.trim().toUpperCase().replace('-', '_');
                com.realmond.etl.model.AdModel.PropertyTypeModel mapped;
                try {
                    mapped = com.realmond.etl.model.AdModel.PropertyTypeModel.valueOf(enumName);
                } catch (IllegalArgumentException ex) {
                    mapped = com.realmond.etl.model.AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
                }
                if (!expectedEnums.contains(mapped)) {
                    expectedEnums.add(mapped);
                }
            }

            assertThat(model.getProjectStats().getPropertyTypes()).containsExactlyElementsOf(expectedEnums);
        }
    }

    @Test
    void convertToProjectModel_ShouldMapUnitStats() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            int expectedUnitStats = 0;
            if (details.getUnits() != null) {
                for (ProjectDetails.BuildingGroup bg : details.getUnits()) {
                    if (bg == null || bg.getUnits() == null) continue;
                    for (ProjectDetails.UnitSet us : bg.getUnits()) {
                        if (us == null || us.getList() == null) continue;
                        expectedUnitStats += us.getList().size();
                    }
                }
            }

            if (expectedUnitStats > 0) {
                assertThat(model.getProjectStats()).isNotNull();
                assertThat(model.getProjectStats().getUnits()).hasSize(expectedUnitStats);

                // Spot-check first UnitStats entry fields
                com.realmond.etl.model.UnitStats first = model.getProjectStats().getUnits().get(0);
                assertThat(first.getPropertyType()).isNotNull();
                // bedrooms non-null matches source first unit bedrooms
                if (details.getUnits() != null) {
                    ProjectDetails.Unit sampleUnit = null;
                    outer: for (ProjectDetails.BuildingGroup bg : details.getUnits()) {
                        if (bg == null || bg.getUnits() == null) continue;
                        for (ProjectDetails.UnitSet us : bg.getUnits()) {
                            if (us == null || us.getList() == null) continue;
                            if (!us.getList().isEmpty()) { sampleUnit = us.getList().get(0); break outer; }
                        }
                    }
                    if (sampleUnit != null && sampleUnit.getBedrooms() != null) {
                        assertThat(first.getBedrooms()).isEqualTo(sampleUnit.getBedrooms().doubleValue());
                    }
                }
            } else {
                if (model.getProjectStats() != null) {
                    assertThat(model.getProjectStats().getUnits()).isNullOrEmpty();
                }
            }
        }
    }

    @Test
    void generateSha1Hash_ShouldReturnExpectedHex() throws Exception {
        Method m = PropertyFinderApiService.class.getDeclaredMethod("generateSha1Hash", String.class);
        m.setAccessible(true);

        String input = "https://example.com/image.jpg";
        String expected = org.apache.commons.codec.digest.DigestUtils.sha1Hex(input);
        assertThat(m.invoke(null, input)).isEqualTo(expected);
    }

    @Test
    void convertToProjectModel_ShouldMapFloorPlans() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            // calculate expected unique URLs
            java.util.Set<String> expectedUrls = new java.util.HashSet<>();
            if (details.getUnits() != null) {
                for (ProjectDetails.BuildingGroup bg : details.getUnits()) {
                    if (bg == null || bg.getUnits() == null) continue;
                    for (ProjectDetails.UnitSet us : bg.getUnits()) {
                        if (us == null || us.getList() == null) continue;
                        for (ProjectDetails.Unit unit : us.getList()) {
                            if (unit == null || unit.getLayouts() == null) continue;
                            for (ProjectDetails.Layout layout : unit.getLayouts()) {
                                if (layout == null || layout.getFloorPlans() == null) continue;
                                for (String url : layout.getFloorPlans()) {
                                    if (url != null && !url.isBlank()) {
                                        expectedUrls.add(url);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            if (expectedUrls.isEmpty()) {
                assertThat(model.getFloorPlans()).isNullOrEmpty();
            } else {
                assertThat(model.getFloorPlans()).hasSize(expectedUrls.size());
                // verify SHA ids
                Method sha = PropertyFinderApiService.class.getDeclaredMethod("generateSha1Hash", String.class);
                sha.setAccessible(true);
                java.util.Set<String> expectedIds = new java.util.HashSet<>();
                for (String u : expectedUrls) {
                    expectedIds.add((String) sha.invoke(null, u));
                }
                java.util.Set<String> actualIds = new java.util.HashSet<>();
                for (Object obj : model.getFloorPlans()) {
                    assertThat(obj).isInstanceOf(com.realmond.etl.model.common.FloorPlanModel.class);
                    com.realmond.etl.model.common.FloorPlanModel fp = (com.realmond.etl.model.common.FloorPlanModel) obj;
                    actualIds.add(fp.getExternalId());
                }
                assertThat(actualIds).isEqualTo(expectedIds);
            }
        }
    }

    @Test
    void convertToProjectModel_ShouldMapBrochuresAndPaymentPlans() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            // --- Brochures --------------------------------------------------
            if (details.getBrochureUrl() != null && !details.getBrochureUrl().isBlank()) {
                assertThat(model.getBrochures()).hasSize(1);
                assertThat(model.getBrochures().get(0).getUrl().toString()).isEqualTo(details.getBrochureUrl());
            } else {
                assertThat(model.getBrochures()).isNullOrEmpty();
            }

            // --- Payment plans ---------------------------------------------
            List<ProjectDetails.PaymentPlan> srcPlans = details.getPaymentPlans();
            if (srcPlans != null && !srcPlans.isEmpty()) {
                assertThat(model.getPaymentPlans()).as("paymentPlans should match source size").hasSize(srcPlans.size());

                // Validate first payment plan hierarchy for sanity
                Object obj = model.getPaymentPlans().get(0);
                assertThat(obj).isInstanceOf(com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel.class);
                com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel mapped = (com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel) obj;
                com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel expectedSrc = null;
                if (!srcPlans.isEmpty()) expectedSrc = convertToLegacy(srcPlans.get(0));
                if (expectedSrc != null) {
                    assertThat(mapped.getTitle()).isEqualTo(expectedSrc.getTitle());
                    if (expectedSrc.getPhases() != null) {
                        assertThat(mapped.getPhases()).hasSize(expectedSrc.getPhases().size());
                    }
                }
            } else {
                assertThat(model.getPaymentPlans()).isNullOrEmpty();
            }
        }
    }

    @Test
    void convertToProjectModel_ShouldMapAdditionalDataFields() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            assertThat(model.getAdditionalData()).isInstanceOf(java.util.Map.class);
            @SuppressWarnings("unchecked") java.util.Map<String,Object> add = (java.util.Map<String,Object>) model.getAdditionalData();

            // developerId
            if (details.getDeveloper() != null && details.getDeveloper().getId() != null) {
                assertThat(add.get("developerId")).isEqualTo(details.getDeveloper().getId());
            }

            // FAQs size check
            if (details.getFaqs() != null && !details.getFaqs().isEmpty()) {
                assertThat(add.get("faqs")).isInstanceOf(java.util.List.class);
                java.util.List<?> faqList = (java.util.List<?>) add.get("faqs");
                assertThat(faqList).hasSize(details.getFaqs().size());
            }

            // ownershipType enum mapping
            String rawOwner = details.getOwnershipType();
            String expectedOwnership;
            if (rawOwner == null || rawOwner.isBlank()) expectedOwnership = "UNKNOWN";
            else if (rawOwner.trim().equalsIgnoreCase("freehold")) expectedOwnership = "FREEHOLD";
            else if (rawOwner.trim().equalsIgnoreCase("leasehold")) expectedOwnership = "LEASEHOLD";
            else expectedOwnership = "UNKNOWN";
            assertThat(add.get("ownershipType")).isEqualTo(expectedOwnership);

            // salesPhase and derived enum
            if (details.getSalesPhase() != null && !details.getSalesPhase().isBlank()) {
                assertThat(add.get("salesPhase")).isEqualTo(details.getSalesPhase());
                assertThat(add.get("ADDITIONAL_SALES_PHASE")).isEqualTo(details.getSalesPhase().trim().toUpperCase());
            } else {
                assertThat(add.containsKey("salesPhase")).isFalse();
            }

            // lastInspectionDate
            if (details.getLastInspectionDate() != null) {
                String expectedDate = details.getLastInspectionDate().toLocalDate().toString();
                assertThat(add.get("lastInspectionDate")).isEqualTo(expectedDate);
            }

            // timelinePhases
            if (details.getTimelinePhases() != null && !details.getTimelinePhases().isEmpty()) {
                assertThat(add.get("timelinePhases")).isEqualTo(details.getTimelinePhases());
            }
        }
    }

    @Test
    void convertToProjectModel_ShouldMapProjectUrls() throws Exception {
        for (ProjectDetails details : sampleDetailsList) {
            Optional<ProjectModel> opt = invokeConvertToProjectModel(details);
            assertThat(opt).isPresent();
            ProjectModel model = opt.get();

            ProjectDetails.Developer dev = details.getDeveloper();
            String developerSlug = dev != null && dev.getSlug() != null ? dev.getSlug().toLowerCase() : null;
            String fullSlug = details.getSlug();
            String projectSlug = null;
            if (fullSlug != null) {
                int idx = fullSlug.indexOf('/');
                projectSlug = (idx >= 0 && idx < fullSlug.length() - 1) ? fullSlug.substring(idx + 1) : fullSlug;
            }

            if (developerSlug != null && projectSlug != null) {
                String expected = "https://www.propertyfinder.ae/en/new-projects/" + developerSlug + "/" + projectSlug;
                assertThat(model.getUrls()).isNotNull();
                assertThat(model.getUrls().get(0).toString()).isEqualTo(expected);
            } else {
                if (model.getUrls() != null) {
                    assertThat(model.getUrls()).isEmpty();
                }
            }
        }
    }

    @Test
    void convertSummaryToProjectModel_ShouldMapCoreFields() throws Exception {
        // Load sample new-projects JSON and unwrap the second summary which has rich data
        InputStream is = PropertyFinderApiServiceTest.class.getResourceAsStream(NEW_PROJECTS_JSON_PATH);
        assertThat(is).as("Fixture stream should not be null").isNotNull();
        byte[] bytes = is.readAllBytes();
        String json = new String(bytes, StandardCharsets.UTF_8);

        NextJsWrapper<com.realmond.temporal_service.crawler.uae.property_finder.model.SearchResultPageProps> wrapper = mapper.readValue(
                json,
                mapper.getTypeFactory().constructParametricType(
                        NextJsWrapper.class,
                        com.realmond.temporal_service.crawler.uae.property_finder.model.SearchResultPageProps.class)
        );

        List<com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectSummary> list = wrapper.getProps()
                .getPageProps()
                .getSearchResult()
                .getData()
                .getProjects()
                .getDeveloper();

        // Use the second entry (index 1) which contains bedrooms, deliveryDate, images etc.
        com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectSummary summary = list.get(1);

        // Reflectively invoke convertSummaryToProjectModel
        Method m = PropertyFinderApiService.class.getDeclaredMethod("convertSummaryToProjectModel", com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectSummary.class);
        m.setAccessible(true);
        Optional<ProjectModel> opt = (Optional<ProjectModel>) m.invoke(apiService, summary);

        assertThat(opt).isPresent();
        ProjectModel model = opt.get();

        // Title
        assertThat(model.getTitle()).isEqualTo(summary.getTitle());

        // Completion date mapped from deliveryDate
        if (summary.getDeliveryDate() != null) {
            String expectedCompletion = summary.getDeliveryDate().toLocalDate().toString();
            assertThat(model.getProjectStats()).isNotNull();
            assertThat(model.getProjectStats().getCompletionDate()).isEqualTo(expectedCompletion);
        }

        // Bedrooms -> units size
        if (summary.getBedrooms() != null && !summary.getBedrooms().isEmpty()) {
            assertThat(model.getProjectStats()).isNotNull();
            assertThat(model.getProjectStats().getUnits()).hasSize(summary.getBedrooms().size());
        }

        // Images
        if (summary.getImages() != null && !summary.getImages().isEmpty()) {
            assertThat(model.getImages()).hasSize(summary.getImages().size());
            assertThat(model.getCoverImage()).isNotNull();
        }

        // Amenities
        if (summary.getAmenities() != null && !summary.getAmenities().isEmpty()) {
            assertThat(model.getAmenities()).hasSize(summary.getAmenities().size());
        }

        // Location & coordinates
        if (summary.getLocation() != null) {
            assertThat(model.getLocation()).isNotNull();
            assertThat(model.getLocation().getAddress()).isEqualTo(summary.getLocation().getFullName());
            if (summary.getLocation().getCoordinates() != null) {
                assertThat(model.getCoordinates()).isNotNull();
                assertThat(model.getCoordinates().getLat()).isEqualTo(summary.getLocation().getCoordinates().getLat());
            }
        }
    }

    // -------------------------------------------------------------------------
    //                               Reflection helper                           
    // -------------------------------------------------------------------------

    @SuppressWarnings("unchecked")
    private Optional<ProjectModel> invokeConvertToProjectModel(ProjectDetails details) throws Exception {
        Method m = PropertyFinderApiService.class.getDeclaredMethod("convertToProjectModel", ProjectDetails.class);
        m.setAccessible(true);
        return (Optional<ProjectModel>) m.invoke(apiService, details);
    }

    // -------------------------------------------------------------------------
    //                               Utils                                       
    // -------------------------------------------------------------------------

    private void loadFixture(String path) throws IOException {
        try (InputStream is = this.getClass().getResourceAsStream(path)) {
            if (is == null) {
                throw new IllegalStateException("Fixture not found: " + path);
            }
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            var type = mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, ProjectDetailsPageProps.class);
            NextJsWrapper<ProjectDetailsPageProps> wrapper = mapper.readValue(json, type);
            sampleDetailsList.add(wrapper.getProps().getPageProps().getDetailResult());
        }
    }

    // Helper to convert source plan to legacy model for comparison
    private com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel convertToLegacy(ProjectDetails.PaymentPlan srcPlan) {
        if (srcPlan == null) return null;
        java.util.List<com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPhaseModel> phases = new java.util.ArrayList<>();
        if (srcPlan.getPhases() != null) {
            for (ProjectDetails.Phase p : srcPlan.getPhases()) {
                com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPhaseModel ppm = com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPhaseModel.builder()
                        .label(p.getLabel())
                        .value(p.getValue() == null ? null : p.getValue().doubleValue())
                        .build();
                phases.add(ppm);
            }
        }
        return com.realmond.temporal_service.crawler.uae.property_finder.legacy.dto.PaymentPlanModel.builder()
                .title(srcPlan.getTitle())
                .phases(phases.isEmpty() ? null : phases)
                .build();
    }
} 