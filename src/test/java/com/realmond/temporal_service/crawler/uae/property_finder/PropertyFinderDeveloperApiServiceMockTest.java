package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.property_finder.model.*;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Integration-style mock tests for {@link PropertyFinderDeveloperApiService} using
 * real JSON fixtures captured from Property Finder's front-end API.
 */
@ExtendWith(MockitoExtension.class)
class PropertyFinderDeveloperApiServiceMockTest {

    // --- Mocks ---------------------------------------------------------------------
    @Mock
    private PropertyFinderFeignRestClient client;

    @Mock
    private PropertyFinderSettings settings;

    // --- System under test ---------------------------------------------------------
    private PropertyFinderDeveloperApiService apiService;

    private ObjectMapper mapper;

    // --- JSON fixtures -------------------------------------------------------------
    private static final String DEVELOPERS_JSON_PATH = "/crawler/uae/propertyfinder/developers.json";
    private static final String DEVELOPER_DETAILS_JSON_PATH = "/crawler/uae/propertyfinder/developer-details.json";

    private NextJsWrapper<DevelopersPageProps> developersWrapper;
    private NextJsWrapper<DeveloperPageProps> developerDetailsWrapper;

    @BeforeEach
    void setUp() throws IOException {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();

        developersWrapper = loadFixture(DEVELOPERS_JSON_PATH,
                mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DevelopersPageProps.class));

        developerDetailsWrapper = loadFixture(DEVELOPER_DETAILS_JSON_PATH,
                mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DeveloperPageProps.class));

        apiService = new PropertyFinderDeveloperApiService(client, settings);
    }

    // ------------------------------------------------------------------------------
    //                               PAGES COUNT                                     
    // ------------------------------------------------------------------------------

    @Test
    void fetchDeveloperPagesCount_WithValidResponse_ShouldReturnCorrectPageCount() {
        // Given
        when(client.getDevelopers(1)).thenReturn(developersWrapper);

        // When
        int pages = apiService.fetchDeveloperPagesCount();

        // Then
        assertThat(pages).isEqualTo(37);
    }

    @Test
    void fetchDeveloperPagesCount_WithNoPagingInfo_ShouldReturnOne() {
        // Given – clone wrapper but strip pagination
        NextJsWrapper<DevelopersPageProps> noPaging = cloneDevelopersWrapper();
        noPaging.getProps().getPageProps().getDevelopersResult().setMeta(null);
        when(client.getDevelopers(1)).thenReturn(noPaging);

        // When
        int pages = apiService.fetchDeveloperPagesCount();

        // Then
        assertThat(pages).isEqualTo(1);
    }

    @Test
    void fetchDeveloperPagesCount_WhenClientThrowsException_ShouldThrowRetryableException() {
        when(client.getDevelopers(1)).thenThrow(new RuntimeException("boom"));
        assertThatThrownBy(() -> apiService.fetchDeveloperPagesCount())
                .isInstanceOf(RetryableCrawlerException.class)
                .hasMessageContaining("developer pages count")
                .hasRootCauseInstanceOf(RuntimeException.class);
    }

    // ------------------------------------------------------------------------------
    //                                PAGE FETCH                                     
    // ------------------------------------------------------------------------------

    @Test
    void fetchDevelopersPage_WithValidResponse_ShouldReturnCrawlRecords() {
        // GIVEN list + details for slug flagged with devPageEnabled=true
        when(client.getDevelopers(1)).thenReturn(developersWrapper);

        // find first dev that has devPageEnabled = true to setup details call
        DeveloperSummary needsDetails = developersWrapper.getProps().getPageProps()
                .getDevelopersResult().getData().stream()
                .filter(d -> Boolean.TRUE.equals(d.getDevPageEnabled()))
                .findFirst()
                .orElseThrow();

        when(client.getDeveloperDetails(needsDetails.getSlug())).thenReturn(developerDetailsWrapper);

        // WHEN
        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(1);

        // THEN – should match number of summaries
        int expected = developersWrapper.getProps().getPageProps().getDevelopersResult().getData().size();
        assertThat(results).hasSize(expected);

        // Validate first record basic mapping
        CrawlRecord<DeveloperModel> first = results.get(0);
        DeveloperSummary summary0 = developersWrapper.getProps().getPageProps().getDevelopersResult().getData().get(0);
        assertThat(first.data().getTitle()).isEqualTo(summary0.getName());
        assertThat(first.data().getExternalId()).isEqualTo(summary0.getId());
        assertThat(first.data().getSourceUrn()).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(first.source()).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(first.urn()).isEqualTo(PropertyFinderCommon.Developer.urn(summary0.getSlug()));

        // Metadata sanity
        assertThat(first.metadata()).containsKeys("raw_data", "conversion_timestamp", "source_api");
    }

    @Test
    void fetchDevelopersPage_WithEmptyResponse_ShouldReturnEmptyList() {
        // Given empty data list
        NextJsWrapper<DevelopersPageProps> wrapper = cloneDevelopersWrapper();
        wrapper.getProps().getPageProps().getDevelopersResult().setData(List.of());
        when(client.getDevelopers(99)).thenReturn(wrapper);

        // When
        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(99);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void fetchDevelopersPage_WithNullData_ShouldReturnEmptyList() {
        NextJsWrapper<DevelopersPageProps> wrapper = cloneDevelopersWrapper();
        wrapper.getProps().getPageProps().getDevelopersResult().setData(null);
        when(client.getDevelopers(99)).thenReturn(wrapper);

        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(99);
        assertThat(results).isEmpty();
    }

    @Test
    void fetchDevelopersPage_WhenClientThrowsException_ShouldThrowRetryableException() {
        when(client.getDevelopers(anyInt())).thenThrow(new RuntimeException("network"));
        assertThatThrownBy(() -> apiService.fetchDevelopersPage(1))
                .isInstanceOf(RetryableCrawlerException.class)
                .hasMessageContaining("developer page")
                .hasRootCauseInstanceOf(RuntimeException.class);
    }

    // ------------------------------------------------------------------------------
    //                                UTILS                                          
    // ------------------------------------------------------------------------------

    @SuppressWarnings("unchecked")
    private <T> T loadFixture(String path, com.fasterxml.jackson.databind.JavaType type) throws IOException {
        try (var is = this.getClass().getResourceAsStream(path)) {
            if (is == null) {
                throw new IllegalStateException("Fixture not found: " + path);
            }
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            return (T) mapper.readValue(json, type);
        }
    }

    private NextJsWrapper<DevelopersPageProps> cloneDevelopersWrapper() {
        try {
            byte[] bytes = mapper.writeValueAsBytes(developersWrapper);
            return mapper.readValue(bytes,
                    mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DevelopersPageProps.class));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
} 