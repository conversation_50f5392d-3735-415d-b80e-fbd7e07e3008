package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.DeveloperModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.property_finder.model.*;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for {@link PropertyFinderDeveloperApiService} focusing on
 * developer data fetching, conversion, and error handling scenarios.
 */
@ExtendWith(MockitoExtension.class)
class PropertyFinderDeveloperApiServiceTest {

    private static final String DEVELOPERS_JSON_PATH = "/crawler/uae/propertyfinder/developers.json";
    private static final String DEVELOPER_DETAILS_JSON_PATH = "/crawler/uae/propertyfinder/developer-details.json";

    // --- Mocks -----------------------------------------------------------------
    @Mock
    private PropertyFinderFeignRestClient feignClient;

    @Mock
    private PropertyFinderSettings settings;

    // --- System under test -----------------------------------------------------
    private PropertyFinderDeveloperApiService apiService;

    private ObjectMapper mapper;
    private NextJsWrapper<DevelopersPageProps> developersWrapper;
    private NextJsWrapper<DeveloperPageProps> developerDetailsWrapper;
    private List<DeveloperSummary> sampleSummaries;
    private DeveloperDetails sampleDetails;

    @BeforeEach
    void setUp() throws IOException {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();

        // Load developers listing fixture
        loadDevelopersFixture();

        // Load developer details fixture
        loadDeveloperDetailsFixture();

        apiService = new PropertyFinderDeveloperApiService(feignClient, settings);
    }

    @Test
    void fetchDeveloperPagesCount_WithValidResponse_ShouldReturnTotalPages() {
        // GIVEN
        when(feignClient.getDevelopers(1)).thenReturn(developersWrapper);

        // WHEN
        int result = apiService.fetchDeveloperPagesCount();

        // THEN
        Integer expectedTotal = developersWrapper.getProps().getPageProps()
                .getDevelopersResult().getMeta().getPagination().getTotal();
        assertThat(result).isEqualTo(expectedTotal);
    }

    @Test
    void fetchDeveloperPagesCount_WithMissingPagination_ShouldReturnDefaultOne() {
        // GIVEN - wrapper with null pagination
        NextJsWrapper<DevelopersPageProps> wrapperWithoutPagination = createDevelopersWrapperWithoutPagination();
        when(feignClient.getDevelopers(1)).thenReturn(wrapperWithoutPagination);

        // WHEN
        int result = apiService.fetchDeveloperPagesCount();

        // THEN
        assertThat(result).isEqualTo(1);
    }

    @Test
    void fetchDeveloperPagesCount_WithException_ShouldThrowRetryableCrawlerException() {
        // GIVEN
        when(feignClient.getDevelopers(1)).thenThrow(new RuntimeException("API error"));

        // WHEN & THEN
        assertThatThrownBy(() -> apiService.fetchDeveloperPagesCount())
                .isInstanceOf(RetryableCrawlerException.class)
                .hasMessageContaining("Error fetching Property Finder developer pages count");
    }

    @Test
    void fetchDevelopersPage_WithValidResponse_ShouldReturnCrawlRecords() {
        // GIVEN
        when(feignClient.getDevelopers(1)).thenReturn(developersWrapper);

        // Find first developer with devPageEnabled=true to setup details call
        DeveloperSummary needsDetails = sampleSummaries.stream()
                .filter(d -> Boolean.TRUE.equals(d.getDevPageEnabled()))
                .findFirst()
                .orElseThrow();

        when(feignClient.getDeveloperDetails(needsDetails.getSlug())).thenReturn(developerDetailsWrapper);

        // WHEN
        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(1);

        // THEN
        assertThat(results).hasSize(sampleSummaries.size());

        // Validate first record basic mapping
        CrawlRecord<DeveloperModel> first = results.get(0);
        DeveloperSummary summary0 = sampleSummaries.get(0);
        assertThat(first.data().getTitle()).isEqualTo(summary0.getName());
        assertThat(first.data().getExternalId()).isEqualTo(summary0.getId());
        assertThat(first.data().getSourceUrn()).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(first.source()).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(first.urn()).isEqualTo(PropertyFinderCommon.Developer.urn(summary0.getSlug()));

        // Metadata sanity check
        assertThat(first.metadata()).containsKeys("raw_data", "conversion_timestamp", "source_api");
    }

    @Test
    void fetchDevelopersPage_WithNoData_ShouldReturnEmptyList() {
        // GIVEN - wrapper with null data
        NextJsWrapper<DevelopersPageProps> emptyWrapper = createEmptyDevelopersWrapper();
        when(feignClient.getDevelopers(1)).thenReturn(emptyWrapper);

        // WHEN
        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(1);

        // THEN
        assertThat(results).isEmpty();
    }

    @Test
    void fetchDevelopersPage_WithNotFound_ShouldReturnEmptyList() {
        // GIVEN
        when(feignClient.getDevelopers(99)).thenThrow(FeignException.NotFound.class);

        // WHEN
        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(99);

        // THEN
        assertThat(results).isEmpty();
    }

    @Test
    void fetchDevelopersPage_WithDeveloperDetailsNotFound_ShouldFallbackToSummary() {
        // GIVEN
        when(feignClient.getDevelopers(1)).thenReturn(developersWrapper);

        DeveloperSummary needsDetails = sampleSummaries.stream()
                .filter(d -> Boolean.TRUE.equals(d.getDevPageEnabled()))
                .findFirst()
                .orElseThrow();

        when(feignClient.getDeveloperDetails(needsDetails.getSlug()))
                .thenThrow(FeignException.NotFound.class);

        // WHEN
        List<CrawlRecord<DeveloperModel>> results = apiService.fetchDevelopersPage(1);

        // THEN
        assertThat(results).hasSize(sampleSummaries.size());
        // Should still process all developers using summary fallback
    }

    @Test
    void fetchDevelopersPage_WithException_ShouldThrowRetryableCrawlerException() {
        // GIVEN
        when(feignClient.getDevelopers(1)).thenThrow(new RuntimeException("API error"));

        // WHEN & THEN
        assertThatThrownBy(() -> apiService.fetchDevelopersPage(1))
                .isInstanceOf(RetryableCrawlerException.class)
                .hasMessageContaining("Error fetching Property Finder developer page 1");
    }

    @Test
    void toDeveloperModel_WithValidSummaryAndDetails_ShouldMapCorrectly() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);
        Optional<DeveloperDetails> details = Optional.of(sampleDetails);

        // WHEN
        Optional<DeveloperModel> result = invokeToDeveloperModel(summary, details);

        // THEN
        assertThat(result).isPresent();
        DeveloperModel model = result.get();

        assertThat(model.getTitle()).isEqualTo(summary.getName());
        assertThat(model.getExternalId()).isEqualTo(summary.getId());
        assertThat(model.getSourceUrn()).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(model.getDeveloperUrn()).isEqualTo(PropertyFinderCommon.Developer.urn(summary.getSlug()));

        // Should prefer details description over summary
        if (details.get().getDescription() != null && !details.get().getDescription().isBlank()) {
            assertThat(model.getDescription()).isNotBlank();
        }

        // Should prefer details logo over summary
        if (details.get().getLogoUrl() != null && !details.get().getLogoUrl().isBlank()) {
            assertThat(model.getLogoUrl()).isEqualTo(details.get().getLogoUrl());
        }
    }

    @Test
    void toDeveloperModel_WithSummaryOnly_ShouldMapCorrectly() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);
        Optional<DeveloperDetails> details = Optional.empty();

        // WHEN
        Optional<DeveloperModel> result = invokeToDeveloperModel(summary, details);

        // THEN
        assertThat(result).isPresent();
        DeveloperModel model = result.get();

        assertThat(model.getTitle()).isEqualTo(summary.getName());
        assertThat(model.getExternalId()).isEqualTo(summary.getId());
        assertThat(model.getSourceUrn()).isEqualTo(PropertyFinderCommon.SOURCE_URN);

        // Should use summary data when details not available
        if (summary.getLogoUrl() != null && !summary.getLogoUrl().isBlank()) {
            assertThat(model.getLogoUrl()).isEqualTo(summary.getLogoUrl());
        }
    }

    @Test
    void toDeveloperModel_WithIncompleteSummary_ShouldReturnEmpty() throws Exception {
        // GIVEN - summary with missing id
        DeveloperSummary incompleteSummary = new DeveloperSummary();
        incompleteSummary.setName("Test Developer");
        // missing id

        // WHEN
        Optional<DeveloperModel> result = invokeToDeveloperModel(incompleteSummary, Optional.empty());

        // THEN
        assertThat(result).isEmpty();
    }

    @Test
    void toDeveloperModel_WithNullName_ShouldReturnEmpty() throws Exception {
        // GIVEN - summary with null name
        DeveloperSummary invalidSummary = new DeveloperSummary();
        invalidSummary.setId("test-id");
        // missing name

        // WHEN
        Optional<DeveloperModel> result = invokeToDeveloperModel(invalidSummary, Optional.empty());

        // THEN
        assertThat(result).isEmpty();
    }

    @Test
    void toDeveloperModel_ShouldMapEstablishedDate() throws Exception {
        // GIVEN - summary with established date
        DeveloperSummary summary = sampleSummaries.get(0);
        if (summary.getEstablishedSince() != null) {
            // WHEN
            Optional<DeveloperModel> result = invokeToDeveloperModel(summary, Optional.empty());

            // THEN
            assertThat(result).isPresent();
            LocalDate expectedFounded = summary.getEstablishedSince().toLocalDate();
            assertThat(result.get().getFounded()).isEqualTo(expectedFounded.toString());
        }
    }

    @Test
    void toDeveloperModel_ShouldMapDeveloperStats() throws Exception {
        // GIVEN - summary with numProjectsOnline
        DeveloperSummary summary = sampleSummaries.get(0);
        if (summary.getNumProjectsOnline() != null) {
            // WHEN
            Optional<DeveloperModel> result = invokeToDeveloperModel(summary, Optional.empty());

            // THEN
            assertThat(result).isPresent();
            assertThat(result.get().getDeveloperStats()).isNotNull();
            assertThat(result.get().getDeveloperStats().getNotCompletedProjectsCount())
                    .isEqualTo(summary.getNumProjectsOnline());
        }
    }

    @Test
    void toDeveloperModel_ShouldMapAdditionalData() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);
        Optional<DeveloperDetails> details = Optional.of(sampleDetails);

        // WHEN
        Optional<DeveloperModel> result = invokeToDeveloperModel(summary, details);

        // THEN
        assertThat(result).isPresent();
        DeveloperModel model = result.get();

        assertThat(model.getAdditionalData()).isInstanceOf(Map.class);
        @SuppressWarnings("unchecked") 
        Map<String, Object> additionalData = (Map<String, Object>) model.getAdditionalData();

        assertThat(additionalData.get("dev_page_enabled")).isEqualTo(summary.getDevPageEnabled());
        assertThat(additionalData.get("slug")).isEqualTo(summary.getSlug());

        if (details.isPresent() && details.get().getContactOptions() != null) {
            assertThat(additionalData.get("contact_options")).isEqualTo(details.get().getContactOptions());
        }
    }

    @Test
    void convertSummaryToDeveloperModel_WithValidSummary_ShouldReturnModel() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);

        // WHEN
        Optional<DeveloperModel> result = invokeConvertSummaryToDeveloperModel(summary);

        // THEN
        assertThat(result).isPresent();
        DeveloperModel model = result.get();

        assertThat(model.getTitle()).isEqualTo(summary.getName());
        assertThat(model.getExternalId()).isEqualTo(summary.getId());
        assertThat(model.getSourceUrn()).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(model.getDeveloperUrn()).isEqualTo(PropertyFinderCommon.Developer.urn(summary.getSlug()));
    }

    @Test
    void convertSummaryToDeveloperModel_WithNullSummary_ShouldReturnEmpty() throws Exception {
        // WHEN
        Optional<DeveloperModel> result = invokeConvertSummaryToDeveloperModel(null);

        // THEN
        assertThat(result).isEmpty();
    }

    @Test
    void convertSummaryToDeveloperModel_WithIncompleteSummary_ShouldReturnEmpty() throws Exception {
        // GIVEN - summary with missing required fields
        DeveloperSummary incompleteSummary = new DeveloperSummary();
        incompleteSummary.setName("Test");
        // missing id

        // WHEN
        Optional<DeveloperModel> result = invokeConvertSummaryToDeveloperModel(incompleteSummary);

        // THEN
        assertThat(result).isEmpty();
    }

    @Test
    void slugify_ShouldCreateValidSlugs() throws Exception {
        // WHEN & THEN
        assertThat(invokeSlugify("Test Developer")).isEqualTo("test-developer");
        assertThat(invokeSlugify("H&H Development")).isEqualTo("hh-development");
        assertThat(invokeSlugify("MAG Property Development")).isEqualTo("mag-property-development");
        assertThat(invokeSlugify("Majid Al Futtaim")).isEqualTo("majid-al-futtaim");
        assertThat(invokeSlugify("")).isEqualTo("unknown");
        assertThat(invokeSlugify(null)).isEqualTo("unknown");
        assertThat(invokeSlugify("   ")).isEqualTo("unknown");
        assertThat(invokeSlugify("Test---Multiple---Dashes")).isEqualTo("test-multiple-dashes");
        assertThat(invokeSlugify("-Leading-Trailing-")).isEqualTo("leading-trailing");
    }

    @Test
    void convert_ShouldCreateMetadata() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);
        Optional<DeveloperDetails> details = Optional.of(sampleDetails);

        // WHEN
        Object result = invokeConvert(summary, details);

        // THEN - use reflection to check the DeveloperConversionResult record
        Method developerModelMethod = result.getClass().getMethod("developerModel");
        Method rawApiMetadataMethod = result.getClass().getMethod("rawApiMetadata");

        @SuppressWarnings("unchecked")
        Optional<DeveloperModel> developerModel = (Optional<DeveloperModel>) developerModelMethod.invoke(result);
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) rawApiMetadataMethod.invoke(result);

        assertThat(developerModel).isPresent();
        assertThat(metadata).containsKeys("raw_data", "conversion_timestamp", "source_api");
        assertThat(metadata.get("source_api")).isEqualTo("propertyfinder");

        @SuppressWarnings("unchecked")
        Map<String, Object> rawData = (Map<String, Object>) metadata.get("raw_data");
        assertThat(rawData.get("developer_summary")).isEqualTo(summary);
        assertThat(rawData.get("developer_details")).isEqualTo(details.get());
    }

    @Test
    void wrapInCrawlRecord_ShouldCreateValidRecord() throws Exception {
        // GIVEN
        DeveloperModel model = DeveloperModel.builder()
                .withDeveloperUrn("test:urn")
                .withSourceUrn(PropertyFinderCommon.SOURCE_URN)
                .withTitle("Test Developer")
                .withExternalId("test-id")
                .build();
        Map<String, Object> metadata = Map.of("test", "value");

        // WHEN
        CrawlRecord<DeveloperModel> result = invokeWrapInCrawlRecord(model, metadata);

        // THEN
        assertThat(result.data()).isEqualTo(model);
        assertThat(result.metadata()).isEqualTo(metadata);
        assertThat(result.source()).isEqualTo(model.getSourceUrn());
        assertThat(result.urn()).isEqualTo(model.getDeveloperUrn());
    }

    @Test
    void createMetadata_ShouldCreateCorrectStructure() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);
        DeveloperDetails details = sampleDetails;

        // WHEN
        Map<String, Object> result = invokeCreateMetadata(summary, details);

        // THEN
        assertThat(result).containsKeys("raw_data", "conversion_timestamp", "source_api", "api_version");
        assertThat(result.get("source_api")).isEqualTo("propertyfinder");
        assertThat(result.get("api_version")).isEqualTo("v1");

        @SuppressWarnings("unchecked")
        Map<String, Object> rawData = (Map<String, Object>) result.get("raw_data");
        assertThat(rawData.get("developer_summary")).isEqualTo(summary);
        assertThat(rawData.get("developer_details")).isEqualTo(details);
    }

    @Test
    void createMetadata_WithNullDetails_ShouldWorkCorrectly() throws Exception {
        // GIVEN
        DeveloperSummary summary = sampleSummaries.get(0);

        // WHEN
        Map<String, Object> result = invokeCreateMetadata(summary, null);

        // THEN
        assertThat(result).containsKeys("raw_data", "conversion_timestamp", "source_api", "api_version");

        @SuppressWarnings("unchecked")
        Map<String, Object> rawData = (Map<String, Object>) result.get("raw_data");
        assertThat(rawData.get("developer_summary")).isEqualTo(summary);
        assertThat(rawData.containsKey("developer_details")).isFalse();
    }

    // -------------------------------------------------------------------------
    //                               Helper Methods                               
    // -------------------------------------------------------------------------

    private void loadDevelopersFixture() throws IOException {
        try (InputStream is = this.getClass().getResourceAsStream(DEVELOPERS_JSON_PATH)) {
            if (is == null) {
                throw new IllegalStateException("Fixture not found: " + DEVELOPERS_JSON_PATH);
            }
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            var type = mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DevelopersPageProps.class);
            developersWrapper = mapper.readValue(json, type);
            sampleSummaries = developersWrapper.getProps().getPageProps()
                    .getDevelopersResult().getData();
        }
    }

    private void loadDeveloperDetailsFixture() throws IOException {
        try (InputStream is = this.getClass().getResourceAsStream(DEVELOPER_DETAILS_JSON_PATH)) {
            if (is == null) {
                throw new IllegalStateException("Fixture not found: " + DEVELOPER_DETAILS_JSON_PATH);
            }
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            var type = mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DeveloperPageProps.class);
            developerDetailsWrapper = mapper.readValue(json, type);
            sampleDetails = developerDetailsWrapper.getProps().getPageProps()
                    .getDevResult().getDeveloper();
        }
    }

    private NextJsWrapper<DevelopersPageProps> createDevelopersWrapperWithoutPagination() {
        NextJsWrapper<DevelopersPageProps> wrapper = new NextJsWrapper<>();
        NextJsWrapper.Props<DevelopersPageProps> props = new NextJsWrapper.Props<>();
        DevelopersPageProps pageProps = new DevelopersPageProps();
        DevelopersPageProps.DevelopersResult result = new DevelopersPageProps.DevelopersResult();
        DevelopersPageProps.Meta meta = new DevelopersPageProps.Meta();
        // Null pagination
        result.setMeta(meta);
        pageProps.setDevelopersResult(result);
        props.setPageProps(pageProps);
        wrapper.setProps(props);
        return wrapper;
    }

    private NextJsWrapper<DevelopersPageProps> createEmptyDevelopersWrapper() {
        NextJsWrapper<DevelopersPageProps> wrapper = new NextJsWrapper<>();
        NextJsWrapper.Props<DevelopersPageProps> props = new NextJsWrapper.Props<>();
        DevelopersPageProps pageProps = new DevelopersPageProps();
        DevelopersPageProps.DevelopersResult result = new DevelopersPageProps.DevelopersResult();
        result.setData(null); // Null data
        pageProps.setDevelopersResult(result);
        props.setPageProps(pageProps);
        wrapper.setProps(props);
        return wrapper;
    }

    // -------------------------------------------------------------------------
    //                               Reflection Helpers                           
    // -------------------------------------------------------------------------

    @SuppressWarnings("unchecked")
    private Optional<DeveloperModel> invokeToDeveloperModel(DeveloperSummary summary, Optional<DeveloperDetails> details) throws Exception {
        Method m = PropertyFinderDeveloperApiService.class.getDeclaredMethod("toDeveloperModel", DeveloperSummary.class, Optional.class);
        m.setAccessible(true);
        return (Optional<DeveloperModel>) m.invoke(apiService, summary, details);
    }

    @SuppressWarnings("unchecked")
    private Optional<DeveloperModel> invokeConvertSummaryToDeveloperModel(DeveloperSummary summary) throws Exception {
        Method m = PropertyFinderDeveloperApiService.class.getDeclaredMethod("convertSummaryToDeveloperModel", DeveloperSummary.class);
        m.setAccessible(true);
        return (Optional<DeveloperModel>) m.invoke(apiService, summary);
    }

    private String invokeSlugify(String name) throws Exception {
        Method m = PropertyFinderDeveloperApiService.class.getDeclaredMethod("slugify", String.class);
        m.setAccessible(true);
        return (String) m.invoke(apiService, name);
    }

    private Object invokeConvert(DeveloperSummary summary, Optional<DeveloperDetails> details) throws Exception {
        Method m = PropertyFinderDeveloperApiService.class.getDeclaredMethod("convert", DeveloperSummary.class, Optional.class);
        m.setAccessible(true);
        return m.invoke(apiService, summary, details);
    }

    @SuppressWarnings("unchecked")
    private CrawlRecord<DeveloperModel> invokeWrapInCrawlRecord(DeveloperModel model, Map<String, Object> metadata) throws Exception {
        Method m = PropertyFinderDeveloperApiService.class.getDeclaredMethod("wrapInCrawlRecord", DeveloperModel.class, Map.class);
        m.setAccessible(true);
        return (CrawlRecord<DeveloperModel>) m.invoke(apiService, model, metadata);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> invokeCreateMetadata(DeveloperSummary summary, DeveloperDetails details) throws Exception {
        Method m = PropertyFinderDeveloperApiService.class.getDeclaredMethod("createMetadata", DeveloperSummary.class, DeveloperDetails.class);
        m.setAccessible(true);
        return (Map<String, Object>) m.invoke(apiService, summary, details);
    }
} 