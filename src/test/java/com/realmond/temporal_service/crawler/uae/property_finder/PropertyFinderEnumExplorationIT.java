package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.property_finder.model.NextJsWrapper;
import com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectDetailsPageProps;
import com.realmond.temporal_service.crawler.uae.property_finder.model.ProjectSummary;
import com.realmond.temporal_service.crawler.uae.property_finder.model.SearchResultPageProps;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Integration-test that scans live Property Finder data to collect distinct enumeration values
 * for the following fields:
 *  – ownershipType
 *  – salesPhase
 *  – timelinePhases.phaseKey
 *  – timelinePhases.category
 *
 * It prints the discovered sets so that engineers can update the mapping spec and whitelist file.
 *
 * Enable by setting RUN_INTEGRATION_TESTS=true (same flag as other ITs).
 */
@Slf4j
@SpringBootTest(classes = PropertyFinderEnumExplorationIT.Config.class, webEnvironment = WebEnvironment.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
@Disabled("For manual exploration only.")
public class PropertyFinderEnumExplorationIT {

    @SpringBootConfiguration
    @EnableFeignClients(basePackageClasses = PropertyFinderFeignRestClient.class)
    @Import({FingerprintGenerator.class, PropertyFinderSettings.class})
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class Config {
    }

    @Autowired
    private PropertyFinderFeignRestClient pfClient;

    private final Set<String> ownershipTypes   = new TreeSet<>();
    private final Set<String> salesPhases      = new TreeSet<>();
    private final Set<String> phaseKeys        = new TreeSet<>();
    private final Set<String> phaseCategories  = new TreeSet<>();

    private final ObjectMapper mapper = new ObjectMapper();

    @Test
    void exploreAllProjectsAndEnums() {
        int maxPages = 50; // safety cap – adjust if needed
        int processedProjects = 0;

        // Build list 1..maxPages and shuffle to randomise crawling order
        List<Integer> pageOrder = java.util.stream.IntStream.rangeClosed(1, maxPages)
                .boxed()
                .collect(java.util.stream.Collectors.toList());
        java.util.Collections.shuffle(pageOrder, new java.util.Random());

        for (int pageNo : pageOrder) {
            log.info("Fetching new-projects page {}", pageNo);
            NextJsWrapper<SearchResultPageProps> search = pfClient.getNewProjects(pageNo);
            if (search == null || search.getProps() == null || search.getProps().getPageProps() == null) {
                continue; // skip invalid page
            }
            var data = search.getProps()
                             .getPageProps()
                             .getSearchResult();
            if (data == null || data.getData() == null || data.getData().getProjects() == null ||
                data.getData().getProjects().getDeveloper() == null) {
                continue;
            }

            List<ProjectSummary> projects = data.getData().getProjects().getDeveloper();
            if (projects.isEmpty()) {
                continue;
            }

            for (ProjectSummary ps : projects) {
                String shareUrl = ps.getShareUrl();
                if (shareUrl == null || !shareUrl.contains("new-projects/")) {
                    continue;
                }
                String slugPart = shareUrl.substring(shareUrl.indexOf("new-projects/") + "new-projects/".length());
                String[] parts = slugPart.split("/");
                if (parts.length < 2) {
                    continue;
                }
                String developerSlug = parts[0];
                String projectSlug   = parts[1];
                try {
                    NextJsWrapper<ProjectDetailsPageProps> details = pfClient.getProjectDetails(developerSlug, projectSlug);
                    var detailResult = details.getProps().getPageProps().getDetailResult();
                    // ownershipType
                    if (detailResult.getOwnershipType() != null && !detailResult.getOwnershipType().isBlank()) {
                        ownershipTypes.add(detailResult.getOwnershipType().toLowerCase());
                    } else {
                        ownershipTypes.add("<null>");
                    }
                    // salesPhase
                    if (detailResult.getSalesPhase() != null && !detailResult.getSalesPhase().isBlank()) {
                        salesPhases.add(detailResult.getSalesPhase().toLowerCase());
                    } else {
                        salesPhases.add("<null>");
                    }
                    // timeline phases
                    if (detailResult.getTimelinePhases() != null) {
                        detailResult.getTimelinePhases().forEach(tp -> {
                            if (tp.getPhaseKey() != null) {
                                phaseKeys.add(tp.getPhaseKey().toLowerCase());
                            }
                            if (tp.getCategory() != null) {
                                phaseCategories.add(tp.getCategory().toLowerCase());
                            }
                        });
                    }
                    processedProjects++;
                } catch (Exception ex) {
                    log.warn("Error fetching project {}: {}", projectSlug, ex.getMessage());
                }
            }
        }

        log.info("Explored {} projects across {} pages", processedProjects, pageOrder.size());
    }

    @AfterAll
    void printResults() {
        log.info("=== ownershipType values === {}", stringifySet(ownershipTypes));
        log.info("=== salesPhase values === {}", stringifySet(salesPhases));
        log.info("=== timeline phaseKey values === {}", stringifySet(phaseKeys));
        log.info("=== timeline category values === {}", stringifySet(phaseCategories));
    }

    private String stringifySet(Set<String> set) {
        return set.stream().sorted().collect(Collectors.joining(", ", "[", "]"));
    }
}
