package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.property_finder.model.*;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.junit.jupiter.api.Assumptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Integration tests for {@link PropertyFinderFeignRestClient} that hit the real
 * Property&nbsp;Finder website.
 * <p>
 * These tests require internet access and are disabled by default. Enable them
 * by setting the environment variable <code>RUN_INTEGRATION_TESTS=true</code>.
 */
@SpringBootTest(classes = {PropertyFinderFeignRestClientIT.TestConfig.class}, webEnvironment = WebEnvironment.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
class PropertyFinderFeignRestClientIT {

    @Configuration
    @EnableFeignClients(basePackageClasses = PropertyFinderFeignRestClient.class)
    @Import({FingerprintGenerator.class, PropertyFinderSettings.class})
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {
        // PropertyFinderFeignConfiguration handles ProxyManager via ObjectProvider
    }

    @Autowired
    private PropertyFinderFeignRestClient pfClient;

    private ProjectSummary firstProject;
    private String developerSlug; // slug with a landing page enabled
    private String projectSlug;
    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeAll
    void setup() {
        // ------------------------------------------------------------
        // 1) Determine a developer that has a public landing page.
        // ------------------------------------------------------------
        int page = 1;
        boolean devFound = false;
        while (page <= 5 && !devFound) {
            NextJsWrapper<DevelopersPageProps> list = pfClient.getDevelopers(page);
            if (list == null || list.getProps() == null || list.getProps().getPageProps() == null) {
                page++;
                continue;
            }

            List<DeveloperSummary> developers = list.getProps()
                                                   .getPageProps()
                                                   .getDevelopersResult()
                                                   .getData();
            if (developers == null) {
                page++;
                continue;
            }

            for (DeveloperSummary dev : developers) {
                if (Boolean.TRUE.equals(dev.getDevPageEnabled())) {
                    developerSlug = dev.getSlug();
                    devFound = true;
                    break;
                }
            }
            page++;
        }

        // ------------------------------------------------------------
        // 2) Grab a project (any) to test project-detail endpoint.
        // ------------------------------------------------------------
        int projPage = 1;
        boolean projectFound = false;
        while (projPage <= 5 && !projectFound) {
            NextJsWrapper<SearchResultPageProps> pageRes = pfClient.getNewProjects(projPage);
            if (pageRes == null || pageRes.getProps() == null || pageRes.getProps().getPageProps() == null) {
                projPage++;
                continue;
            }

            List<ProjectSummary> projects = pageRes.getProps()
                                                 .getPageProps()
                                                 .getSearchResult()
                                                 .getData()
                                                 .getProjects()
                                                 .getDeveloper();
            if (projects == null) {
                projPage++;
                continue;
            }

            ProjectSummary ps = projects.stream().findFirst().orElse(null);
            if (ps != null) {
                firstProject = ps;
                String url = ps.getShareUrl();
                if (url != null && url.contains("new-projects/")) {
                    String slugPart = url.substring(url.indexOf("new-projects/") + "new-projects/".length());
                    String[] parts = slugPart.split("/");
                    if (parts.length >= 2) {
                        projectSlug = parts[1];
                    }
                }
                projectFound = true;
            }
            projPage++;
        }

        Assumptions.assumeTrue(devFound, "Could not find a developer landing page slug via live site");
        Assumptions.assumeTrue(projectFound, "Could not find a project via live site");
    }

    @Test
    void testGetNewProjects() {
        NextJsWrapper<SearchResultPageProps> response = pfClient.getNewProjects(1);
        assertThat(response).isNotNull();
        assertThat(response.getProps()).isNotNull();
        assertThat(response.getProps().getPageProps()).isNotNull();

        try {
            System.out.println("=== NewProjects PageProps ===\n" +
                    mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response.getProps().getPageProps()));
        } catch (Exception ignored) {}
    }

    @Test
    void testGetProjectDetails() {
        Assumptions.assumeTrue(projectSlug != null && !projectSlug.isBlank(), "Project slug unavailable – skipping");
        NextJsWrapper<ProjectDetailsPageProps> response = pfClient.getProjectDetails(developerSlug, projectSlug);
        assertThat(response).isNotNull();
        assertThat(response.getProps()).isNotNull();
        assertThat(response.getProps().getPageProps()).isNotNull();
        assertThat(response.getProps().getPageProps().getDetailResult()).isNotNull();

        try {
            System.out.println("=== ProjectDetails detailResult ===\n" +
                    mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response.getProps().getPageProps().getDetailResult()));
        } catch (Exception ignored) {}
    }

    @Test
    void testGetDevelopersList() {
        NextJsWrapper<DevelopersPageProps> response = pfClient.getDevelopers(1);
        assertThat(response).isNotNull();
        assertThat(response.getProps()).isNotNull();
        assertThat(response.getProps().getPageProps()).isNotNull();
        assertThat(response.getProps().getPageProps().getDevelopersResult()).isNotNull();

        try {
            System.out.println("=== DevelopersResult ===\n" +
                    mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response.getProps().getPageProps().getDevelopersResult()));
        } catch (Exception ignored) {}
    }

    @Test
    void testGetDeveloperDetails() {
        try {
            NextJsWrapper<DeveloperPageProps> response = pfClient.getDeveloperDetails(developerSlug);
            assertThat(response).isNotNull();
            assertThat(response.getProps()).isNotNull();
            assertThat(response.getProps().getPageProps()).isNotNull();
            assertThat(response.getProps().getPageProps().getDevResult()).isNotNull();

            try {
                System.out.println("=== DeveloperDetails devResult ===\n" +
                        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response.getProps().getPageProps().getDevResult()));
            } catch (Exception ignored) {}

        } catch (feign.FeignException.NotFound nf) {
            Assumptions.assumeTrue(false, "Landing page not available for selected developer – skipping");
        }
    }
} 