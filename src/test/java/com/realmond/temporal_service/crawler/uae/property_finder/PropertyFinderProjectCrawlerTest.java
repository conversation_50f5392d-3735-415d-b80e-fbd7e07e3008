package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.property_finder.model.*;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Integration tests for {@link PropertyFinderProjectCrawler} using mocked dependencies.
 * 
 * This test suite covers all public methods of PropertyFinderProjectCrawler and uses
 * realistic data patterns from the PropertyFinder API responses found in test fixtures.
 */
@ExtendWith(MockitoExtension.class)
class PropertyFinderProjectCrawlerTest {

    @Mock
    private PropertyFinderFeignRestClient feignRestClient;

    @Mock
    private PropertyFinderApiService apiService;

    @InjectMocks
    private PropertyFinderProjectCrawler crawler;

    private ObjectMapper mapper;
    private NextJsWrapper<SearchResultPageProps> sampleSearchResultWrapper;
    private NextJsWrapper<ProjectDetailsPageProps> sampleProjectDetailsWrapper1;
    private NextJsWrapper<ProjectDetailsPageProps> sampleProjectDetailsWrapper2;
    private CrawlRecord<ProjectModel> sampleCrawlRecord;

    @BeforeEach
    void setUp() throws IOException {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        
        // Load test fixtures
        loadTestFixtures();
        
        // Create sample CrawlRecord for API service mock
        sampleCrawlRecord = createSampleCrawlRecord();
    }

    @Test
    void getSourceUrn_ShouldReturnCorrectUrn() {
        String sourceUrn = crawler.getSourceUrn();
        
        assertThat(sourceUrn).isEqualTo(PropertyFinderCommon.SOURCE_URN);
        assertThat(sourceUrn).isEqualTo("urn:source:propertyfinder:ae");
    }

    @Test
    void supportsPagination_ShouldReturnTrue() {
        Boolean supportsPagination = crawler.supportsPagination();
        
        assertThat(supportsPagination).isTrue();
    }

    @Test
    void fetchAllPageIds_WithValidPagination_ShouldReturnCorrectPageIds() {
        // Given: Mock API service returns 85 pages (from test fixture)
        when(apiService.getTotalPages()).thenReturn(85);

        // When: Fetch all page IDs
        List<String> pageIds = crawler.fetchAllPageIds();

        // Then: Should return page numbers 1 through 85 as strings
        assertThat(pageIds).hasSize(85);
        assertThat(pageIds.get(0)).isEqualTo("1");
        assertThat(pageIds.get(84)).isEqualTo("85");
        
        verify(apiService).getTotalPages();
    }

    @Test
    void fetchAllPageIds_WithSinglePage_ShouldReturnEmptyList() {
        // Given: Mock API service returns 1 page
        when(apiService.getTotalPages()).thenReturn(1);

        // When: Fetch all page IDs
        List<String> pageIds = crawler.fetchAllPageIds();

        // Then: Should return empty list (only page 1 will be crawled by framework)
        assertThat(pageIds).isEmpty();

        verify(apiService).getTotalPages();
    }

    @Test
    void fetchAllPageIds_WithNullPagination_ShouldReturnEmptyList() {
        // Given: Mock API service returns null pagination
        when(apiService.getTotalPages()).thenReturn(null);

        // When: Fetch all page IDs
        List<String> pageIds = crawler.fetchAllPageIds();

        // Then: Should return empty list
        assertThat(pageIds).isEmpty();

        verify(apiService).getTotalPages();
    }

    @Test
    void fetchAllPageIds_WithZeroPages_ShouldReturnEmptyList() {
        // Given: Mock API service returns 0 pages
        when(apiService.getTotalPages()).thenReturn(0);

        // When: Fetch all page IDs
        List<String> pageIds = crawler.fetchAllPageIds();

        // Then: Should return empty list
        assertThat(pageIds).isEmpty();

        verify(apiService).getTotalPages();
    }

    @Test
    void fetchAllPageIds_WhenServiceThrowsRetryableException_ShouldPropagateException() {
        // Given: Mock API service throws RetryableCrawlerException
        when(apiService.getTotalPages())
            .thenThrow(new RetryableCrawlerException("API temporarily unavailable"));

        // When & Then: Should propagate the exception
        assertThatThrownBy(() -> crawler.fetchAllPageIds())
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("API temporarily unavailable");

        verify(apiService).getTotalPages();
    }

    @Test
    void parsePage_WithValidPageNumber_ShouldReturnProjectsFromPage() {
        // Given: Mock API service returns projects for page 1
        List<CrawlRecord<ProjectModel>> expectedProjects = List.of(sampleCrawlRecord);
        when(apiService.fetchProjectsPage(1)).thenReturn(expectedProjects);

        // When: Parse page 1
        List<CrawlRecord<ProjectModel>> result = crawler.parsePage("1");

        // Then: Should return the projects from the service
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(sampleCrawlRecord);
        assertThat(result.get(0).data().getSourceUrn()).isEqualTo("urn:source:propertyfinder:ae");

        verify(apiService).fetchProjectsPage(1);
    }

    @Test
    void parsePage_WithEmptyResults_ShouldReturnEmptyList() {
        // Given: Mock API service returns empty list for page 999
        when(apiService.fetchProjectsPage(999)).thenReturn(Collections.emptyList());

        // When: Parse page 999
        List<CrawlRecord<ProjectModel>> result = crawler.parsePage("999");

        // Then: Should return empty list
        assertThat(result).isEmpty();

        verify(apiService).fetchProjectsPage(999);
    }

    @Test
    void parsePage_WithMultipleProjects_ShouldReturnAllProjects() {
        // Given: Mock API service returns multiple projects
        List<CrawlRecord<ProjectModel>> multipleProjects = createMultipleCrawlRecords();
        when(apiService.fetchProjectsPage(1)).thenReturn(multipleProjects);

        // When: Parse page 1
        List<CrawlRecord<ProjectModel>> result = crawler.parsePage("1");

        // Then: Should return all projects
        assertThat(result).hasSize(3);
        assertThat(result.get(0).data().getTitle()).isEqualTo("Albero");
        assertThat(result.get(1).data().getTitle()).isEqualTo("The Watercrest");
        assertThat(result.get(2).data().getTitle()).isEqualTo("Anantara Mina");

        verify(apiService).fetchProjectsPage(1);
    }

    @Test
    void parsePage_WithInvalidPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for invalid page number
        assertThatThrownBy(() -> crawler.parsePage("invalid"))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: invalid")
            .hasCauseInstanceOf(NumberFormatException.class);

        // Should not call the service for invalid input
        verifyNoInteractions(apiService);
    }

    @Test
    void parsePage_WithNullPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for null page number
        assertThatThrownBy(() -> crawler.parsePage(null))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: null")
            .hasCauseInstanceOf(NumberFormatException.class);

        verifyNoInteractions(apiService);
    }

    @Test
    void parsePage_WithEmptyPageNumber_ShouldThrowNonRetryableException() {
        // When & Then: Should throw NonRetryableCrawlerException for empty page number
        assertThatThrownBy(() -> crawler.parsePage(""))
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("failed to parse page number: ")
            .hasCauseInstanceOf(NumberFormatException.class);

        verifyNoInteractions(apiService);
    }

    @Test
    void parsePage_WithNegativePageNumber_ShouldCallServiceWithNegativeNumber() {
        // Given: Mock API service can handle negative page numbers
        when(apiService.fetchProjectsPage(-1)).thenReturn(Collections.emptyList());

        // When: Parse negative page number
        List<CrawlRecord<ProjectModel>> result = crawler.parsePage("-1");

        // Then: Should pass negative number to service
        assertThat(result).isEmpty();
        verify(apiService).fetchProjectsPage(-1);
    }

    @Test
    void parsePage_WhenServiceThrowsRetryableException_ShouldPropagateException() {
        // Given: Mock API service throws RetryableCrawlerException
        when(apiService.fetchProjectsPage(anyInt()))
            .thenThrow(new RetryableCrawlerException("Network timeout"));

        // When & Then: Should propagate the exception
        assertThatThrownBy(() -> crawler.parsePage("1"))
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Network timeout");

        verify(apiService).fetchProjectsPage(1);
    }

    @Test
    void fetchAll_ShouldThrowNonRetryableException() {
        // When & Then: Should always throw NonRetryableCrawlerException
        assertThatThrownBy(() -> crawler.fetchAll())
            .isInstanceOf(NonRetryableCrawlerException.class)
            .hasMessage("fetching all entries is not supported");

        // Should not interact with the service
        verifyNoInteractions(apiService);
    }

    @Test
    void parsePage_WithLargePageNumber_ShouldHandleCorrectly() {
        // Given: Test with large page number that's still valid
        String largePageNumber = "999999";
        when(apiService.fetchProjectsPage(999999)).thenReturn(Collections.emptyList());

        // When: Parse large page number
        List<CrawlRecord<ProjectModel>> result = crawler.parsePage(largePageNumber);

        // Then: Should handle correctly
        assertThat(result).isEmpty();
        verify(apiService).fetchProjectsPage(999999);
    }

    @Test
    void parsePage_WithPageNumberZero_ShouldCallServiceWithZero() {
        // Given: Mock API service can handle page 0
        when(apiService.fetchProjectsPage(0)).thenReturn(Collections.emptyList());

        // When: Parse page 0
        List<CrawlRecord<ProjectModel>> result = crawler.parsePage("0");

        // Then: Should pass 0 to service
        assertThat(result).isEmpty();
        verify(apiService).fetchProjectsPage(0);
    }

    // Test to verify 404 handling during pagination discovery (integration-style test)
    @Test
    void integrationTest_WithMockedFeignClient_ShouldHandle404GracefullyDuringPaginationDiscovery() {
        // Given: Create a real crawler with real API service but mocked Feign client
        PropertyFinderApiService realApiService = new PropertyFinderApiService(feignRestClient);
        PropertyFinderProjectCrawler realCrawler = new PropertyFinderProjectCrawler(realApiService);
        
        // Mock 404 on page 1 (pagination discovery)
        when(feignRestClient.getNewProjects(1)).thenThrow(FeignException.NotFound.class);

        // When: Fetch page IDs
        List<String> pageIds = realCrawler.fetchAllPageIds();

        // Then: Should return empty list gracefully
        assertThat(pageIds).isEmpty();
        
        verify(feignRestClient).getNewProjects(1);
    }

    @Test
    void integrationTest_WithMockedFeignClient_ShouldProcessProjectsCorrectly() {
        // Given: Create a real crawler with real API service but mocked Feign client
        PropertyFinderApiService realApiService = new PropertyFinderApiService(feignRestClient);
        PropertyFinderProjectCrawler realCrawler = new PropertyFinderProjectCrawler(realApiService);
        
        // Mock successful responses
        when(feignRestClient.getNewProjects(1)).thenReturn(sampleSearchResultWrapper);
        when(feignRestClient.getProjectDetails(anyString(), anyString())).thenReturn(sampleProjectDetailsWrapper1);

        // When: Parse page 1
        List<CrawlRecord<ProjectModel>> result = realCrawler.parsePage("1");

        // Then: Should process projects and return valid ProjectModels
        assertThat(result).isNotEmpty();
        
        // Verify that the conversion produces valid ProjectModel objects
        for (CrawlRecord<ProjectModel> record : result) {
            ProjectModel project = record.data();
            assertThat(project.getSourceUrn()).isEqualTo("urn:source:propertyfinder:ae");
            assertThat(project.getProjectUrn()).isNotNull();
            assertThat(project.getDeveloperUrn()).isNotNull();
            assertThat(project.getTitle()).isNotNull();
        }
        
        // Verify metadata is properly set
        for (CrawlRecord<ProjectModel> record : result) {
            assertThat(record.metadata()).isNotNull();
            assertThat(record.source()).isEqualTo("urn:source:propertyfinder:ae");
            assertThat(record.urn()).isEqualTo(record.data().getProjectUrn());
        }
        
        verify(feignRestClient).getNewProjects(1);
        verify(feignRestClient, atLeastOnce()).getProjectDetails(anyString(), anyString());
    }

    @Test
    void integrationTest_WithMockedFeignClient_ShouldHandle404OnProjectDetails() {
        // Given: Create a real crawler with real API service but mocked Feign client
        PropertyFinderApiService realApiService = new PropertyFinderApiService(feignRestClient);
        PropertyFinderProjectCrawler realCrawler = new PropertyFinderProjectCrawler(realApiService);
        
        // Mock successful search results but 404 on project details
        when(feignRestClient.getNewProjects(1)).thenReturn(sampleSearchResultWrapper);
        when(feignRestClient.getProjectDetails(anyString(), anyString())).thenThrow(FeignException.NotFound.class);

        // When: Parse page 1
        List<CrawlRecord<ProjectModel>> result = realCrawler.parsePage("1");

        // Then: Should handle individual project failures gracefully and continue processing
        // The result may be empty if all projects fail, which is acceptable
        assertThat(result).isNotNull(); // Should not throw exception
        
        verify(feignRestClient).getNewProjects(1);
        verify(feignRestClient, atLeastOnce()).getProjectDetails(anyString(), anyString());
    }

    // Helper methods to create test data based on PropertyFinder fixtures

    private void loadTestFixtures() throws IOException {
        // Load new-projects.json fixture
        try (InputStream is = getClass().getResourceAsStream("/crawler/uae/propertyfinder/new-projects.json")) {
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            sampleSearchResultWrapper = mapper.readValue(json, mapper.getTypeFactory()
                .constructParametricType(NextJsWrapper.class, SearchResultPageProps.class));
        }

        // Load project-details.json fixture
        try (InputStream is = getClass().getResourceAsStream("/crawler/uae/propertyfinder/project-details.json")) {
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            sampleProjectDetailsWrapper1 = mapper.readValue(json, mapper.getTypeFactory()
                .constructParametricType(NextJsWrapper.class, ProjectDetailsPageProps.class));
        }

        // Load project-details-2.json fixture
        try (InputStream is = getClass().getResourceAsStream("/crawler/uae/propertyfinder/project-details-2.json")) {
            String json = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            sampleProjectDetailsWrapper2 = mapper.readValue(json, mapper.getTypeFactory()
                .constructParametricType(NextJsWrapper.class, ProjectDetailsPageProps.class));
        }
    }

    private CrawlRecord<ProjectModel> createSampleCrawlRecord() {
        ProjectModel sampleProject = ProjectModel.builder()
            .withTitle("Albero")
            .withProjectSlug("albero")
            .withExternalId("4778026c-90e6-435a-87eb-0017b2c5e6c1")
            .withProjectUrn("urn:source:propertyfinder:ae:developer:emaar-properties:project:albero")
            .withDeveloperUrn("urn:source:propertyfinder:ae:developer:emaar-properties")
            .withSourceUrn("urn:source:propertyfinder:ae")
            .withCurrency("AED")
            .withDescription("Welcome to Albero by Emaar Properties...")
            .build();

        Map<String, Object> metadata = Map.of(
            "conversion_timestamp", "2025-01-14T10:00:00Z",
            "source_api", "urn:source:propertyfinder:ae",
            "api_version", "v1",
            "raw_data", Map.of(
                "project_summary", Map.of(
                    "id", "4778026c-90e6-435a-87eb-0017b2c5e6c1",
                    "title", "Albero",
                    "shareUrl", "/en/new-projects/emaar-properties/albero"
                ),
                "project_detail", Map.of(
                    "id", "4778026c-90e6-435a-87eb-0017b2c5e6c1",
                    "slug", "emaar-properties/albero",
                    "title", "Albero",
                    "description", "Welcome to Albero by Emaar Properties..."
                )
            )
        );

        return new CrawlRecord<>(
            sampleProject,
            metadata,
            "urn:source:propertyfinder:ae",
            sampleProject.getProjectUrn()
        );
    }

    private List<CrawlRecord<ProjectModel>> createMultipleCrawlRecords() {
        // Create multiple projects based on PropertyFinder fixture data
        ProjectModel project1 = ProjectModel.builder()
            .withTitle("Albero")
            .withProjectSlug("albero")
            .withExternalId("4778026c-90e6-435a-87eb-0017b2c5e6c1")
            .withProjectUrn("urn:source:propertyfinder:ae:developer:emaar-properties:project:albero")
            .withDeveloperUrn("urn:source:propertyfinder:ae:developer:emaar-properties")
            .withSourceUrn("urn:source:propertyfinder:ae")
            .withCurrency("AED")
            .build();

        ProjectModel project2 = ProjectModel.builder()
            .withTitle("The Watercrest")
            .withProjectSlug("the-watercrest")
            .withExternalId("63bfe869-50e1-466a-8ce5-5d060e97c11e")
            .withProjectUrn("urn:source:propertyfinder:ae:developer:ellington:project:the-watercrest")
            .withDeveloperUrn("urn:source:propertyfinder:ae:developer:ellington")
            .withSourceUrn("urn:source:propertyfinder:ae")
            .withCurrency("AED")
            .build();

        ProjectModel project3 = ProjectModel.builder()
            .withTitle("Anantara Mina")
            .withProjectSlug("anantara-mina")
            .withExternalId("309e9eb1-147a-412e-b6e6-d42d3132d696")
            .withProjectUrn("urn:source:propertyfinder:ae:developer:rak-properties:project:anantara-mina")
            .withDeveloperUrn("urn:source:propertyfinder:ae:developer:rak-properties")
            .withSourceUrn("urn:source:propertyfinder:ae")
            .withCurrency("AED")
            .build();

        Map<String, Object> metadata = Map.of(
            "conversion_timestamp", "2025-01-14T10:00:00Z",
            "source_api", "urn:source:propertyfinder:ae",
            "api_version", "v1"
        );

        return List.of(
            new CrawlRecord<>(project1, metadata, "urn:source:propertyfinder:ae", project1.getProjectUrn()),
            new CrawlRecord<>(project2, metadata, "urn:source:propertyfinder:ae", project2.getProjectUrn()),
            new CrawlRecord<>(project3, metadata, "urn:source:propertyfinder:ae", project3.getProjectUrn())
        );
    }
} 