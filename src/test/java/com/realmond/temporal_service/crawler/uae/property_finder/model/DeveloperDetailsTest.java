package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies correct deserialisation of the Property-Finder developer-details payload.
 */
class DeveloperDetailsTest {

    private ObjectMapper mapper;
    private DeveloperDetails sut;

    @BeforeEach
    void setUp() throws Exception {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();

        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/propertyfinder/developer-details.json")) {
            assertThat(in).as("fixture stream").isNotNull();

            NextJsWrapper<DeveloperPageProps> root = mapper.readValue(
                    in,
                    mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DeveloperPageProps.class)
            );

            sut = root.getProps()
                    .getPageProps()
                    .getDevResult()
                    .getDeveloper();
        }
    }

    @Test
    void fields_shouldDeserializeCorrectly() {
        assertThat(sut).isNotNull();
        assertThat(sut.getId()).isEqualTo("8a509d77-0b04-4669-9a78-d9fd5cdc78da");
        assertThat(sut.getName()).isEqualTo("Sobha Realty");
        assertThat(sut.getDescription()).contains("Sobha Realty");
        assertThat(sut.getDevPageEnabled()).isTrue();
        assertThat(sut.getCoverImageUrl()).startsWith("https://");
        assertThat(sut.getLogoUrl()).startsWith("https://");

        assertThat(sut.getContactOptions()).isNotNull();
        assertThat(sut.getContactOptions().getEmail()).isTrue();
        assertThat(sut.getContactOptions().getWhatsapp()).startsWith("https://wa.me/");
    }
} 