package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;
import java.time.OffsetDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies correct deserialisation of the Property-Finder "developers" list
 * payload into the {@link DevelopersPageProps} POJO hierarchy.
 */
class DevelopersListTest {

    private ObjectMapper mapper;
    private DevelopersPageProps sut;

    @BeforeEach
    void setUp() throws Exception {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();

        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/propertyfinder/developers.json")) {
            assertThat(in).as("fixture stream").isNotNull();

            NextJsWrapper<DevelopersPageProps> root = mapper.readValue(
                    in,
                    mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, DevelopersPageProps.class)
            );

            sut = root.getProps()
                       .getPageProps();
        }
    }

    @Test
    void basic_structure_shouldDeserialize() {
        assertThat(sut).isNotNull();
        assertThat(sut.getDevelopersResult()).isNotNull();
        assertThat(sut.getDevelopersResult().getMeta()).isNotNull();
        assertThat(sut.getDevelopersResult().getMeta().getCount()).isEqualTo(434);
        assertThat(sut.getDevelopersResult().getMeta().getPagination().getPage()).isEqualTo(2);

        // developer list assertions
        List<DeveloperSummary> developers = sut.getDevelopersResult().getData();
        assertThat(developers).isNotEmpty();

        DeveloperSummary first = developers.get(0);
        assertThat(first.getId()).isEqualTo("0cc56eac-683f-4cf9-8807-d48321209f53");
        assertThat(first.getName()).isEqualTo("Alef Group");
        assertThat(first.getSlug()).isEqualTo("alef-group");
        assertThat(first.getDevPageEnabled()).isFalse();
        assertThat(first.getEstablishedSince()).isEqualTo(OffsetDateTime.parse("2013-03-14T00:00:00Z"));
        assertThat(first.getLogoUrl()).startsWith("https://");
        assertThat(first.getNumProjectsOnline()).isEqualTo(30);
    }
} 