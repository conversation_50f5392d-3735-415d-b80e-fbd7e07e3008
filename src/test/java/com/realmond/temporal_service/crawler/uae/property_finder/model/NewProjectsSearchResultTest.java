package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies correct deserialisation of the Property-Finder "new-projects" list
 * payload into the {@link SearchResultPageProps} POJO hierarchy.
 */
class NewProjectsSearchResultTest {

    private ObjectMapper mapper;
    private SearchResultPageProps sut;

    @BeforeEach
    void setUp() throws Exception {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        // Fixture contains a single trailing comma – relax the parser for tests.
        mapper.configure(JsonReadFeature.ALLOW_TRAILING_COMMA.mappedFeature(), true);

        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/propertyfinder/new-projects.json")) {
            assertThat(in).as("fixture stream").isNotNull();

            NextJsWrapper<SearchResultPageProps> root = mapper.readValue(
                    in,
                    mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, SearchResultPageProps.class)
            );

            sut = root.getProps()
                    .getPageProps();
        }
    }

    @Test
    void basic_structure_shouldDeserialize() {
        assertThat(sut).isNotNull();
        assertThat(sut.getSearchResult()).isNotNull();

        // meta assertions
        assertThat(sut.getSearchResult().getMeta()).isNotNull();
        assertThat(sut.getSearchResult().getMeta().getPagination().getPage()).isEqualTo(2);

        // project list assertions
        List<ProjectSummary> projects = sut.getSearchResult()
                                           .getData()
                                           .getProjects()
                                           .getDeveloper();
        assertThat(projects).isNotEmpty();

        ProjectSummary first = projects.get(0);
        assertThat(first.getId()).isEqualTo("4778026c-90e6-435a-87eb-0017b2c5e6c1");
        assertThat(first.getTitle()).isEqualTo("Albero");
        assertThat(first.getDeveloper()).isNotNull();
        assertThat(first.getDeveloper().getName()).isEqualTo("Emaar Properties");
        assertThat(first.getAmenities()).isNotEmpty();
    }
} 