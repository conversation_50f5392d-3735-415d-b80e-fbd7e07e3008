package com.realmond.temporal_service.crawler.uae.property_finder.model;

import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Verifies correct deserialisation of the Property-Finder "project-detail" payload
 * into the {@link ProjectDetails} POJO hierarchy for both sample fixtures.
 */
class ProjectDetailsTest {

    private ObjectMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        // Relax parsing rules a bit for potential trailing commas in the future.
        mapper.configure(JsonReadFeature.ALLOW_TRAILING_COMMA.mappedFeature(), true);
    }

    @Test
    void albero_fixture_shouldDeserialize() throws Exception {
        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/propertyfinder/project-details.json")) {
            assertThat(in).as("fixture stream").isNotNull();

            NextJsWrapper<ProjectDetailsPageProps> root = mapper.readValue(
                    in,
                    mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, ProjectDetailsPageProps.class)
            );

            ProjectDetails sut = root.getProps()
                                     .getPageProps()
                                     .getDetailResult();

            // basic assertions
            assertThat(sut).isNotNull();
            assertThat(sut.getId()).isEqualTo("4778026c-90e6-435a-87eb-0017b2c5e6c1");
            assertThat(sut.getTitle()).isEqualTo("Albero");
            assertThat(sut.getStartingPrice()).isEqualTo(1_820_000);

            // developer
            assertThat(sut.getDeveloper()).isNotNull();
            assertThat(sut.getDeveloper().getName()).isEqualTo("Emaar Properties");

            // amenities and gallery
            assertThat(sut.getAmenities()).isNotEmpty();
            assertThat(sut.getImages()).isNotEmpty();
        }
    }

    @Test
    void coralBeach_fixture_shouldDeserialize() throws Exception {
        try (InputStream in = getClass().getResourceAsStream("/crawler/uae/propertyfinder/project-details-2.json")) {
            assertThat(in).as("fixture stream").isNotNull();

            NextJsWrapper<ProjectDetailsPageProps> root = mapper.readValue(
                    in,
                    mapper.getTypeFactory().constructParametricType(NextJsWrapper.class, ProjectDetailsPageProps.class)
            );

            ProjectDetails sut = root.getProps()
                                     .getPageProps()
                                     .getDetailResult();

            // basic assertions
            assertThat(sut).isNotNull();
            assertThat(sut.getId()).isEqualTo("2ed8ea87-eea8-483b-9562-fb795d7d0144");
            assertThat(sut.getTitle()).isEqualTo("Coral Beach Villas");
            assertThat(sut.getStartingPrice()).isEqualTo(17_300_000);

            // developer
            assertThat(sut.getDeveloper()).isNotNull();
            assertThat(sut.getDeveloper().getName()).isEqualTo("Sobha Realty");

            // payment plans present in this fixture
            assertThat(sut.getPaymentPlans()).isNotEmpty();
            assertThat(sut.getPaymentPlans().get(0).getPhases()).isNotEmpty();

            // timeline
            assertThat(sut.getTimelinePhases()).isNotEmpty();
        }
    }
} 