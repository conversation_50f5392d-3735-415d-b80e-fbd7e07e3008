<!DOCTYPE html>
<html lang="en" style="--vh:1vh">
<head>
    <meta charSet="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=GTM-N9KDBF9"></script>
    <title>Alnair</title>
    <link rel="icon" type="image/png" href="/favicon.png"/>
    <link rel="modulepreload" href="/assets/entry.client-DzurGooA.js"/>
    <link rel="modulepreload" href="/assets/clientModeHttpHeaderManager-C9RmXsuh.js"/>
    <link rel="modulepreload" href="/assets/effector-B7BVxv2N.js"/>
    <link rel="modulepreload" href="/assets/toPropertyKey-B9UmubNl.js"/>
    <link rel="modulepreload" href="/assets/manager-Bjjz_weN.js"/>
    <link rel="modulepreload" href="/assets/index-DzhfzPRG.js"/>
    <link rel="modulepreload" href="/assets/debug-build-DaLUBJrk.js"/>
    <link rel="modulepreload" href="/assets/index-lhEm7YEp.js"/>
    <link rel="modulepreload" href="/assets/CustomI18nextProvider-DMrXB3fU.js"/>
    <link rel="modulepreload" href="/assets/root-DQ8p9z4E.js"/>
    <link rel="modulepreload" href="/assets/index-D1oHx4gy.js"/>
    <link rel="modulepreload" href="/assets/debounce-DN82-I86.js"/>
    <link rel="modulepreload" href="/assets/withReactQuery-D6b-IU1D.js"/>
    <link rel="modulepreload" href="/assets/index.es-CjrEKEV-.js"/>
    <link rel="modulepreload" href="/assets/should-revalidate-D-jMpSm3.js"/>
    <link rel="modulepreload" href="/assets/createApiServices-DxiPWx9N.js"/>
    <link rel="modulepreload" href="/assets/context-CS3l8pcR.js"/>
    <link rel="modulepreload" href="/assets/isTouchDevice-BtFX-jxZ.js"/>
    <link rel="modulepreload" href="/assets/GoogleReCaptchaContext-G8mI_5bn.js"/>
    <link rel="modulepreload" href="/assets/index-B3K6BV7f.js"/>
    <link rel="modulepreload" href="/assets/IsInCollectionProvider-Borts8ir.js"/>
    <link rel="modulepreload" href="/assets/IsInCollectionContext-BcTDvJVr.js"/>
    <link rel="modulepreload" href="/assets/index-Cv4VwrvW.js"/>
    <link rel="modulepreload" href="/assets/useWillUnmount-BTu1QOH9.js"/>
    <link rel="modulepreload" href="/assets/index-CuUA_3QR.js"/>
    <link rel="modulepreload" href="/assets/context-L8W4aFYA.js"/>
    <link rel="modulepreload" href="/assets/QueryClientProvider-Djt9oaZK.js"/>
    <link rel="modulepreload" href="/assets/index-9IwBu1qG.js"/>
    <link rel="modulepreload" href="/assets/app_layout-BtFaM-Fj.js"/>
    <link rel="modulepreload" href="/assets/RedirectToErrorPage-0laPYz9L.js"/>
    <link rel="modulepreload" href="/assets/index-BWsAfL3m.js"/>
    <link rel="modulepreload" href="/assets/import_modals-DPQGb7B1.js"/>
    <link rel="modulepreload" href="/assets/react-tooltip.min-D942YL5r.js"/>
    <link rel="modulepreload" href="/assets/index-DbxGA5hl.js"/>
    <link rel="modulepreload" href="/assets/useDidMount-SOSqJV7G.js"/>
    <link rel="modulepreload" href="/assets/useMainFilters-tzdomz5p.js"/>
    <link rel="modulepreload" href="/assets/analyticsContext-DpZV9T6i.js"/>
    <link rel="modulepreload" href="/assets/MeasureContext-BE9EF_L8.js"/>
    <link rel="modulepreload" href="/assets/MeasureStore-DrdzITP3.js"/>
    <link rel="modulepreload" href="/assets/FiltersContext-DDb5f0fR.js"/>
    <link rel="modulepreload" href="/assets/index-DK8mBXJ7.js"/>
    <link rel="modulepreload" href="/assets/CurrencyStore-BiP0EzQS.js"/>
    <link rel="modulepreload" href="/assets/converters-CTUY7UUw.js"/>
    <link rel="modulepreload" href="/assets/isString-CsdCDsnW.js"/>
    <link rel="modulepreload" href="/assets/omit-B9iVDUuQ.js"/>
    <link rel="modulepreload" href="/assets/omit-BoRzqOh4.js"/>
    <link rel="modulepreload" href="/assets/last-oQFn8hUT.js"/>
    <link rel="modulepreload" href="/assets/helpers-C3hq4x-L.js"/>
    <link rel="modulepreload" href="/assets/react-number-format.es-COYKVZWD.js"/>
    <link rel="modulepreload" href="/assets/index-D-ImV_kn.js"/>
    <link rel="modulepreload" href="/assets/isBefore-CfJADuSa.js"/>
    <link rel="modulepreload" href="/assets/useFilters-CKqTC2TL.js"/>
    <link rel="modulepreload" href="/assets/hooks-DIzN3zjD.js"/>
    <link rel="modulepreload" href="/assets/NumeralProvider-Bt8Hi53c.js"/>
    <link rel="modulepreload" href="/assets/useDidUpdate-DbzxUgDV.js"/>
    <link rel="modulepreload" href="/assets/index-BTxTMWWa.js"/>
    <link rel="modulepreload" href="/assets/index-CGTviU3j.js"/>
    <link rel="modulepreload" href="/assets/index-mcx6wl3H.js"/>
    <link rel="modulepreload" href="/assets/AuthProvider-DKGZlOPK.js"/>
    <link rel="modulepreload" href="/assets/hooks-B9wVXA-M.js"/>
    <link rel="modulepreload" href="/assets/helpers-JCefK0aN.js"/>
    <link rel="modulepreload" href="/assets/find-CCVKaEKR.js"/>
    <link rel="modulepreload" href="/assets/useCurrentUser-C6mZ7Kif.js"/>
    <link rel="modulepreload" href="/assets/useCurrentCity-Bs08G94U.js"/>
    <link rel="modulepreload" href="/assets/countries-CCFEiA38.js"/>
    <link rel="modulepreload" href="/assets/index-PyoBGbMz.js"/>
    <link rel="modulepreload" href="/assets/useMainFilterStore-BA4s8gCu.js"/>
    <link rel="modulepreload" href="/assets/styles.module-DTVS7n51.js"/>
    <link rel="modulepreload" href="/assets/DictionarySegmentControlField-DGRtIkDk.js"/>
    <link rel="modulepreload" href="/assets/index-kK9avhVY.js"/>
    <link rel="modulepreload" href="/assets/DropdownOption-BIYGm1xk.js"/>
    <link rel="modulepreload" href="/assets/DropdownOptionList-Dbhh1TEn.js"/>
    <link rel="modulepreload" href="/assets/free-mode-Cl4ytcXx.js"/>
    <link rel="modulepreload" href="/assets/hammer-CLPwbGmQ.js"/>
    <link rel="modulepreload" href="/assets/helpers-CUKRr88i.js"/>
    <link rel="modulepreload" href="/assets/useAgenciesListedByFilterSelectedOptionsStore-CmxGrZtC.js"/>
    <link rel="modulepreload" href="/assets/set-CSqHUXx4.js"/>
    <link rel="modulepreload" href="/assets/setMonth-N6JsmM3C.js"/>
    <link rel="modulepreload" href="/assets/flow-B_iWYD9z.js"/>
    <link rel="modulepreload" href="/assets/sortBy-DtaJkhn7.js"/>
    <link rel="modulepreload" href="/assets/hooks-Ur_KVb63.js"/>
    <link rel="modulepreload" href="/assets/ChipsComboboxControl-DodGjeki.js"/>
    <link rel="modulepreload" href="/assets/DictionaryCombobox-BPT-xhR9.js"/>
    <link rel="modulepreload" href="/assets/ComboboxOptionsList-CHPGXLQC.js"/>
    <link rel="modulepreload" href="/assets/index-Bl8rNFhg.js"/>
    <link rel="modulepreload" href="/assets/tslib.es6-mgWJUayD.js"/>
    <link rel="modulepreload" href="/assets/index-B1JdJRAd.js"/>
    <link rel="modulepreload" href="/assets/MobileDrawer-OyyiwXPt.js"/>
    <link rel="modulepreload" href="/assets/index-nggwapDP.js"/>
    <link rel="modulepreload" href="/assets/InputCurrency-Be-49gi_.js"/>
    <link rel="modulepreload" href="/assets/index-Cl0x-mvD.js"/>
    <link rel="modulepreload" href="/assets/ImWithClientProvider-DkxxfzWF.js"/>
    <link rel="modulepreload" href="/assets/useDebounceOnChange-CQ5yEfvK.js"/>
    <link rel="modulepreload" href="/assets/useDebouncedValue-KqD10P93.js"/>
    <link rel="modulepreload" href="/assets/PriceHint-Cx9bGpiT.js"/>
    <link rel="modulepreload" href="/assets/index-eSZKOShP.js"/>
    <link rel="modulepreload" href="/assets/ExclusiveLabel-uGyNOYgP.js"/>
    <link rel="modulepreload" href="/assets/index-Bpkc5PGW.js"/>
    <link rel="modulepreload" href="/assets/DropdownOptionColumnGroups-BHDORrmB.js"/>
    <link rel="modulepreload" href="/assets/SearchOnSiteCombobox-BpPJ5uLi.js"/>
    <link rel="modulepreload" href="/assets/ComboboxPopup-DzIUVGDr.js"/>
    <link rel="modulepreload" href="/assets/suggestService-CJ-q0Pdv.js"/>
    <link rel="modulepreload" href="/assets/utils-DWenTZcj.js"/>
    <link rel="modulepreload" href="/assets/useAsyncLoadComboboxOptions-CFq9I0IM.js"/>
    <link rel="modulepreload" href="/assets/constants-Bbh45y0w.js"/>
    <link rel="modulepreload" href="/assets/FiltersFooter-Ro1yJnEH.js"/>
    <link rel="modulepreload" href="/assets/SingleCombobox-BXQU1QtY.js"/>
    <link rel="modulepreload" href="/assets/ModelsInventorySaleType-D692humT.js"/>
    <link rel="modulepreload" href="/assets/head-Bh32IBBv.js"/>
    <link rel="modulepreload" href="/assets/useGoogleAnalytics-iXCouI2B.js"/>
    <link rel="modulepreload" href="/assets/Indicator-BCTcZ_qo.js"/>
    <link rel="modulepreload" href="/assets/SubscriptionManagementButton-MLzzhfOe.js"/>
    <link rel="modulepreload" href="/assets/useShowVerificationStatusPopup-DFdnjsGP.js"/>
    <link rel="modulepreload" href="/assets/compat-p3DJ-mhR.js"/>
    <link rel="modulepreload" href="/assets/addMilliseconds-DI2RJqCX.js"/>
    <link rel="modulepreload" href="/assets/isAfter-Ch9B7M-a.js"/>
    <link rel="modulepreload" href="/assets/VerificationProgressBar-0zKsugIi.js"/>
    <link rel="modulepreload" href="/assets/stopPropagation-DAtVfayL.js"/>
    <link rel="modulepreload" href="/assets/NewBadge-DZ8jXqRK.js"/>
    <link rel="modulepreload" href="/assets/AgencyProvider-B7ay0TiU.js"/>
    <link rel="modulepreload" href="/assets/index-BWSVCGi4.js"/>
    <link rel="modulepreload" href="/assets/toasts-DLTkdsps.js"/>
    <link rel="modulepreload" href="/assets/useQuery-DWrYcOWg.js"/>
    <link rel="modulepreload" href="/assets/useBaseQuery-DUGbP2Kb.js"/>
    <link rel="modulepreload" href="/assets/validation-C7tcXRWA.js"/>
    <link rel="modulepreload" href="/assets/BaseMapContainer-CvrlSu9u.js"/>
    <link rel="modulepreload" href="/assets/index-D2XAoqWc.js"/>
    <link rel="modulepreload" href="/assets/GoogleMapTileLayer-Cozx3goK.js"/>
    <link rel="modulepreload" href="/assets/reverse-i5ZcxzPe.js"/>
    <link rel="modulepreload" href="/assets/reverse-lqZiMTWN.js"/>
    <link rel="modulepreload" href="/assets/last-MvWub-5u.js"/>
    <link rel="modulepreload" href="/assets/uniq-B5OHIs0i.js"/>
    <link rel="modulepreload" href="/assets/_baseUniq-D_-AMbwg.js"/>
    <link rel="modulepreload" href="/assets/types-nlarRGfo.js"/>
    <link rel="modulepreload" href="/assets/index-DbE2djz8.js"/>
    <link rel="modulepreload" href="/assets/InvitesToAgency-Dg3kND8q.js"/>
    <link rel="modulepreload" href="/assets/ListItemNew-D7uUqXqm.js"/>
    <link rel="modulepreload" href="/assets/useCheckPermission-CkiEq5Il.js"/>
    <link rel="modulepreload" href="/assets/IconButton-NvUYdJN3.js"/>
    <link rel="modulepreload" href="/assets/Card-TPJzz58y.js"/>
    <link rel="modulepreload" href="/assets/index-BRM6HjI4.js"/>
    <link rel="modulepreload" href="/assets/useTariffGuard-DeDQ9Suh.js"/>
    <link rel="modulepreload" href="/assets/client-only-Dt6en-JV.js"/>
    <link rel="modulepreload" href="/assets/SimpleMobileDrawer-BB-WwdR7.js"/>
    <link rel="modulepreload" href="/assets/SiteSettings-Ce8IYV6m.js"/>
    <link rel="modulepreload" href="/assets/useMeasureOptionsManager-DePTqd2U.js"/>
    <link rel="modulepreload" href="/assets/LogoWithClient-BhA5CT2v.js"/>
    <link rel="modulepreload" href="/assets/Logo-BSXRLg9K.js"/>
    <link rel="modulepreload" href="/assets/flowRight-DhZel7Cq.js"/>
    <link rel="modulepreload" href="/assets/AccessControlProviderWrapper-GxDL5-L5.js"/>
    <link rel="modulepreload" href="/assets/utils-DynR-DyR.js"/>
    <link rel="modulepreload" href="/assets/context-DM7mM-1t.js"/>
    <link rel="modulepreload" href="/assets/index-3OLT3HBU.js"/>
    <link rel="modulepreload" href="/assets/search_for_projects_layout-k6Xed7VG.js"/>
    <link rel="modulepreload" href="/assets/useSendGaEventOnMount-DqKjSp_H.js"/>
    <link rel="modulepreload" href="/assets/bulkAddToCollectionStore-CsU7coXx.js"/>
    <link rel="modulepreload" href="/assets/currentActivePolygonStore-ez3Y9sXM.js"/>
    <link rel="modulepreload" href="/assets/BedroomsFilterCombobox-aepjA3xI.js"/>
    <link rel="modulepreload" href="/assets/SearchProjectsEffect-BULfUfD8.js"/>
    <link rel="modulepreload" href="/assets/DropdownPriceFilter-v1kk05Ne.js"/>
    <link rel="modulepreload" href="/assets/FilterButtonWithFilterCount-CuznGVYw.js"/>
    <link rel="modulepreload" href="/assets/InfoTooltip-D_0XCGFC.js"/>
    <link rel="modulepreload" href="/assets/MapLayerToggle-COWNPRZT.js"/>
    <link rel="modulepreload" href="/assets/index-CpSq0KHr.js"/>
    <link rel="modulepreload" href="/assets/index-ConoCbk8.js"/>
    <link rel="modulepreload" href="/assets/server.browser-BYcQV9Ub.js"/>
    <link rel="modulepreload" href="/assets/FlagIcon-B9JW5tLz.js"/>
    <link rel="modulepreload" href="/assets/utils-CAeOQ-Tx.js"/>
    <link rel="modulepreload" href="/assets/utils-7zCAlAqF.js"/>
    <link rel="modulepreload" href="/assets/getPathOptionsForPolygon-Ck_FHalz.js"/>
    <link rel="modulepreload" href="/assets/ShowOnlyWhenImNotWithClient-CK99e40A.js"/>
    <link rel="modulepreload" href="/assets/useNewsQuery-V2lH7Duk.js"/>
    <link rel="modulepreload" href="/assets/SliderNavigationControl-sC_msmFi.js"/>
    <link rel="modulepreload" href="/assets/useMediaQuery-BvndBfCI.js"/>
    <link rel="modulepreload" href="/assets/index-CxfOSz5v.js"/>
    <link rel="modulepreload" href="/assets/ImagePlaceholder-BKiX4mws.js"/>
    <link rel="modulepreload" href="/assets/AgencyCell-B8T15WJf.js"/>
    <link rel="modulepreload" href="/assets/helpers-DdY64bOu.js"/>
    <link rel="modulepreload" href="/assets/SimpleDropdownSort-C8EWwsey.js"/>
    <link rel="modulepreload" href="/assets/ProjectsNotFound-whREnr87.js"/>
    <link rel="modulepreload" href="/assets/index-DdYzqrcu.js"/>
    <link rel="modulepreload" href="/assets/delay-DwP2GHX9.js"/>
    <link rel="modulepreload" href="/assets/core-C0wgD8BO.js"/>
    <link rel="modulepreload" href="/assets/useManualMapTooltipState-CkO7UkoB.js"/>
    <link rel="modulepreload" href="/assets/helpers-Io_Pmx5I.js"/>
    <link rel="modulepreload" href="/assets/map--SAUJ3_C.js"/>
    <link rel="modulepreload" href="/assets/helpers-Bb0OYOCE.js"/>
    <link rel="modulepreload" href="/assets/helpers-TP9ZegV6.js"/>
    <link rel="modulepreload" href="/assets/isEqual-j_RfxUxk.js"/>
    <link rel="modulepreload" href="/assets/Models_Project-PiukaWZu.js"/>
    <link rel="modulepreload" href="/assets/context-D-45luS0.js"/>
    <link rel="modulepreload" href="/assets/ProjectCard-DxRWvxW7.js"/>
    <link rel="modulepreload" href="/assets/index-omaZuc-a.js"/>
    <link rel="modulepreload" href="/assets/ShowOnlyToAuthUsers-C9mgaFdC.js"/>
    <link rel="modulepreload" href="/assets/AddToBookmarksButton-csorYAjM.js"/>
    <link rel="modulepreload" href="/assets/ProjectCardSkeleton-B4BoDR21.js"/>
    <link rel="modulepreload" href="/assets/DateFormat-COf59ysS.js"/>
    <link rel="modulepreload" href="/assets/CircleProgress-Dlf_hvA1.js"/>
    <link rel="modulepreload" href="/assets/Models_GetUserBookmarksItem-C9eLmA1J.js"/>
    <link rel="modulepreload" href="/assets/createDistrictQueryKeys-D8-Ml7Mm.js"/>
    <link rel="modulepreload" href="/assets/useInfiniteQuery-GuWJly7h.js"/>
    <link rel="modulepreload" href="/assets/newsQueryKeys-DdMnl9Fx.js"/>
    <link rel="modulepreload" href="/assets/index-DeqjcgkY.js"/>
    <link rel="modulepreload" href="/assets/DropdownControlTransparent-BNRzYjkE.js"/>
    <link rel="modulepreload" href="/assets/EmptyData-Cr5ICnWe.js"/>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'GTM-N9KDBF9', {
            page_path: window.location.pathname,
        });
    </script>
    <link rel="stylesheet" href="/assets/root-DL7HVTlL.css"/>
    <link rel="stylesheet" href="/fonts/fonts.css?v=3"/>
    <link rel="stylesheet" href="/style/reset.css"/>
    <link rel="stylesheet" href="/style/react-day-picker.css"/>
    <link rel="stylesheet" href="/style/react-tooltip.css "/>
    <link rel="stylesheet" href="/style/swipe.css"/>
    <link rel="stylesheet" href="/style/simplebar.css"/>
    <link rel="stylesheet" href="/assets/variables-CoDMbc7T.css"/>
    <link rel="stylesheet" href="/assets/index-BzeEnF-4.css"/>
    <link rel="stylesheet" href="/assets/react-tooltip-C1RxmEJd.css"/>
    <link rel="stylesheet" href="/assets/index-CIGspkUj.css"/>
    <link rel="stylesheet" href="/assets/index-_o2ZaHar.css"/>
    <link rel="stylesheet" href="/assets/useMainFilters-CzF5A1Ty.css"/>
    <link rel="stylesheet" href="/assets/index-B2bRXHm6.css"/>
    <link rel="stylesheet" href="/assets/index-DTi9ota9.css"/>
    <link rel="stylesheet" href="/assets/styles-BRZoLynU.css"/>
    <link rel="stylesheet" href="/assets/DropdownOption-B87qHNXs.css"/>
    <link rel="stylesheet" href="/assets/DropdownOptionList-CgjJxN1R.css"/>
    <link rel="stylesheet" href="/assets/useAgenciesListedByFilterSelectedOptionsStore-BFLhgIrR.css"/>
    <link rel="stylesheet" href="/assets/ChipsComboboxControl-TL0OA0t7.css"/>
    <link rel="stylesheet" href="/assets/DictionaryCombobox-CmQXHYEg.css"/>
    <link rel="stylesheet" href="/assets/ComboboxOptionsList-DujX5L_x.css"/>
    <link rel="stylesheet" href="/assets/MobileDrawer-C_J-abo6.css"/>
    <link rel="stylesheet" href="/assets/PriceHint-BMaGYChV.css"/>
    <link rel="stylesheet" href="/assets/index-TSWRHw_o.css"/>
    <link rel="stylesheet" href="/assets/index-rRMqGaHk.css"/>
    <link rel="stylesheet" href="/assets/DropdownOptionColumnGroups-LMyHkDhW.css"/>
    <link rel="stylesheet" href="/assets/ComboboxPopup-C2ybbEwS.css"/>
    <link rel="stylesheet" href="/assets/FiltersFooter-DKQaQeh4.css"/>
    <link rel="stylesheet" href="/assets/SingleCombobox-CZxUUPSQ.css"/>
    <link rel="stylesheet" href="/assets/Indicator-BbtvRwe1.css"/>
    <link rel="stylesheet" href="/assets/useShowVerificationStatusPopup-D1GWmdof.css"/>
    <link rel="stylesheet" href="/assets/VerificationProgressBar-Bzfh_we5.css"/>
    <link rel="stylesheet" href="/assets/NewBadge-dfLcfMDU.css"/>
    <link rel="stylesheet" href="/assets/validation-CdGmaXDe.css"/>
    <link rel="stylesheet" href="/assets/BaseMapContainer-DQK-U8lT.css"/>
    <link rel="stylesheet" href="/assets/index-DHG3qMD-.css"/>
    <link rel="stylesheet" href="/assets/InvitesToAgency-BDYmJQwj.css"/>
    <link rel="stylesheet" href="/assets/ListItemNew-DXRuQg_s.css"/>
    <link rel="stylesheet" href="/assets/IconButton-D5PtH23T.css"/>
    <link rel="stylesheet" href="/assets/Card-B5ADMC51.css"/>
    <link rel="stylesheet" href="/assets/index-Bn_Xn9bw.css"/>
    <link rel="stylesheet" href="/assets/SimpleMobileDrawer-D1_yjUud.css"/>
    <link rel="stylesheet" href="/assets/SiteSettings-D-2opAWi.css"/>
    <link rel="stylesheet" href="/assets/useMeasureOptionsManager-fZKK9FmS.css"/>
    <link rel="stylesheet" href="/assets/LogoWithClient-Djff4t1X.css"/>
    <link rel="stylesheet" href="/assets/index-Dzts6YcB.css"/>
    <link rel="stylesheet" href="/assets/search_for_projects_layout-i4G4gYml.css"/>
    <link rel="stylesheet" href="/assets/SearchProjectsEffect-BfjfcrcG.css"/>
    <link rel="stylesheet" href="/assets/core-C3r3wMeK.css"/>
    <link rel="stylesheet" href="/assets/ProjectCard-T7cs6O27.css"/>
    <link rel="stylesheet" href="/assets/index-Du6_scQs.css"/>
    <link rel="stylesheet" href="/assets/index-B0YlClZO.css"/>
    <link rel="stylesheet" href="/assets/AddToBookmarksButton-BlBsuTcS.css"/>
    <link rel="stylesheet" href="/assets/ProjectCardSkeleton-D0YtKkhO.css"/>
    <link rel="stylesheet" href="/assets/CircleProgress-BZIBsSJw.css"/>
    <link rel="stylesheet" href="/assets/DropdownPriceFilter-YQmoyK1O.css"/>
    <link rel="stylesheet" href="/assets/InfoTooltip-Dh2NYlxu.css"/>
    <link rel="stylesheet" href="/assets/MapLayerToggle-BV_AfXiy.css"/>
    <link rel="stylesheet" href="/assets/SliderNavigationControl-Ch4_5q_3.css"/>
    <link rel="stylesheet" href="/assets/ImagePlaceholder-qxZDBb_E.css"/>
    <link rel="stylesheet" href="/assets/DropdownControlTransparent-DKkwDImu.css"/>
    <link rel="stylesheet" href="/assets/EmptyData-B1M-ihk7.css"/>
    <style id="google-recaptcha-hidden-badge">
        .grecaptcha-badge {
            display: none !important;
        }
    </style>
    <style id="ssr_mediaStyles" type="text/css">
        .fresnel-container {
            margin: 0;
            padding: 0;
        }

        @media not all and (min-width: 0px) and (max-width:767.98px) {
            .fresnel-at-sm {
                display:none!important;
            }
        }

        @media not all and (min-width: 768px) and (max-width:1023.98px) {
            .fresnel-at-md {
                display:none!important;
            }
        }

        @media not all and (min-width: 1024px) and (max-width:1279.98px) {
            .fresnel-at-lg {
                display:none!important;
            }
        }

        @media not all and (min-width: 1280px) {
            .fresnel-at-xl {
                display:none!important;
            }
        }

        @media not all and (max-width: 767.98px) {
            .fresnel-lessThan-md {
                display:none!important;
            }
        }

        @media not all and (max-width: 1023.98px) {
            .fresnel-lessThan-lg {
                display:none!important;
            }
        }

        @media not all and (max-width: 1279.98px) {
            .fresnel-lessThan-xl {
                display:none!important;
            }
        }

        @media not all and (min-width: 768px) {
            .fresnel-greaterThan-sm {
                display:none!important;
            }
        }

        @media not all and (min-width: 1024px) {
            .fresnel-greaterThan-md {
                display:none!important;
            }
        }

        @media not all and (min-width: 1280px) {
            .fresnel-greaterThan-lg {
                display:none!important;
            }
        }

        @media not all and (min-width: 0px) {
            .fresnel-greaterThanOrEqual-sm {
                display:none!important;
            }
        }

        @media not all and (min-width: 768px) {
            .fresnel-greaterThanOrEqual-md {
                display:none!important;
            }
        }

        @media not all and (min-width: 1024px) {
            .fresnel-greaterThanOrEqual-lg {
                display:none!important;
            }
        }

        @media not all and (min-width: 1280px) {
            .fresnel-greaterThanOrEqual-xl {
                display:none!important;
            }
        }

        @media not all and (min-width: 0px) and (max-width:767.98px) {
            .fresnel-between-sm-md {
                display:none!important;
            }
        }

        @media not all and (min-width: 0px) and (max-width:1023.98px) {
            .fresnel-between-sm-lg {
                display:none!important;
            }
        }

        @media not all and (min-width: 0px) and (max-width:1279.98px) {
            .fresnel-between-sm-xl {
                display:none!important;
            }
        }

        @media not all and (min-width: 768px) and (max-width:1023.98px) {
            .fresnel-between-md-lg {
                display:none!important;
            }
        }

        @media not all and (min-width: 768px) and (max-width:1279.98px) {
            .fresnel-between-md-xl {
                display:none!important;
            }
        }

        @media not all and (min-width: 1024px) and (max-width:1279.98px) {
            .fresnel-between-lg-xl {
                display:none!important;
            }
        }
    </style>
    <script id="__i18NextInitNamespaces">
        window.__i18NextInitNamespaces = ["album", "auth", "billing", "bookUnit", "constants", "countryNames", "errorsPage", "featureAnnouncement", "feedbackForm", "filter", "footer", "forAgenciesPage", "header", "inventory", "landingPage", "layoutPage", "map", "paymentPlan", "profile", "project", "projectCard", "promotion", "salesOffices", "search", "selection", "selectionUnit", "sort", "transactions", "ui", "unit", "validation", "zod"]
    </script>
    <script src="https://www.google.com/recaptcha/api.js?hl=EN&amp;render=6LeXhjcpAAAAALESFYYVM1C0sDop8X-3hfLt4G7l" id="my-google-recaptcha-v3-script"></script>
</head>
<body>
<div id="GlobalTopLoadingBar" class="_container_eilx5_12" aria-hidden="true">
    <div class="_bar_eilx5_25 _bar_idle_eilx5_42"></div>
</div>
<div class="_root_15c7h_1">
    <div class="_navbarWrapper_15c7h_16">
        <header class="_desktopWrapper_1yfi7_1">
            <div>
                <div class="_wrapper_hhvc5_1">
                    <div class="_navLink_hhvc5_18 _navLinkWithSubgroup_hhvc5_27">
                        <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 font-bold" type="button">
                            <span class="_content_1y0c0_118">About</span>
                        </a>
                        <div class="_dropdownMenuRoot_hhvc5_27">
                            <div class="_stack_1xc3q_1 _stack_hhvc5_63 _gap16_1xc3q_41">
                                <span class="_root_3tp0y_1 _sizeM_3tp0y_20 _weight_bold_3tp0y_89">Knowledge Base</span>
                                <div class="_stack_1xc3q_1 _gap8_1xc3q_21">
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="https://about.alnair.ae/search-tools" target="_blank">
                                        <span class="_content_1y0c0_118">Search tools</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="https://about.alnair.ae/sales-selections" target="_blank">
                                        <span class="_content_1y0c0_118">Sales selections</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="https://about.alnair.ae/power_up_your_website" target="_blank">
                                        <span class="_content_1y0c0_118">Feeds and integration</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="https://about.alnair.ae/booking_90" target="_blank">
                                        <span class="_content_1y0c0_118">Booking</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="https://about.alnair.ae/agency_users" target="_blank">
                                        <span class="_content_1y0c0_118">Creating an Agency and Managing Users</span>
                                    </a>
                                </div>
                            </div>
                            <div class="_stack_1xc3q_1 _stack_hhvc5_63 _gap16_1xc3q_41">
                                <span class="_root_3tp0y_1 _sizeM_3tp0y_20 _weight_bold_3tp0y_89">Services</span>
                                <div class="_stack_1xc3q_1 _gap8_1xc3q_21">
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="/" data-discover="true">
                                        <span class="_content_1y0c0_118">All about us</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="/app/for-agencies" data-discover="true">
                                        <span class="_content_1y0c0_118">For Agencies</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="https://about.alnair.ae/for_developers" target="_blank">
                                        <span class="_content_1y0c0_118">For Developers</span>
                                    </a>
                                </div>
                            </div>
                            <div class="_stack_1xc3q_1 _stack_hhvc5_63 _gap16_1xc3q_41">
                                <span class="_root_3tp0y_1 _sizeM_3tp0y_20 _weight_bold_3tp0y_89">Terms &amp;Conditions</span>
                                <div class="_stack_1xc3q_1 _gap8_1xc3q_21">
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="/app/terms-of-use" data-discover="true">
                                        <span class="_content_1y0c0_118">Terms of use</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="/app/privacy" data-discover="true">
                                        <span class="_content_1y0c0_118">Privacy policy</span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="/app/cookie-policy" data-discover="true">
                                        <span class="_content_1y0c0_118">Cookie policy </span>
                                    </a>
                                    <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19 !font-normal" href="/app/contacts" data-discover="true">
                                        <span class="_content_1y0c0_118">Contacts</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="_navLink_hhvc5_18">
                        <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19" href="/app/developers" data-discover="true">
                            <span class="_content_1y0c0_118">Developers</span>
                        </a>
                    </div>
                    <div class="_navLink_hhvc5_18">
                        <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19" href="/app/districts" data-discover="true">
                            <span class="_content_1y0c0_118">Districts</span>
                        </a>
                    </div>
                    <div class="_navLink_hhvc5_18">
                        <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19" href="/app/bulk_offers" data-discover="true">
                            <span class="_content_1y0c0_118">Bulk Offers</span>
                        </a>
                    </div>
                    <div class="_navLink_hhvc5_18">
                        <a class="_root_1y0c0_1 _medium_1y0c0_95 _secondary_1y0c0_32 _align_center_1y0c0_19" href="/app/news" data-discover="true">
                            <span class="_content_1y0c0_118">News</span>
                        </a>
                    </div>
                    <div class="_navLink_hhvc5_18"></div>
                    <div class="_navLink_hhvc5_18"></div>
                </div>
            </div>
            <div class="_logo_wrapper_1yfi7_34">
                <a class="_logo_1raem_1" href="/app" data-discover="true">
                    <svg preserveAspectRatio="xMaxYMid meet" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 81 18" height="18" width="81" fill="none">
                        <path d="M29.89 5.137v11.376h-2.384v-1.308c-1.04 1.086-2.416 1.629-4.128 1.629-1.545 0-2.875-.558-3.991-1.675-1.1-1.13-1.651-2.576-1.651-4.334 0-1.743.558-3.18 1.674-4.312 1.116-1.131 2.439-1.697 3.968-1.697 1.712 0 3.088.543 4.128 1.629V5.137h2.385Zm-6.123 9.587c.994 0 1.866-.36 2.615-1.078.75-.734 1.124-1.674 1.124-2.821 0-1.147-.375-2.08-1.124-2.798-.75-.734-1.62-1.1-2.615-1.1-1.07 0-1.957.358-2.66 1.077-.703.703-1.055 1.644-1.055 2.821 0 1.177.352 2.125 1.055 2.844.703.703 1.59 1.055 2.66 1.055ZM35.431 16.513h-2.385V0h2.385v16.513ZM44.92 4.816c1.36 0 2.461.451 3.302 1.353.841.887 1.261 2.041 1.261 3.463v6.88h-2.408v-6.398c0-.933-.252-1.682-.756-2.248-.49-.565-1.14-.848-1.95-.848-1.055 0-1.888.36-2.5 1.078-.596.718-.894 1.773-.894 3.165v5.252H38.59V5.137h2.385v1.4c.963-1.147 2.278-1.72 3.945-1.72ZM63.476 5.137v11.376h-2.385v-1.308c-1.04 1.086-2.416 1.629-4.128 1.629-1.544 0-2.875-.558-3.99-1.675-1.102-1.13-1.652-2.576-1.652-4.334 0-1.743.558-3.18 1.674-4.312 1.116-1.131 2.439-1.697 3.968-1.697 1.712 0 3.088.543 4.128 1.629V5.137h2.385Zm-6.123 9.587c.993 0 1.865-.36 2.614-1.078.75-.734 1.124-1.674 1.124-2.821 0-1.147-.375-2.08-1.124-2.798-.75-.734-1.62-1.1-2.614-1.1-1.07 0-1.957.358-2.66 1.077-.704.703-1.056 1.644-1.056 2.821 0 1.177.352 2.125 1.055 2.844.704.703 1.59 1.055 2.66 1.055ZM68.007.23c.428 0 .788.145 1.078.435.29.29.436.642.436 1.055 0 .428-.145.78-.436 1.055-.275.275-.634.413-1.078.413-.443 0-.81-.138-1.1-.413-.291-.275-.436-.627-.436-1.055 0-.413.145-.764.435-1.055.291-.29.658-.436 1.101-.436ZM69.2 16.512h-2.385V5.137H69.2v11.376ZM79.253 5c.336 0 .604.03.803.091l-.16 2.294a6.343 6.343 0 0 0-.666-.023c-1.407 0-2.477.459-3.21 1.376-.735.902-1.102 2.156-1.102 3.761v4.014h-2.385V5.137h2.385v2.431C75.958 5.856 77.403 5 79.253 5Z" fill="#12122D"></path>
                        <path d="M6.454 14.802 7.437 18l.982-3.198c.246-.8.659-1.491 1.213-2.045a4.897 4.897 0 0 1 2.043-1.21l3.199-.983-3.199-.983A4.88 4.88 0 0 1 9.63 8.368a4.895 4.895 0 0 1-1.21-2.043l-.983-3.199-.983 3.199a4.897 4.897 0 0 1-1.21 2.045 4.894 4.894 0 0 1-2.045 1.21L0 10.564l3.199.983c.792.244 1.476.65 2.035 1.201.56.56.974 1.252 1.22 2.055Z" fill="#4F5FD9"></path>
                    </svg>
                </a>
            </div>
            <div>
                <div class="_wrapper_1o0uv_1">
                    <button class="_root_1a3h2_1 _medium_1a3h2_16 _labelMediumSize_1a3h2_46 _secondary_17qz6_13" data-size="medium">
                        <div class="_content_1a3h2_30">
                            <div class="_label_1a3h2_39">
                                <div class="_root_1tcmy_1">
                                    <img class="_flag_item_1gkyc_1" alt="flag" src="data:image/svg+xml,%3csvg%20xmlns=&#x27;http://www.w3.org/2000/svg&#x27;%20id=&#x27;flag-icons-gb&#x27;%20viewBox=&#x27;0%200%20640%20480&#x27;%3e%3cpath%20fill=&#x27;%23012169&#x27;%20d=&#x27;M0%200h640v480H0z&#x27;/%3e%3cpath%20fill=&#x27;%23FFF&#x27;%20d=&#x27;m75%200%20244%20181L562%200h78v62L400%20241l240%20178v61h-80L320%20301%2081%20480H0v-60l239-178L0%2064V0h75z&#x27;/%3e%3cpath%20fill=&#x27;%23C8102E&#x27;%20d=&#x27;m424%20281%20216%20159v40L369%20281h55zm-184%2020%206%2035L54%20480H0l240-179zM640%200v3L391%20191l2-44L590%200h50zM0%200l239%20176h-60L0%2042V0z&#x27;/%3e%3cpath%20fill=&#x27;%23FFF&#x27;%20d=&#x27;M241%200v480h160V0H241zM0%20160v160h640V160H0z&#x27;/%3e%3cpath%20fill=&#x27;%23C8102E&#x27;%20d=&#x27;M0%20193v96h640v-96H0zM273%200v480h96V0h-96z&#x27;/%3e%3c/svg%3e"/>
                                    <span class="_dash_1tcmy_22">/</span>
                                    <span>AED</span>
                                    <span class="_dash_1tcmy_22">/</span>
                                    <span>m²</span>
                                </div>
                            </div>
                        </div>
                    </button>
                    <a data-size="medium" class="_root_1a3h2_1 _medium_1a3h2_16 _labelMediumSize_1a3h2_46 _primary_17qz6_1" href="/login/email" data-discover="true">
                        <div class="_content_1a3h2_30">
                            <div class="_label_1a3h2_39">Sign in or Sign up</div>
                        </div>
                    </a>
                </div>
            </div>
        </header>
        <div class="_root_1km7n_11">
            <header class="_content_1km7n_1">
                <button class="_root_1y0c0_1 _small_1y0c0_89 _secondary_1y0c0_32 _align_center_1y0c0_19" type="button">
                            <span class="_content_1y0c0_118">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" class="" height="24px" width="24px" color="currentColor">
                                    <path d="M7.736 12.799a6.36 6.36 0 0 0 2.648-.576l2.346 3.325a1.066 1.066 0 1 0 1.744-1.23l-2.322-3.292a6.399 6.399 0 1 0-4.415 1.773Zm0-10.665a4.266 4.266 0 1 1 0 8.531 4.266 4.266 0 0 1 0-8.531Z" fill="currentColor"></path>
                                </svg>
                            </span>
                </button>
                <a class="_logo_1raem_1" href="/app" data-discover="true">
                    <svg preserveAspectRatio="xMaxYMid meet" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 81 18" height="18" width="81" fill="none">
                        <path d="M29.89 5.137v11.376h-2.384v-1.308c-1.04 1.086-2.416 1.629-4.128 1.629-1.545 0-2.875-.558-3.991-1.675-1.1-1.13-1.651-2.576-1.651-4.334 0-1.743.558-3.18 1.674-4.312 1.116-1.131 2.439-1.697 3.968-1.697 1.712 0 3.088.543 4.128 1.629V5.137h2.385Zm-6.123 9.587c.994 0 1.866-.36 2.615-1.078.75-.734 1.124-1.674 1.124-2.821 0-1.147-.375-2.08-1.124-2.798-.75-.734-1.62-1.1-2.615-1.1-1.07 0-1.957.358-2.66 1.077-.703.703-1.055 1.644-1.055 2.821 0 1.177.352 2.125 1.055 2.844.703.703 1.59 1.055 2.66 1.055ZM35.431 16.513h-2.385V0h2.385v16.513ZM44.92 4.816c1.36 0 2.461.451 3.302 1.353.841.887 1.261 2.041 1.261 3.463v6.88h-2.408v-6.398c0-.933-.252-1.682-.756-2.248-.49-.565-1.14-.848-1.95-.848-1.055 0-1.888.36-2.5 1.078-.596.718-.894 1.773-.894 3.165v5.252H38.59V5.137h2.385v1.4c.963-1.147 2.278-1.72 3.945-1.72ZM63.476 5.137v11.376h-2.385v-1.308c-1.04 1.086-2.416 1.629-4.128 1.629-1.544 0-2.875-.558-3.99-1.675-1.102-1.13-1.652-2.576-1.652-4.334 0-1.743.558-3.18 1.674-4.312 1.116-1.131 2.439-1.697 3.968-1.697 1.712 0 3.088.543 4.128 1.629V5.137h2.385Zm-6.123 9.587c.993 0 1.865-.36 2.614-1.078.75-.734 1.124-1.674 1.124-2.821 0-1.147-.375-2.08-1.124-2.798-.75-.734-1.62-1.1-2.614-1.1-1.07 0-1.957.358-2.66 1.077-.704.703-1.056 1.644-1.056 2.821 0 1.177.352 2.125 1.055 2.844.704.703 1.59 1.055 2.66 1.055ZM68.007.23c.428 0 .788.145 1.078.435.29.29.436.642.436 1.055 0 .428-.145.78-.436 1.055-.275.275-.634.413-1.078.413-.443 0-.81-.138-1.1-.413-.291-.275-.436-.627-.436-1.055 0-.413.145-.764.435-1.055.291-.29.658-.436 1.101-.436ZM69.2 16.512h-2.385V5.137H69.2v11.376ZM79.253 5c.336 0 .604.03.803.091l-.16 2.294a6.343 6.343 0 0 0-.666-.023c-1.407 0-2.477.459-3.21 1.376-.735.902-1.102 2.156-1.102 3.761v4.014h-2.385V5.137h2.385v2.431C75.958 5.856 77.403 5 79.253 5Z" fill="#12122D"></path>
                        <path d="M6.454 14.802 7.437 18l.982-3.198c.246-.8.659-1.491 1.213-2.045a4.897 4.897 0 0 1 2.043-1.21l3.199-.983-3.199-.983A4.88 4.88 0 0 1 9.63 8.368a4.895 4.895 0 0 1-1.21-2.043l-.983-3.199-.983 3.199a4.897 4.897 0 0 1-1.21 2.045 4.894 4.894 0 0 1-2.045 1.21L0 10.564l3.199.983c.792.244 1.476.65 2.035 1.201.56.56.974 1.252 1.22 2.055Z" fill="#4F5FD9"></path>
                    </svg>
                </a>
                <button class="_root_1y0c0_1 _small_1y0c0_89 _secondary_1y0c0_32 _align_center_1y0c0_19" type="button">
                            <span class="_content_1y0c0_118">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" class="" height="24px" width="24px" color="currentColor">
                                    <path d="M14 8a.906.906 0 0 0-.251-.628.842.842 0 0 0-.606-.26H2.857a.842.842 0 0 0-.606.26A.906.906 0 0 0 2 8c0 .236.09.462.251.629.16.167.379.26.606.26h10.286a.842.842 0 0 0 .606-.26A.906.906 0 0 0 14 8ZM2.857 11.223a.842.842 0 0 0-.606.26.906.906 0 0 0-.251.628c0 .236.09.462.251.629.16.167.379.26.606.26h10.286a.842.842 0 0 0 .606-.26.906.906 0 0 0 .251-.629.906.906 0 0 0-.251-.628.842.842 0 0 0-.606-.26H2.857ZM2 3.89c0 .235.09.461.251.628.16.166.379.26.606.26h10.286a.842.842 0 0 0 .606-.26.906.906 0 0 0 .251-.629.906.906 0 0 0-.251-.628.842.842 0 0 0-.606-.26H2.857a.842.842 0 0 0-.606.26.906.906 0 0 0-.251.628Z" fill="currentColor"></path>
                                </svg>
                            </span>
                </button>
            </header>
        </div>
    </div>
    <div class="_content_15c7h_30"></div>
</div>
<script>
    ( (storageKey2, restoreKey) => {
            if (!window.history.state || !window.history.state.key) {
                let key = Math.random().toString(32).slice(2);
                window.history.replaceState({
                    key
                }, "");
            }
            try {
                let positions = JSON.parse(sessionStorage.getItem(storageKey2) || "{}");
                let storedY = positions[restoreKey || window.history.state.key];
                if (typeof storedY === "number") {
                    window.scrollTo(0, storedY);
                }
            } catch (error) {
                console.error(error);
                sessionStorage.removeItem(storageKey2);
            }
        }
    )("react-router-scroll-positions", null)
</script>
<script>
    window.__reactRouterContext = {
        "basename": "/",
        "future": {
            "unstable_middleware": false,
            "unstable_optimizeDeps": true,
            "unstable_splitRouteModules": true,
            "unstable_subResourceIntegrity": false,
            "unstable_viteEnvironmentApi": true
        },
        "routeDiscovery": {
            "mode": "lazy",
            "manifestPath": "/__manifest"
        },
        "ssr": true,
        "isSpaMode": false
    };
    window.__reactRouterContext.stream = new ReadableStream({
        start(controller) {
            window.__reactRouterContext.streamController = controller;
        }
    }).pipeThrough(new TextEncoderStream());
</script>
<script type="module" async="">
    ;import*as route0 from "/assets/root-DQ8p9z4E.js";
    import*as route1 from "/assets/app_layout-BtFaM-Fj.js";
    import*as route2 from "/assets/search_for_projects_layout-k6Xed7VG.js";
    window.__reactRouterManifest = {
        "entry": {
            "module": "/assets/entry.client-DzurGooA.js",
            "imports": ["/assets/clientModeHttpHeaderManager-C9RmXsuh.js", "/assets/effector-B7BVxv2N.js", "/assets/toPropertyKey-B9UmubNl.js", "/assets/manager-Bjjz_weN.js", "/assets/index-DzhfzPRG.js", "/assets/debug-build-DaLUBJrk.js", "/assets/index-lhEm7YEp.js", "/assets/CustomI18nextProvider-DMrXB3fU.js"],
            "css": []
        },
        "routes": {
            "root": {
                "id": "root",
                "path": "",
                "hasAction": false,
                "hasLoader": true,
                "hasClientAction": false,
                "hasClientLoader": false,
                "hasClientMiddleware": false,
                "hasErrorBoundary": true,
                "module": "/assets/root-DQ8p9z4E.js",
                "imports": ["/assets/clientModeHttpHeaderManager-C9RmXsuh.js", "/assets/effector-B7BVxv2N.js", "/assets/toPropertyKey-B9UmubNl.js", "/assets/manager-Bjjz_weN.js", "/assets/index-DzhfzPRG.js", "/assets/debug-build-DaLUBJrk.js", "/assets/index-lhEm7YEp.js", "/assets/CustomI18nextProvider-DMrXB3fU.js", "/assets/index-D1oHx4gy.js", "/assets/debounce-DN82-I86.js", "/assets/withReactQuery-D6b-IU1D.js", "/assets/index.es-CjrEKEV-.js", "/assets/should-revalidate-D-jMpSm3.js", "/assets/createApiServices-DxiPWx9N.js", "/assets/context-CS3l8pcR.js", "/assets/isTouchDevice-BtFX-jxZ.js", "/assets/GoogleReCaptchaContext-G8mI_5bn.js", "/assets/index-B3K6BV7f.js", "/assets/IsInCollectionProvider-Borts8ir.js", "/assets/IsInCollectionContext-BcTDvJVr.js", "/assets/index-Cv4VwrvW.js", "/assets/useWillUnmount-BTu1QOH9.js", "/assets/index-CuUA_3QR.js", "/assets/context-L8W4aFYA.js", "/assets/QueryClientProvider-Djt9oaZK.js", "/assets/index-9IwBu1qG.js"],
                "css": ["/assets/root-DL7HVTlL.css"]
            },
            "routes/layouts/app_layout": {
                "id": "routes/layouts/app_layout",
                "parentId": "root",
                "hasAction": false,
                "hasLoader": true,
                "hasClientAction": false,
                "hasClientLoader": false,
                "hasClientMiddleware": false,
                "hasErrorBoundary": true,
                "module": "/assets/app_layout-BtFaM-Fj.js",
                "imports": ["/assets/index-D1oHx4gy.js", "/assets/clientModeHttpHeaderManager-C9RmXsuh.js", "/assets/RedirectToErrorPage-0laPYz9L.js", "/assets/index-BWsAfL3m.js", "/assets/should-revalidate-D-jMpSm3.js", "/assets/import_modals-DPQGb7B1.js", "/assets/createApiServices-DxiPWx9N.js", "/assets/index-Cv4VwrvW.js", "/assets/index-DzhfzPRG.js", "/assets/index-9IwBu1qG.js", "/assets/react-tooltip.min-D942YL5r.js", "/assets/effector-B7BVxv2N.js", "/assets/debounce-DN82-I86.js", "/assets/index-DbxGA5hl.js", "/assets/useDidMount-SOSqJV7G.js", "/assets/useMainFilters-tzdomz5p.js", "/assets/analyticsContext-DpZV9T6i.js", "/assets/MeasureContext-BE9EF_L8.js", "/assets/MeasureStore-DrdzITP3.js", "/assets/FiltersContext-DDb5f0fR.js", "/assets/index-DK8mBXJ7.js", "/assets/CurrencyStore-BiP0EzQS.js", "/assets/converters-CTUY7UUw.js", "/assets/isString-CsdCDsnW.js", "/assets/omit-B9iVDUuQ.js", "/assets/omit-BoRzqOh4.js", "/assets/last-oQFn8hUT.js", "/assets/helpers-C3hq4x-L.js", "/assets/react-number-format.es-COYKVZWD.js", "/assets/index-D-ImV_kn.js", "/assets/isBefore-CfJADuSa.js", "/assets/index.es-CjrEKEV-.js", "/assets/index-lhEm7YEp.js", "/assets/useFilters-CKqTC2TL.js", "/assets/hooks-DIzN3zjD.js", "/assets/NumeralProvider-Bt8Hi53c.js", "/assets/useDidUpdate-DbzxUgDV.js", "/assets/useWillUnmount-BTu1QOH9.js", "/assets/index-BTxTMWWa.js", "/assets/index-CGTviU3j.js", "/assets/index-mcx6wl3H.js", "/assets/AuthProvider-DKGZlOPK.js", "/assets/hooks-B9wVXA-M.js", "/assets/QueryClientProvider-Djt9oaZK.js", "/assets/helpers-JCefK0aN.js", "/assets/find-CCVKaEKR.js", "/assets/useCurrentUser-C6mZ7Kif.js", "/assets/useCurrentCity-Bs08G94U.js", "/assets/countries-CCFEiA38.js", "/assets/index-PyoBGbMz.js", "/assets/useMainFilterStore-BA4s8gCu.js", "/assets/styles.module-DTVS7n51.js", "/assets/DictionarySegmentControlField-DGRtIkDk.js", "/assets/index-kK9avhVY.js", "/assets/DropdownOption-BIYGm1xk.js", "/assets/DropdownOptionList-Dbhh1TEn.js", "/assets/free-mode-Cl4ytcXx.js", "/assets/hammer-CLPwbGmQ.js", "/assets/helpers-CUKRr88i.js", "/assets/useAgenciesListedByFilterSelectedOptionsStore-CmxGrZtC.js", "/assets/set-CSqHUXx4.js", "/assets/setMonth-N6JsmM3C.js", "/assets/flow-B_iWYD9z.js", "/assets/sortBy-DtaJkhn7.js", "/assets/hooks-Ur_KVb63.js", "/assets/ChipsComboboxControl-DodGjeki.js", "/assets/DictionaryCombobox-BPT-xhR9.js", "/assets/ComboboxOptionsList-CHPGXLQC.js", "/assets/index-Bl8rNFhg.js", "/assets/tslib.es6-mgWJUayD.js", "/assets/index-B1JdJRAd.js", "/assets/MobileDrawer-OyyiwXPt.js", "/assets/isTouchDevice-BtFX-jxZ.js", "/assets/index-nggwapDP.js", "/assets/InputCurrency-Be-49gi_.js", "/assets/index-Cl0x-mvD.js", "/assets/ImWithClientProvider-DkxxfzWF.js", "/assets/useDebounceOnChange-CQ5yEfvK.js", "/assets/useDebouncedValue-KqD10P93.js", "/assets/PriceHint-Cx9bGpiT.js", "/assets/index-eSZKOShP.js", "/assets/ExclusiveLabel-uGyNOYgP.js", "/assets/index-Bpkc5PGW.js", "/assets/DropdownOptionColumnGroups-BHDORrmB.js", "/assets/SearchOnSiteCombobox-BpPJ5uLi.js", "/assets/ComboboxPopup-DzIUVGDr.js", "/assets/suggestService-CJ-q0Pdv.js", "/assets/utils-DWenTZcj.js", "/assets/useAsyncLoadComboboxOptions-CFq9I0IM.js", "/assets/constants-Bbh45y0w.js", "/assets/FiltersFooter-Ro1yJnEH.js", "/assets/SingleCombobox-BXQU1QtY.js", "/assets/ModelsInventorySaleType-D692humT.js", "/assets/head-Bh32IBBv.js", "/assets/index-B3K6BV7f.js", "/assets/useGoogleAnalytics-iXCouI2B.js", "/assets/Indicator-BCTcZ_qo.js", "/assets/context-CS3l8pcR.js", "/assets/SubscriptionManagementButton-MLzzhfOe.js", "/assets/useShowVerificationStatusPopup-DFdnjsGP.js", "/assets/compat-p3DJ-mhR.js", "/assets/addMilliseconds-DI2RJqCX.js", "/assets/isAfter-Ch9B7M-a.js", "/assets/VerificationProgressBar-0zKsugIi.js", "/assets/stopPropagation-DAtVfayL.js", "/assets/NewBadge-DZ8jXqRK.js", "/assets/AgencyProvider-B7ay0TiU.js", "/assets/index-BWSVCGi4.js", "/assets/toasts-DLTkdsps.js", "/assets/useQuery-DWrYcOWg.js", "/assets/useBaseQuery-DUGbP2Kb.js", "/assets/validation-C7tcXRWA.js", "/assets/BaseMapContainer-CvrlSu9u.js", "/assets/index-D2XAoqWc.js", "/assets/GoogleMapTileLayer-Cozx3goK.js", "/assets/reverse-i5ZcxzPe.js", "/assets/reverse-lqZiMTWN.js", "/assets/last-MvWub-5u.js", "/assets/uniq-B5OHIs0i.js", "/assets/_baseUniq-D_-AMbwg.js", "/assets/types-nlarRGfo.js", "/assets/index-DbE2djz8.js", "/assets/InvitesToAgency-Dg3kND8q.js", "/assets/ListItemNew-D7uUqXqm.js", "/assets/useCheckPermission-CkiEq5Il.js", "/assets/IconButton-NvUYdJN3.js", "/assets/Card-TPJzz58y.js", "/assets/index-BRM6HjI4.js", "/assets/useTariffGuard-DeDQ9Suh.js", "/assets/client-only-Dt6en-JV.js", "/assets/SimpleMobileDrawer-BB-WwdR7.js", "/assets/SiteSettings-Ce8IYV6m.js", "/assets/useMeasureOptionsManager-DePTqd2U.js", "/assets/LogoWithClient-BhA5CT2v.js", "/assets/Logo-BSXRLg9K.js", "/assets/flowRight-DhZel7Cq.js", "/assets/AccessControlProviderWrapper-GxDL5-L5.js", "/assets/utils-DynR-DyR.js", "/assets/context-DM7mM-1t.js", "/assets/index-3OLT3HBU.js"],
                "css": ["/assets/react-tooltip-C1RxmEJd.css", "/assets/index-CIGspkUj.css", "/assets/index-_o2ZaHar.css", "/assets/useMainFilters-CzF5A1Ty.css", "/assets/index-B2bRXHm6.css", "/assets/index-DTi9ota9.css", "/assets/styles-BRZoLynU.css", "/assets/DropdownOption-B87qHNXs.css", "/assets/DropdownOptionList-CgjJxN1R.css", "/assets/useAgenciesListedByFilterSelectedOptionsStore-BFLhgIrR.css", "/assets/ChipsComboboxControl-TL0OA0t7.css", "/assets/DictionaryCombobox-CmQXHYEg.css", "/assets/ComboboxOptionsList-DujX5L_x.css", "/assets/MobileDrawer-C_J-abo6.css", "/assets/PriceHint-BMaGYChV.css", "/assets/index-TSWRHw_o.css", "/assets/index-rRMqGaHk.css", "/assets/DropdownOptionColumnGroups-LMyHkDhW.css", "/assets/ComboboxPopup-C2ybbEwS.css", "/assets/FiltersFooter-DKQaQeh4.css", "/assets/SingleCombobox-CZxUUPSQ.css", "/assets/Indicator-BbtvRwe1.css", "/assets/useShowVerificationStatusPopup-D1GWmdof.css", "/assets/VerificationProgressBar-Bzfh_we5.css", "/assets/NewBadge-dfLcfMDU.css", "/assets/validation-CdGmaXDe.css", "/assets/BaseMapContainer-DQK-U8lT.css", "/assets/index-DHG3qMD-.css", "/assets/InvitesToAgency-BDYmJQwj.css", "/assets/ListItemNew-DXRuQg_s.css", "/assets/IconButton-D5PtH23T.css", "/assets/Card-B5ADMC51.css", "/assets/index-Bn_Xn9bw.css", "/assets/SimpleMobileDrawer-D1_yjUud.css", "/assets/SiteSettings-D-2opAWi.css", "/assets/useMeasureOptionsManager-fZKK9FmS.css", "/assets/LogoWithClient-Djff4t1X.css", "/assets/index-Dzts6YcB.css"]
            },
            "pages/search_for_projects_page/search_for_projects_layout": {
                "id": "pages/search_for_projects_page/search_for_projects_layout",
                "parentId": "routes/layouts/app_layout",
                "path": "/app",
                "hasAction": false,
                "hasLoader": false,
                "hasClientAction": false,
                "hasClientLoader": false,
                "hasClientMiddleware": false,
                "hasErrorBoundary": false,
                "module": "/assets/search_for_projects_layout-k6Xed7VG.js",
                "imports": ["/assets/index-D1oHx4gy.js", "/assets/clientModeHttpHeaderManager-C9RmXsuh.js", "/assets/should-revalidate-D-jMpSm3.js", "/assets/client-only-Dt6en-JV.js", "/assets/analyticsContext-DpZV9T6i.js", "/assets/useSendGaEventOnMount-DqKjSp_H.js", "/assets/react-tooltip.min-D942YL5r.js", "/assets/debounce-DN82-I86.js", "/assets/useWillUnmount-BTu1QOH9.js", "/assets/IconButton-NvUYdJN3.js", "/assets/bulkAddToCollectionStore-CsU7coXx.js", "/assets/currentActivePolygonStore-ez3Y9sXM.js", "/assets/head-Bh32IBBv.js", "/assets/index-BTxTMWWa.js", "/assets/FiltersContext-DDb5f0fR.js", "/assets/isString-CsdCDsnW.js", "/assets/useMainFilterStore-BA4s8gCu.js", "/assets/CurrencyStore-BiP0EzQS.js", "/assets/MeasureStore-DrdzITP3.js", "/assets/omit-B9iVDUuQ.js", "/assets/BedroomsFilterCombobox-aepjA3xI.js", "/assets/helpers-CUKRr88i.js", "/assets/useAgenciesListedByFilterSelectedOptionsStore-CmxGrZtC.js", "/assets/hammer-CLPwbGmQ.js", "/assets/flow-B_iWYD9z.js", "/assets/MeasureContext-BE9EF_L8.js", "/assets/InputCurrency-Be-49gi_.js", "/assets/PriceHint-Cx9bGpiT.js", "/assets/SearchProjectsEffect-BULfUfD8.js", "/assets/sortBy-DtaJkhn7.js", "/assets/DropdownPriceFilter-v1kk05Ne.js", "/assets/useMainFilters-tzdomz5p.js", "/assets/helpers-JCefK0aN.js", "/assets/index-B3K6BV7f.js", "/assets/ImWithClientProvider-DkxxfzWF.js", "/assets/FilterButtonWithFilterCount-CuznGVYw.js", "/assets/useDidUpdate-DbzxUgDV.js", "/assets/index-DbxGA5hl.js", "/assets/InfoTooltip-D_0XCGFC.js", "/assets/BaseMapContainer-CvrlSu9u.js", "/assets/MapLayerToggle-COWNPRZT.js", "/assets/effector-B7BVxv2N.js", "/assets/index-CpSq0KHr.js", "/assets/index-ConoCbk8.js", "/assets/server.browser-BYcQV9Ub.js", "/assets/FlagIcon-B9JW5tLz.js", "/assets/GoogleMapTileLayer-Cozx3goK.js", "/assets/index-9IwBu1qG.js", "/assets/utils-CAeOQ-Tx.js", "/assets/utils-7zCAlAqF.js", "/assets/getPathOptionsForPolygon-Ck_FHalz.js", "/assets/ShowOnlyWhenImNotWithClient-CK99e40A.js", "/assets/useNewsQuery-V2lH7Duk.js", "/assets/SliderNavigationControl-sC_msmFi.js", "/assets/useMediaQuery-BvndBfCI.js", "/assets/NewBadge-DZ8jXqRK.js", "/assets/index-CxfOSz5v.js", "/assets/ImagePlaceholder-BKiX4mws.js", "/assets/AgencyCell-B8T15WJf.js", "/assets/helpers-DdY64bOu.js", "/assets/SimpleDropdownSort-C8EWwsey.js", "/assets/ProjectsNotFound-whREnr87.js", "/assets/useAsyncLoadComboboxOptions-CFq9I0IM.js", "/assets/useDidMount-SOSqJV7G.js", "/assets/index-DdYzqrcu.js", "/assets/utils-DynR-DyR.js", "/assets/useGoogleAnalytics-iXCouI2B.js", "/assets/index-DzhfzPRG.js", "/assets/index-DK8mBXJ7.js", "/assets/helpers-C3hq4x-L.js", "/assets/converters-CTUY7UUw.js", "/assets/react-number-format.es-COYKVZWD.js", "/assets/index-D-ImV_kn.js", "/assets/isBefore-CfJADuSa.js", "/assets/useFilters-CKqTC2TL.js", "/assets/omit-BoRzqOh4.js", "/assets/last-oQFn8hUT.js", "/assets/ChipsComboboxControl-DodGjeki.js", "/assets/DictionaryCombobox-BPT-xhR9.js", "/assets/ComboboxOptionsList-CHPGXLQC.js", "/assets/index-Bl8rNFhg.js", "/assets/tslib.es6-mgWJUayD.js", "/assets/index-lhEm7YEp.js", "/assets/index-B1JdJRAd.js", "/assets/MobileDrawer-OyyiwXPt.js", "/assets/isTouchDevice-BtFX-jxZ.js", "/assets/DropdownOption-BIYGm1xk.js", "/assets/index-nggwapDP.js", "/assets/FiltersFooter-Ro1yJnEH.js", "/assets/index-kK9avhVY.js", "/assets/DropdownOptionList-Dbhh1TEn.js", "/assets/free-mode-Cl4ytcXx.js", "/assets/SingleCombobox-BXQU1QtY.js", "/assets/index-Bpkc5PGW.js", "/assets/DictionarySegmentControlField-DGRtIkDk.js", "/assets/ModelsInventorySaleType-D692humT.js", "/assets/set-CSqHUXx4.js", "/assets/setMonth-N6JsmM3C.js", "/assets/hooks-Ur_KVb63.js", "/assets/hooks-DIzN3zjD.js", "/assets/index-Cl0x-mvD.js", "/assets/index-eSZKOShP.js", "/assets/ExclusiveLabel-uGyNOYgP.js", "/assets/DropdownOptionColumnGroups-BHDORrmB.js", "/assets/delay-DwP2GHX9.js", "/assets/core-C0wgD8BO.js", "/assets/useManualMapTooltipState-CkO7UkoB.js", "/assets/helpers-Io_Pmx5I.js", "/assets/find-CCVKaEKR.js", "/assets/map--SAUJ3_C.js", "/assets/reverse-lqZiMTWN.js", "/assets/context-CS3l8pcR.js", "/assets/SubscriptionManagementButton-MLzzhfOe.js", "/assets/helpers-Bb0OYOCE.js", "/assets/helpers-TP9ZegV6.js", "/assets/isEqual-j_RfxUxk.js", "/assets/Models_Project-PiukaWZu.js", "/assets/context-D-45luS0.js", "/assets/ProjectCard-DxRWvxW7.js", "/assets/index-omaZuc-a.js", "/assets/Indicator-BCTcZ_qo.js", "/assets/index-BWSVCGi4.js", "/assets/utils-DWenTZcj.js", "/assets/createApiServices-DxiPWx9N.js", "/assets/index-Cv4VwrvW.js", "/assets/toasts-DLTkdsps.js", "/assets/useQuery-DWrYcOWg.js", "/assets/useBaseQuery-DUGbP2Kb.js", "/assets/QueryClientProvider-Djt9oaZK.js", "/assets/useCurrentUser-C6mZ7Kif.js", "/assets/hooks-B9wVXA-M.js", "/assets/ShowOnlyToAuthUsers-C9mgaFdC.js", "/assets/AddToBookmarksButton-csorYAjM.js", "/assets/ProjectCardSkeleton-B4BoDR21.js", "/assets/DateFormat-COf59ysS.js", "/assets/CircleProgress-Dlf_hvA1.js", "/assets/Models_GetUserBookmarksItem-C9eLmA1J.js", "/assets/index.es-CjrEKEV-.js", "/assets/NumeralProvider-Bt8Hi53c.js", "/assets/index-CGTviU3j.js", "/assets/index-mcx6wl3H.js", "/assets/AuthProvider-DKGZlOPK.js", "/assets/useCurrentCity-Bs08G94U.js", "/assets/countries-CCFEiA38.js", "/assets/index-PyoBGbMz.js", "/assets/styles.module-DTVS7n51.js", "/assets/useDebounceOnChange-CQ5yEfvK.js", "/assets/useDebouncedValue-KqD10P93.js", "/assets/SearchOnSiteCombobox-BpPJ5uLi.js", "/assets/ComboboxPopup-DzIUVGDr.js", "/assets/suggestService-CJ-q0Pdv.js", "/assets/constants-Bbh45y0w.js", "/assets/SimpleMobileDrawer-BB-WwdR7.js", "/assets/index-D2XAoqWc.js", "/assets/reverse-i5ZcxzPe.js", "/assets/createDistrictQueryKeys-D8-Ml7Mm.js", "/assets/useInfiniteQuery-GuWJly7h.js", "/assets/newsQueryKeys-DdMnl9Fx.js", "/assets/index-BRM6HjI4.js", "/assets/index-DeqjcgkY.js", "/assets/DropdownControlTransparent-BNRzYjkE.js", "/assets/EmptyData-Cr5ICnWe.js"],
                "css": ["/assets/search_for_projects_layout-i4G4gYml.css", "/assets/react-tooltip-C1RxmEJd.css", "/assets/index-CIGspkUj.css", "/assets/IconButton-D5PtH23T.css", "/assets/BaseMapContainer-DQK-U8lT.css", "/assets/index-DHG3qMD-.css", "/assets/index-DTi9ota9.css", "/assets/NewBadge-dfLcfMDU.css", "/assets/ChipsComboboxControl-TL0OA0t7.css", "/assets/DictionaryCombobox-CmQXHYEg.css", "/assets/ComboboxOptionsList-DujX5L_x.css", "/assets/MobileDrawer-C_J-abo6.css", "/assets/DropdownOption-B87qHNXs.css", "/assets/FiltersFooter-DKQaQeh4.css", "/assets/DropdownOptionList-CgjJxN1R.css", "/assets/SingleCombobox-CZxUUPSQ.css", "/assets/index-rRMqGaHk.css", "/assets/useAgenciesListedByFilterSelectedOptionsStore-BFLhgIrR.css", "/assets/PriceHint-BMaGYChV.css", "/assets/index-TSWRHw_o.css", "/assets/DropdownOptionColumnGroups-LMyHkDhW.css", "/assets/SearchProjectsEffect-BfjfcrcG.css", "/assets/core-C3r3wMeK.css", "/assets/ProjectCard-T7cs6O27.css", "/assets/index-Du6_scQs.css", "/assets/Indicator-BbtvRwe1.css", "/assets/index-B0YlClZO.css", "/assets/AddToBookmarksButton-BlBsuTcS.css", "/assets/ProjectCardSkeleton-D0YtKkhO.css", "/assets/CircleProgress-BZIBsSJw.css", "/assets/DropdownPriceFilter-YQmoyK1O.css", "/assets/useMainFilters-CzF5A1Ty.css", "/assets/index-B2bRXHm6.css", "/assets/styles-BRZoLynU.css", "/assets/ComboboxPopup-C2ybbEwS.css", "/assets/InfoTooltip-Dh2NYlxu.css", "/assets/SimpleMobileDrawer-D1_yjUud.css", "/assets/MapLayerToggle-BV_AfXiy.css", "/assets/SliderNavigationControl-Ch4_5q_3.css", "/assets/ImagePlaceholder-qxZDBb_E.css", "/assets/index-Bn_Xn9bw.css", "/assets/DropdownControlTransparent-DKkwDImu.css", "/assets/EmptyData-B1M-ihk7.css"]
            },
            "pages/LandingPage/LandingPage": {
                "id": "pages/LandingPage/LandingPage",
                "parentId": "routes/layouts/app_layout",
                "index": true,
                "hasAction": false,
                "hasLoader": true,
                "hasClientAction": false,
                "hasClientLoader": false,
                "hasClientMiddleware": false,
                "hasErrorBoundary": false,
                "module": "/assets/LandingPage-awyy_rHe.js",
                "imports": ["/assets/index-D1oHx4gy.js", "/assets/clientModeHttpHeaderManager-C9RmXsuh.js", "/assets/analyticsContext-DpZV9T6i.js", "/assets/useSendGaEventOnMount-DqKjSp_H.js", "/assets/index-DzhfzPRG.js", "/assets/react-tooltip.min-D942YL5r.js", "/assets/debounce-DN82-I86.js", "/assets/index-9IwBu1qG.js", "/assets/client-only-Dt6en-JV.js", "/assets/MeasureContext-BE9EF_L8.js", "/assets/useCurrentCity-Bs08G94U.js", "/assets/helpers-JCefK0aN.js", "/assets/CurrencyStore-BiP0EzQS.js", "/assets/FiltersContext-DDb5f0fR.js", "/assets/isString-CsdCDsnW.js", "/assets/useMainFilterStore-BA4s8gCu.js", "/assets/MeasureStore-DrdzITP3.js", "/assets/omit-B9iVDUuQ.js", "/assets/hammer-CLPwbGmQ.js", "/assets/helpers-CUKRr88i.js", "/assets/useAgenciesListedByFilterSelectedOptionsStore-CmxGrZtC.js", "/assets/flow-B_iWYD9z.js", "/assets/InputCurrency-Be-49gi_.js", "/assets/ImWithClientProvider-DkxxfzWF.js", "/assets/sortBy-DtaJkhn7.js", "/assets/SearchOnSiteCombobox-BpPJ5uLi.js", "/assets/index-CY-5onI9.js", "/assets/index-CxYLih5l.js", "/assets/effector-B7BVxv2N.js", "/assets/useGoogleAnalytics-iXCouI2B.js", "/assets/index-DbxGA5hl.js", "/assets/AlertSubmission-BgSNf6jE.js", "/assets/useSignInByEmailSubmit-CUSKzFVx.js", "/assets/TermsFooter-CHNccMsy.js", "/assets/useDidMount-SOSqJV7G.js", "/assets/hooks-B9wVXA-M.js", "/assets/find-CCVKaEKR.js", "/assets/isBefore-CfJADuSa.js", "/assets/converters-CTUY7UUw.js", "/assets/index-DK8mBXJ7.js", "/assets/helpers-C3hq4x-L.js", "/assets/react-number-format.es-COYKVZWD.js", "/assets/index-D-ImV_kn.js", "/assets/useFilters-CKqTC2TL.js", "/assets/omit-BoRzqOh4.js", "/assets/last-oQFn8hUT.js", "/assets/index-lhEm7YEp.js", "/assets/useWillUnmount-BTu1QOH9.js", "/assets/set-CSqHUXx4.js", "/assets/setMonth-N6JsmM3C.js", "/assets/hooks-Ur_KVb63.js", "/assets/hooks-DIzN3zjD.js", "/assets/ChipsComboboxControl-DodGjeki.js", "/assets/DictionaryCombobox-BPT-xhR9.js", "/assets/ComboboxOptionsList-CHPGXLQC.js", "/assets/index-Bl8rNFhg.js", "/assets/tslib.es6-mgWJUayD.js", "/assets/index-B1JdJRAd.js", "/assets/MobileDrawer-OyyiwXPt.js", "/assets/isTouchDevice-BtFX-jxZ.js", "/assets/useDidUpdate-DbzxUgDV.js", "/assets/DropdownOption-BIYGm1xk.js", "/assets/index-nggwapDP.js", "/assets/index-Cl0x-mvD.js", "/assets/ComboboxPopup-DzIUVGDr.js", "/assets/index-CGTviU3j.js", "/assets/index-mcx6wl3H.js", "/assets/suggestService-CJ-q0Pdv.js", "/assets/index-Cv4VwrvW.js", "/assets/utils-DWenTZcj.js", "/assets/createApiServices-DxiPWx9N.js", "/assets/useAsyncLoadComboboxOptions-CFq9I0IM.js", "/assets/index-DL6jHQtM.js", "/assets/index-D8cIc1Qi.js", "/assets/useMutation-gwM82jFU.js", "/assets/QueryClientProvider-Djt9oaZK.js", "/assets/services-C6O9HMD8.js", "/assets/addGRecaptchaResponse-DSRH-NFK.js", "/assets/useGoogleReCaptcha-BxdLt1dG.js", "/assets/GoogleReCaptchaContext-G8mI_5bn.js", "/assets/context-CS3l8pcR.js", "/assets/SubscriptionManagementButton-MLzzhfOe.js"],
                "css": ["/assets/LandingPage-D-qj8UqM.css", "/assets/react-tooltip-C1RxmEJd.css", "/assets/index-CIGspkUj.css", "/assets/useAgenciesListedByFilterSelectedOptionsStore-BFLhgIrR.css", "/assets/ChipsComboboxControl-TL0OA0t7.css", "/assets/DictionaryCombobox-CmQXHYEg.css", "/assets/ComboboxOptionsList-DujX5L_x.css", "/assets/MobileDrawer-C_J-abo6.css", "/assets/DropdownOption-B87qHNXs.css", "/assets/ComboboxPopup-C2ybbEwS.css", "/assets/index-B2bRXHm6.css", "/assets/index-DTi9ota9.css", "/assets/index-CJ5DH5so.css", "/assets/TermsFooter-Blrbj_EK.css"]
            }
        },
        "url": "/assets/manifest-02968df3.js",
        "version": "02968df3"
    };
    window.__reactRouterRouteModules = {
        "root": route0,
        "routes/layouts/app_layout": route1,
        "pages/search_for_projects_page/search_for_projects_layout": route2
    };

    import("/assets/entry.client-DzurGooA.js");
</script>
<div id="react_confirm_alert"></div>
<div id="portals_container">
    <div id="portals_popup"></div>
    <div id="portals_dropdown"></div>
    <div id="portals_alert"></div>
    <div id="portals_toasty">
        <section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section>
    </div>
</div>
<!--$?-->
<template id="B:0"></template>
<!--/$-->
<div hidden id="S:0">
    <script>
        window.__reactRouterContext.streamController.enqueue("[{\"_1\":2,\"_1569\":-5,\"_1570\":-5},\"loaderData\",{\"_3\":4,\"_68\":69},\"root\",{\"_5\":6,\"_39\":40,\"_41\":42,\"_43\":44,\"_45\":-5,\"_46\":-5,\"_47\":48,\"_49\":50,\"_51\":-5,\"_52\":53,\"_66\":67},\"initNamespaces\",[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38],\"album\",\"auth\",\"billing\",\"bookUnit\",\"constants\",\"countryNames\",\"errorsPage\",\"featureAnnouncement\",\"feedbackForm\",\"filter\",\"footer\",\"forAgenciesPage\",\"header\",\"inventory\",\"landingPage\",\"layoutPage\",\"map\",\"paymentPlan\",\"profile\",\"project\",\"projectCard\",\"promotion\",\"salesOffices\",\"search\",\"selection\",\"selectionUnit\",\"sort\",\"transactions\",\"ui\",\"unit\",\"validation\",\"zod\",\"language\",\"en\",\"t\",[\"SingleFetchFallback\"],\"body\",[\"SingleFetchFallback\"],\"token\",\"token_expires_at\",\"currency\",\"AED\",\"measure\",\"sqm\",\"city\",\"whitelabel\",{\"_54\":55,\"_56\":57},\"isWhitelabel\",false,\"data\",{\"_58\":59,\"_60\":61},\"title\",\"Alnair\",\"logo\",{\"_62\":63,\"_64\":65,\"_60\":65},\"id\",1,\"src\",\"/favicon.png\",\"origin\",\"http://alnair.ae\",\"routes/layouts/app_layout\",{\"_70\":71,\"_1237\":1238,\"_47\":48,\"_1552\":48,\"_49\":50,\"_39\":40,\"_1553\":63,\"_1548\":-5,\"_1554\":55,\"_45\":-5,\"_46\":-5,\"_1547\":-5,\"_1497\":-5,\"_1555\":55,\"_1556\":1557,\"_52\":53},\"info\",{\"_51\":72,\"_83\":84,\"_333\":334,\"_352\":353,\"_1237\":1238,\"_1257\":1258,\"_1312\":1313,\"_1318\":1319,\"_1407\":1408,\"_1497\":1498,\"_1547\":-5,\"_1548\":1549,\"_1550\":1551},{\"_62\":63,\"_73\":74,\"_75\":76,\"_77\":78,\"_79\":80,\"_47\":48,\"_81\":55,\"_82\":63},\"name\",\"Dubai\",\"country_id\",2,\"latitude\",\"25.15433020\",\"longitude\",\"55.26127900\",\"is_private\",\"is_permit_number\",\"cities\",[85,86,91,96,101,107,112,117,124,129,134,139,146,151,156,162,167,172,177,183,188,193,198,203,208,213,218,223,228,233,238,243,248,253,258,263,268,273,278,282,287,292,297,302,308,313,318,323,328],{\"_62\":63,\"_73\":74,\"_75\":76,\"_77\":78,\"_79\":80,\"_47\":48,\"_81\":55,\"_82\":63},{\"_62\":76,\"_73\":87,\"_75\":76,\"_77\":88,\"_79\":89,\"_47\":48,\"_81\":55,\"_82\":90},\"Abu Dhabi\",\"24.39871220\",\"54.47347640\",0,{\"_62\":92,\"_73\":93,\"_75\":76,\"_77\":94,\"_79\":95,\"_47\":48,\"_81\":55,\"_82\":90},3,\"Sharjah\",\"25.34447620\",\"55.40548330\",{\"_62\":97,\"_73\":98,\"_75\":76,\"_77\":99,\"_79\":100,\"_47\":48,\"_81\":55,\"_82\":90},5,\"Ras Al Khaimah\",\"25.77862550\",\"55.95320280\",{\"_62\":102,\"_73\":103,\"_75\":92,\"_77\":104,\"_79\":105,\"_47\":106,\"_81\":55,\"_82\":90},6,\"Phuket\",\"7.87897800\",\"98.39839200\",\"THB\",{\"_62\":108,\"_73\":109,\"_75\":76,\"_77\":110,\"_79\":111,\"_47\":48,\"_81\":55,\"_82\":90},7,\"Ajman\",\"25.40327750\",\"55.47766910\",{\"_62\":113,\"_73\":114,\"_75\":92,\"_77\":115,\"_79\":116,\"_47\":106,\"_81\":55,\"_82\":90},8,\"Samui\",\"9.49987710\",\"99.93911410\",{\"_62\":118,\"_73\":119,\"_75\":120,\"_77\":121,\"_79\":122,\"_47\":123,\"_81\":55,\"_82\":90},9,\"Muscat\",4,\"23.61018270\",\"58.59334350\",\"OMR\",{\"_62\":125,\"_73\":126,\"_75\":120,\"_77\":127,\"_79\":128,\"_47\":123,\"_81\":55,\"_82\":90},10,\"Salalah\",\"17.01570000\",\"54.09240000\",{\"_62\":130,\"_73\":131,\"_75\":120,\"_77\":132,\"_79\":133,\"_47\":123,\"_81\":55,\"_82\":90},11,\"Duqm\",\"19.57340000\",\"57.63320000\",{\"_62\":135,\"_73\":136,\"_75\":92,\"_77\":137,\"_79\":138,\"_47\":106,\"_81\":55,\"_82\":90},12,\"Bangkok\",\"13.75630000\",\"100.50180000\",{\"_62\":140,\"_73\":141,\"_75\":97,\"_77\":142,\"_79\":143,\"_47\":144,\"_81\":145,\"_82\":90},13,\"London (Intermark)\",\"51.49582475\",\"-0.09205173\",\"GBP\",true,{\"_62\":147,\"_73\":148,\"_75\":76,\"_77\":149,\"_79\":150,\"_47\":48,\"_81\":55,\"_82\":90},14,\"Umm Al Quwain\",\"25.54917200\",\"55.54931700\",{\"_62\":152,\"_73\":153,\"_75\":92,\"_77\":154,\"_79\":155,\"_47\":106,\"_81\":55,\"_82\":90},15,\"Pattaya\",\"12.92360000\",\"100.88280000\",{\"_62\":157,\"_73\":158,\"_75\":102,\"_77\":159,\"_79\":160,\"_47\":161,\"_81\":55,\"_82\":90},16,\"Bali\",\"-8.34050000\",\"115.09200000\",\"USD\",{\"_62\":163,\"_73\":164,\"_75\":92,\"_77\":165,\"_79\":166,\"_47\":106,\"_81\":55,\"_82\":90},17,\"Chiang Mai\",\"18.70610000\",\"98.98170000\",{\"_62\":168,\"_73\":169,\"_75\":92,\"_77\":170,\"_79\":171,\"_47\":106,\"_81\":55,\"_82\":90},18,\"Hua Hin\",\"12.56830000\",\"99.96280000\",{\"_62\":173,\"_73\":174,\"_75\":108,\"_77\":175,\"_79\":176,\"_47\":48,\"_81\":145,\"_82\":90},19,\"Qatar\",\"25.27698700\",\"51.52639800\",{\"_62\":178,\"_73\":179,\"_75\":113,\"_77\":180,\"_79\":181,\"_47\":182,\"_81\":145,\"_82\":90},21,\"Limassol\",\"34.70710000\",\"33.02260000\",\"EUR\",{\"_62\":184,\"_73\":185,\"_75\":113,\"_77\":186,\"_79\":187,\"_47\":182,\"_81\":145,\"_82\":90},22,\"Paphos\",\"34.77560000\",\"32.42450000\",{\"_62\":189,\"_73\":190,\"_75\":113,\"_77\":191,\"_79\":192,\"_47\":182,\"_81\":145,\"_82\":90},23,\"Larnaca\",\"34.91993750\",\"33.61554530\",{\"_62\":194,\"_73\":195,\"_75\":113,\"_77\":196,\"_79\":197,\"_47\":182,\"_81\":145,\"_82\":90},24,\"Nicosia\",\"35.18560000\",\"33.38230000\",{\"_62\":199,\"_73\":200,\"_75\":113,\"_77\":201,\"_79\":202,\"_47\":182,\"_81\":145,\"_82\":90},25,\"Famagusta\",\"35.11750000\",\"33.94000000\",{\"_62\":204,\"_73\":205,\"_75\":97,\"_77\":206,\"_79\":207,\"_47\":144,\"_81\":145,\"_82\":90},26,\"Manchester\",\"53.47876500\",\"-2.24945700\",{\"_62\":209,\"_73\":210,\"_75\":97,\"_77\":211,\"_79\":212,\"_47\":144,\"_81\":145,\"_82\":90},27,\"Birmingham\",\"52.47930400\",\"-1.89882750\",{\"_62\":214,\"_73\":215,\"_75\":97,\"_77\":216,\"_79\":217,\"_47\":144,\"_81\":145,\"_82\":90},28,\"Liverpool\",\"53.40631400\",\"-2.98152200\",{\"_62\":219,\"_73\":220,\"_75\":118,\"_77\":221,\"_79\":222,\"_47\":182,\"_81\":145,\"_82\":90},29,\"Athens\",\"37.98380000\",\"23.72750000\",{\"_62\":224,\"_73\":225,\"_75\":118,\"_77\":226,\"_79\":227,\"_47\":182,\"_81\":145,\"_82\":90},30,\"Chalkidiki\",\"40.24440000\",\"23.49970000\",{\"_62\":229,\"_73\":230,\"_75\":118,\"_77\":231,\"_79\":232,\"_47\":182,\"_81\":145,\"_82\":90},31,\"Thessaloniki\",\"40.64010000\",\"22.94440000\",{\"_62\":234,\"_73\":235,\"_75\":125,\"_77\":236,\"_79\":237,\"_47\":182,\"_81\":145,\"_82\":90},32,\"Barcelona\",\"41.38510000\",\"2.17340000\",{\"_62\":239,\"_73\":240,\"_75\":125,\"_77\":241,\"_79\":242,\"_47\":182,\"_81\":145,\"_82\":90},33,\"Madrid\",\"40.41680000\",\"-3.70380000\",{\"_62\":244,\"_73\":245,\"_75\":125,\"_77\":246,\"_79\":247,\"_47\":182,\"_81\":145,\"_82\":90},34,\"Malaga\",\"36.72130000\",\"-4.42140000\",{\"_62\":249,\"_73\":250,\"_75\":125,\"_77\":251,\"_79\":252,\"_47\":182,\"_81\":145,\"_82\":90},35,\"Benidorm\",\"38.54110000\",\"-0.12250000\",{\"_62\":254,\"_73\":255,\"_75\":125,\"_77\":256,\"_79\":257,\"_47\":182,\"_81\":145,\"_82\":90},36,\"Costa Del Sol\",\"36.52980000\",\"-4.88300000\",{\"_62\":259,\"_73\":260,\"_75\":125,\"_77\":261,\"_79\":262,\"_47\":182,\"_81\":145,\"_82\":90},37,\"Costa Blanca\",\"38.34520000\",\"-0.48150000\",{\"_62\":264,\"_73\":265,\"_75\":125,\"_77\":266,\"_79\":267,\"_47\":182,\"_81\":145,\"_82\":90},38,\"Costa Maresme\",\"41.55060000\",\"2.42690000\",{\"_62\":269,\"_73\":270,\"_75\":125,\"_77\":271,\"_79\":272,\"_47\":182,\"_81\":145,\"_82\":90},39,\"Costa Dorado\",\"41.20190000\",\"1.52570000\",{\"_62\":274,\"_73\":275,\"_75\":125,\"_77\":276,\"_79\":277,\"_47\":182,\"_81\":145,\"_82\":90},40,\"Costa Bravo\",\"41.97940000\",\"3.16610000\",{\"_62\":279,\"_73\":280,\"_75\":102,\"_77\":159,\"_79\":281,\"_47\":161,\"_81\":55,\"_82\":90},41,\"Gili Islands\",\"116.02750000\",{\"_62\":283,\"_73\":284,\"_75\":102,\"_77\":285,\"_79\":286,\"_47\":161,\"_81\":55,\"_82\":90},42,\"Lombok\",\"-8.65000000\",\"116.32000000\",{\"_62\":288,\"_73\":289,\"_75\":102,\"_77\":290,\"_79\":291,\"_47\":161,\"_81\":55,\"_82\":90},43,\"Sumbawa\",\"-8.65290000\",\"117.36160000\",{\"_62\":293,\"_73\":294,\"_75\":118,\"_77\":295,\"_79\":296,\"_47\":182,\"_81\":145,\"_82\":90},44,\"Glyfada\",\"37.87443866\",\"23.75560853\",{\"_62\":298,\"_73\":299,\"_75\":118,\"_77\":300,\"_79\":301,\"_47\":182,\"_81\":145,\"_82\":90},45,\"Ermioni\",\"37.38570216\",\"23.24543336\",{\"_62\":303,\"_73\":304,\"_75\":305,\"_77\":306,\"_79\":307,\"_47\":161,\"_81\":55,\"_82\":90},46,\"Istanbul\",157,\"41.13530395\",\"28.98328603\",{\"_62\":309,\"_73\":310,\"_75\":305,\"_77\":311,\"_79\":312,\"_47\":161,\"_81\":55,\"_82\":90},47,\"Alanya\",\"36.54545085\",\"31.99651492\",{\"_62\":314,\"_73\":315,\"_75\":305,\"_77\":316,\"_79\":317,\"_47\":161,\"_81\":55,\"_82\":90},48,\"Antalya\",\"36.91125691\",\"30.69741097\",{\"_62\":319,\"_73\":320,\"_75\":305,\"_77\":321,\"_79\":322,\"_47\":161,\"_81\":55,\"_82\":90},49,\"Mersin\",\"36.81173288\",\"34.64391483\",{\"_62\":324,\"_73\":325,\"_75\":118,\"_77\":326,\"_79\":327,\"_47\":161,\"_81\":145,\"_82\":90},50,\"Patras\",\"38.24563686\",\"21.73702184\",{\"_62\":329,\"_73\":330,\"_75\":118,\"_77\":331,\"_79\":332,\"_47\":161,\"_81\":55,\"_82\":90},51,\"Crete\",\"35.24753144\",\"24.79500397\",\"countries\",[335,337,339,341,343,345,346,348,350],{\"_62\":76,\"_58\":336,\"_47\":48},\"United Arab Emirates\",{\"_62\":92,\"_58\":338,\"_47\":106},\"Thailand\",{\"_62\":120,\"_58\":340,\"_47\":123},\"Oman\",{\"_62\":97,\"_58\":342,\"_47\":144},\"United Kingdom\",{\"_62\":102,\"_58\":344,\"_47\":161},\"Indonesia\",{\"_62\":108,\"_58\":174,\"_47\":48},{\"_62\":113,\"_58\":347,\"_47\":182},\"Cyprus South\",{\"_62\":118,\"_58\":349,\"_47\":182},\"Greece\",{\"_62\":125,\"_58\":351,\"_47\":182},\"Spain\",\"catalogs\",{\"_354\":355,\"_396\":397,\"_680\":681,\"_699\":700,\"_753\":754,\"_772\":773,\"_786\":787,\"_824\":825,\"_843\":844,\"_872\":873,\"_896\":897,\"_961\":962,\"_970\":971,\"_1047\":1048,\"_1096\":1097,\"_1118\":1119,\"_1181\":1182,\"_1205\":1206},\"unit_complaints\",{\"_62\":356,\"_357\":358},449,\"options\",[359,366,371,376,381,386,391],{\"_62\":360,\"_361\":362,\"_363\":364,\"_56\":365},451,\"value\",\"No response from broker\",\"key\",\"unit_complaints_no_response_broker\",[],{\"_62\":367,\"_361\":368,\"_363\":369,\"_56\":370},450,\"Property not available\",\"unit_complaints_not_available\",[],{\"_62\":372,\"_361\":373,\"_363\":374,\"_56\":375},452,\"No property details\",\"unit_complaints_no_details\",[],{\"_62\":377,\"_361\":378,\"_363\":379,\"_56\":380},454,\"Inaccurate locations\",\"unit_complaints_locations\",[],{\"_62\":382,\"_361\":383,\"_363\":384,\"_56\":385},453,\"Poor quality or irrelevant photos\",\"unit_complaints_poor_quality\",[],{\"_62\":387,\"_361\":388,\"_363\":389,\"_56\":390},455,\"Inaccurate number of bedrooms\",\"unit_complaints_bedrooms\",[],{\"_62\":392,\"_361\":393,\"_363\":394,\"_56\":395},456,\"Incorrect property type\",\"unit_complaints_property_type\",[],\"spoken_languages\",{\"_62\":398,\"_357\":399},387,[400,405,410,415,420,425,430,435,440,445,450,455,460,465,470,475,480,485,490,495,500,505,510,515,520,525,530,535,540,545,550,555,560,565,570,575,580,585,590,595,600,605,610,615,620,625,630,635,640,645,650,655,660,665,670,675],{\"_62\":401,\"_361\":402,\"_363\":403,\"_56\":404},441,\"Ukrainian\",\"spoken_languages_ukrainian\",[],{\"_62\":406,\"_361\":407,\"_363\":408,\"_56\":409},437,\"Tamil\",\"spoken_languages_tamil\",[],{\"_62\":411,\"_361\":412,\"_363\":413,\"_56\":414},406,\"German\",\"spoken_languages_german\",[],{\"_62\":416,\"_361\":417,\"_363\":418,\"_56\":419},421,\"Norwegian\",\"spoken_languages_norwegian\",[],{\"_62\":421,\"_361\":422,\"_363\":423,\"_56\":424},416,\"Kurdi\",\"spoken_languages_kurdi\",[],{\"_62\":426,\"_361\":427,\"_363\":428,\"_56\":429},409,\"Hindi\",\"spoken_languages_hindi\",[],{\"_62\":431,\"_361\":432,\"_363\":433,\"_56\":434},389,\"Albanian\",\"spoken_languages_albanian\",[],{\"_62\":436,\"_361\":437,\"_363\":438,\"_56\":439},420,\"Mandarin\",\"spoken_languages_mandarin\",[],{\"_62\":441,\"_361\":442,\"_363\":443,\"_56\":444},417,\"Latvian\",\"spoken_languages_latvian\",[],{\"_62\":446,\"_361\":447,\"_363\":448,\"_56\":449},397,\"Cantonese\",\"spoken_languages_cantonese\",[],{\"_62\":451,\"_361\":452,\"_363\":453,\"_56\":454},388,\"Afrikaans\",\"spoken_languages_afrikaans\",[],{\"_62\":456,\"_361\":457,\"_363\":458,\"_56\":459},438,\"Telugu\",\"spoken_languages_telugu\",[],{\"_62\":461,\"_361\":462,\"_363\":463,\"_56\":464},431,\"Slovene\",\"spoken_languages_slovene\",[],{\"_62\":466,\"_361\":467,\"_363\":468,\"_56\":469},426,\"Romanian\",\"spoken_languages_romanian\",[],{\"_62\":471,\"_361\":472,\"_363\":473,\"_56\":474},424,\"Portuguese\",\"spoken_languages_portuguese\",[],{\"_62\":476,\"_361\":477,\"_363\":478,\"_56\":479},401,\"Danish\",\"spoken_languages_danish\",[],{\"_62\":481,\"_361\":482,\"_363\":483,\"_56\":484},433,\"Spanish\",\"spoken_languages_spanish\",[],{\"_62\":486,\"_361\":487,\"_363\":488,\"_56\":489},419,\"Malayalam\",\"spoken_languages_malayalam\",[],{\"_62\":491,\"_361\":492,\"_363\":493,\"_56\":494},408,\"Gujarati\",\"spoken_languages_gujarati\",[],{\"_62\":496,\"_361\":497,\"_363\":498,\"_56\":499},390,\"Amharic\",\"spoken_languages_amharic\",[],{\"_62\":501,\"_361\":502,\"_363\":503,\"_56\":504},427,\"Russian\",\"spoken_languages_russian\",[],{\"_62\":506,\"_361\":507,\"_363\":508,\"_56\":509},407,\"Greek\",\"spoken_languages_greek\",[],{\"_62\":511,\"_361\":512,\"_363\":513,\"_56\":514},402,\"Dutch\",\"spoken_languages_dutch\",[],{\"_62\":516,\"_361\":517,\"_363\":518,\"_56\":519},395,\"Berber\",\"spoken_languages_berber\",[],{\"_62\":521,\"_361\":522,\"_363\":523,\"_56\":524},443,\"Uzbek\",\"spoken_languages_uzbek\",[],{\"_62\":526,\"_361\":527,\"_363\":528,\"_56\":529},430,\"Slovak\",\"spoken_languages_slovak\",[],{\"_62\":531,\"_361\":532,\"_363\":533,\"_56\":534},400,\"Czech\",\"spoken_languages_czech\",[],{\"_62\":536,\"_361\":537,\"_363\":538,\"_56\":539},434,\"Swahili\",\"spoken_languages_swahili\",[],{\"_62\":541,\"_361\":542,\"_363\":543,\"_56\":544},428,\"Serbian\",\"spoken_languages_serbian\",[],{\"_62\":546,\"_361\":547,\"_363\":548,\"_56\":549},423,\"Polish\",\"spoken_languages_polish\",[],{\"_62\":551,\"_361\":552,\"_363\":553,\"_56\":554},411,\"Italian\",\"spoken_languages_italian\",[],{\"_62\":556,\"_361\":557,\"_363\":558,\"_56\":559},392,\"Azerbaijani\",\"spoken_languages_azerbaijani\",[],{\"_62\":561,\"_361\":562,\"_363\":563,\"_56\":564},435,\"Swedish\",\"spoken_languages_swedish\",[],{\"_62\":566,\"_361\":567,\"_363\":568,\"_56\":569},422,\"Persian/Farsi\",\"spoken_languages_persian\",[],{\"_62\":571,\"_361\":572,\"_363\":573,\"_56\":574},410,\"Hungarian\",\"spoken_languages_hungarian\",[],{\"_62\":576,\"_361\":577,\"_363\":578,\"_56\":579},405,\"French\",\"spoken_languages_french\",[],{\"_62\":581,\"_361\":582,\"_363\":583,\"_56\":584},404,\"Finnish\",\"spoken_languages_finnish\",[],{\"_62\":586,\"_361\":587,\"_363\":588,\"_56\":589},403,\"English\",\"spoken_languages_english\",[],{\"_62\":591,\"_361\":592,\"_363\":593,\"_56\":594},396,\"Bulgarian\",\"spoken_languages_bulgarian\",[],{\"_62\":596,\"_361\":597,\"_363\":598,\"_56\":599},399,\"Croatian\",\"spoken_languages_croatian\",[],{\"_62\":601,\"_361\":602,\"_363\":603,\"_56\":604},398,\"Catalan\",\"spoken_languages_catalan\",[],{\"_62\":606,\"_361\":607,\"_363\":608,\"_56\":609},442,\"Urdu\",\"spoken_languages_urdu\",[],{\"_62\":611,\"_361\":612,\"_363\":613,\"_56\":614},440,\"Turkish\",\"spoken_languages_turkish\",[],{\"_62\":616,\"_361\":617,\"_363\":618,\"_56\":619},439,\"Thai\",\"spoken_languages_thai\",[],{\"_62\":621,\"_361\":622,\"_363\":623,\"_56\":624},436,\"Tagalog\",\"spoken_languages_tagalog\",[],{\"_62\":626,\"_361\":627,\"_363\":628,\"_56\":629},394,\"Bengali\",\"spoken_languages_bengali\",[],{\"_62\":631,\"_361\":632,\"_363\":633,\"_56\":634},432,\"Somali\",\"spoken_languages_somali\",[],{\"_62\":636,\"_361\":637,\"_363\":638,\"_56\":639},414,\"Kazakh\",\"spoken_languages_kazakh\",[],{\"_62\":641,\"_361\":642,\"_363\":643,\"_56\":644},413,\"Kannada\",\"spoken_languages_kannada\",[],{\"_62\":646,\"_361\":647,\"_363\":648,\"_56\":649},393,\"Belarusian\",\"spoken_languages_belarusian\",[],{\"_62\":651,\"_361\":652,\"_363\":653,\"_56\":654},429,\"Sinhalese\",\"spoken_languages_sinhalese\",[],{\"_62\":656,\"_361\":657,\"_363\":658,\"_56\":659},418,\"Malay\",\"spoken_languages_malay\",[],{\"_62\":661,\"_361\":662,\"_363\":663,\"_56\":664},415,\"Korean\",\"spoken_languages_korean\",[],{\"_62\":666,\"_361\":667,\"_363\":668,\"_56\":669},412,\"Japanese\",\"spoken_languages_japanese\",[],{\"_62\":671,\"_361\":672,\"_363\":673,\"_56\":674},391,\"Arabic\",\"spoken_languages_arabic\",[],{\"_62\":676,\"_361\":677,\"_363\":678,\"_56\":679},425,\"Punjabi\",\"spoken_languages_punjabi\",[],\"compound_villa_plot\",{\"_62\":682,\"_357\":683},240,[684,689,694],{\"_62\":685,\"_361\":686,\"_363\":687,\"_56\":688},241,\"Plot with a house\",\"compound_villa_plot_with_house\",[],{\"_62\":690,\"_361\":691,\"_363\":692,\"_56\":693},242,\"Land without a house\",\"compound_villa_plot_without_house\",[],{\"_62\":695,\"_361\":696,\"_363\":697,\"_56\":698},243,\"Individual project\",\"compound_villa_plot_individual_project\",[],\"payment_plan_when\",{\"_62\":701,\"_357\":702},349,[703,708,713,718,723,728,733,738,743,748],{\"_62\":704,\"_361\":705,\"_363\":706,\"_56\":707},350,\"At booking\",\"payment_plan_when_booking\",[],{\"_62\":709,\"_361\":710,\"_363\":711,\"_56\":712},351,\"Exact Date\",\"payment_plan_when_exact_date\",[],{\"_62\":714,\"_361\":715,\"_363\":716,\"_56\":717},352,\"Period after booking\",\"payment_plan_when_days_after_booking\",[],{\"_62\":719,\"_361\":720,\"_363\":721,\"_56\":722},353,\"Completion rate\",\"payment_plan_when_completion_rate\",[],{\"_62\":724,\"_361\":725,\"_363\":726,\"_56\":727},354,\"Handover\",\"payment_plan_when_handover\",[],{\"_62\":729,\"_361\":730,\"_363\":731,\"_56\":732},355,\"Days after handover\",\"payment_plan_when_days_after_handover\",[],{\"_62\":734,\"_361\":735,\"_363\":736,\"_56\":737},382,\"Tax Fee\",\"payment_plan_when_tax_fee\",[],{\"_62\":739,\"_361\":740,\"_363\":741,\"_56\":742},381,\"Down payment\",\"payment_plan_when_down_payment\",[],{\"_62\":744,\"_361\":745,\"_363\":746,\"_56\":747},383,\"Extra Fee\",\"payment_plan_when_extra_fee\",[],{\"_62\":749,\"_361\":750,\"_363\":751,\"_56\":752},465,\"Period after down payment\",\"payment_plan_when_days_after_down_payment\",[],\"selection_status\",{\"_62\":755,\"_357\":756},181,[757,762,767],{\"_62\":758,\"_361\":759,\"_363\":760,\"_56\":761},184,\"Removed\",\"selection_status_removed\",[],{\"_62\":763,\"_361\":764,\"_363\":765,\"_56\":766},183,\"Completed\",\"selection_status_completed\",[],{\"_62\":768,\"_361\":769,\"_363\":770,\"_56\":771},182,\"In progress\",\"selection_status_progress\",[],\"unit_media_type\",{\"_62\":774,\"_357\":775},363,[776,781],{\"_62\":777,\"_361\":778,\"_363\":779,\"_56\":780},364,\"Photo\",\"unit_media_type_photo\",[],{\"_62\":782,\"_361\":783,\"_363\":784,\"_56\":785},365,\"Plan\",\"unit_media_type_plan\",[],\"booking_status\",{\"_62\":788,\"_357\":789},368,[790,795,800,805,810,814,819],{\"_62\":791,\"_361\":792,\"_363\":793,\"_56\":794},375,\"Commission payed\",\"booking_status_commission_payed\",[],{\"_62\":796,\"_361\":797,\"_363\":798,\"_56\":799},369,\"Created\",\"booking_status_created\",[],{\"_62\":801,\"_361\":802,\"_363\":803,\"_56\":804},374,\"Waiting for commission payment\",\"booking_status_waiting_commission_payed\",[],{\"_62\":806,\"_361\":807,\"_363\":808,\"_56\":809},373,\"Booked\",\"booking_status_booked\",[],{\"_62\":811,\"_361\":740,\"_363\":812,\"_56\":813},372,\"booking_status_down_payment\",[],{\"_62\":815,\"_361\":816,\"_363\":817,\"_56\":818},370,\"Fixed\",\"booking_status_fixed\",[],{\"_62\":820,\"_361\":821,\"_363\":822,\"_56\":823},371,\"Booking payment\",\"booking_status_booking_payment\",[],\"unit_furnishing\",{\"_62\":826,\"_357\":827},330,[828,833,838],{\"_62\":829,\"_361\":830,\"_363\":831,\"_56\":832},331,\"Furnished\",\"unit_furnishing_furnished\",[],{\"_62\":834,\"_361\":835,\"_363\":836,\"_56\":837},332,\"Unfurnished\",\"unit_furnishing_unfurnished\",[],{\"_62\":839,\"_361\":840,\"_363\":841,\"_56\":842},333,\"Partially furnished\",\"unit_furnishings_partly_furnished\",[],\"project_gallery_category\",{\"_62\":845,\"_357\":846},92,[847,852,857,862,867],{\"_62\":848,\"_361\":849,\"_363\":850,\"_56\":851},107,\"Project presentation\",\"project_gallery_category_presentation\",[],{\"_62\":853,\"_361\":854,\"_363\":855,\"_56\":856},93,\"Construction progress\",\"project_gallery_category_construction_progress\",[],{\"_62\":858,\"_361\":859,\"_363\":860,\"_56\":861},94,\"Finishing examples\",\"project_gallery_category_finishing_examples\",[],{\"_62\":863,\"_361\":864,\"_363\":865,\"_56\":866},95,\"Infrastructure\",\"project_gallery_category_infrastructure\",[],{\"_62\":868,\"_361\":869,\"_363\":870,\"_56\":871},96,\"View\",\"project_gallery_category_view\",[],\"booking_payment_method\",{\"_62\":874,\"_357\":875},376,[876,881,886,891],{\"_62\":877,\"_361\":878,\"_363\":879,\"_56\":880},378,\"Card not Russia\",\"booking_status_card_not_russia\",[],{\"_62\":882,\"_361\":883,\"_363\":884,\"_56\":885},380,\"Other\",\"booking_status_other\",[],{\"_62\":887,\"_361\":888,\"_363\":889,\"_56\":890},379,\"Card of Russia\",\"booking_status_card_russia\",[],{\"_62\":892,\"_361\":893,\"_363\":894,\"_56\":895},377,\"SWIFT\",\"booking_payment_method_swift\",[],\"project_facilities\",{\"_62\":898,\"_357\":899},66,[900,907,913,919,925,931,937,943,949,955],{\"_62\":901,\"_361\":902,\"_363\":903,\"_56\":904},457,\"Branded\",\"project_facilities_branded\",{\"_905\":906},\"icon\",\"branded\",{\"_62\":908,\"_361\":909,\"_363\":910,\"_56\":911},153,\"Concierge Service\",\"project_facilities_concierge\",{\"_905\":912},\"concierge_service\",{\"_62\":914,\"_361\":915,\"_363\":916,\"_56\":917},286,\"Pets Allowed\",\"project_facilities_pets\",{\"_905\":918},\"pets\",{\"_62\":920,\"_361\":921,\"_363\":922,\"_56\":923},281,\"Kids Play Area\",\"project_facilities_kids\",{\"_905\":924},\"kids_play_area\",{\"_62\":926,\"_361\":927,\"_363\":928,\"_56\":929},156,\"Electric Car Charger\",\"project_facilities_car_charger\",{\"_905\":930},\"electric_car_charger\",{\"_62\":932,\"_361\":933,\"_363\":934,\"_56\":935},446,\"Free A/C\",\"project_facilities_free_ac\",{\"_905\":936},\"central_ac\",{\"_62\":938,\"_361\":939,\"_363\":940,\"_56\":941},447,\"Private beach\",\"project_facilities_private_beach\",{\"_905\":942},\"private_beach\",{\"_62\":944,\"_361\":945,\"_363\":946,\"_56\":947},448,\"Short-term rental forbidden\",\"project_facilities_short_rental_forbidden\",{\"_905\":948},\"short_rental_forbidden\",{\"_62\":950,\"_361\":951,\"_363\":952,\"_56\":953},463,\"Private pool\",\"project_facilities_private_pool\",{\"_905\":954},\"private_pool\",{\"_62\":956,\"_361\":957,\"_363\":958,\"_56\":959},464,\"Maid/Study room\",\"project_facilities_maid_room\",{\"_905\":960},\"maid_room\",\"project_media_type\",{\"_62\":963,\"_357\":964},358,[965],{\"_62\":966,\"_361\":967,\"_363\":968,\"_56\":969},359,\"Presentation\",\"project_media_type_presentation\",[],\"rooms\",{\"_62\":972,\"_357\":973},109,[974,981,987,993,999,1005,1011,1017,1023,1029,1035,1041],{\"_62\":975,\"_361\":976,\"_363\":977,\"_56\":978},110,\"Studio\",\"rooms_studio\",{\"_979\":980},\"short_value\",\"St\",{\"_62\":982,\"_361\":983,\"_363\":984,\"_56\":985},111,\"1 BR\",\"rooms_1\",{\"_979\":986},\"1BR\",{\"_62\":988,\"_361\":989,\"_363\":990,\"_56\":991},112,\"2 BR\",\"rooms_2\",{\"_979\":992},\"2BR\",{\"_62\":994,\"_361\":995,\"_363\":996,\"_56\":997},113,\"3 BR\",\"rooms_3\",{\"_979\":998},\"3BR\",{\"_62\":1000,\"_361\":1001,\"_363\":1002,\"_56\":1003},114,\"4 BR\",\"rooms_4\",{\"_979\":1004},\"4BR\",{\"_62\":1006,\"_361\":1007,\"_363\":1008,\"_56\":1009},115,\"5 BR\",\"rooms_5\",{\"_979\":1010},\"5BR\",{\"_62\":1012,\"_361\":1013,\"_363\":1014,\"_56\":1015},341,\"6 BR\",\"rooms_6\",{\"_979\":1016},\"6BR\",{\"_62\":1018,\"_361\":1019,\"_363\":1020,\"_56\":1021},342,\"7 BR\",\"rooms_7\",{\"_979\":1022},\"7BR\",{\"_62\":1024,\"_361\":1025,\"_363\":1026,\"_56\":1027},343,\"8 BR\",\"rooms_8\",{\"_979\":1028},\"8BR\",{\"_62\":1030,\"_361\":1031,\"_363\":1032,\"_56\":1033},344,\"9 BR\",\"rooms_9\",{\"_979\":1034},\"9BR\",{\"_62\":1036,\"_361\":1037,\"_363\":1038,\"_56\":1039},345,\"10 BR\",\"rooms_10\",{\"_979\":1040},\"10BR\",{\"_62\":1042,\"_361\":1043,\"_363\":1044,\"_56\":1045},164,\"NA\",\"rooms_n\",{\"_979\":1046},\"Free\",\"unit_type_room\",{\"_62\":1049,\"_357\":1050},137,[1051,1056,1061,1066,1071,1076,1081,1086,1091],{\"_62\":1052,\"_361\":1053,\"_363\":1054,\"_56\":1055},138,\"Apartment\",\"unit_type_room_unit\",[],{\"_62\":1057,\"_361\":1058,\"_363\":1059,\"_56\":1060},462,\"Villa\",\"unit_type_room_villa\",[],{\"_62\":1062,\"_361\":1063,\"_363\":1064,\"_56\":1065},445,\"Townhouse\",\"unit_type_room_townhouse\",[],{\"_62\":1067,\"_361\":1068,\"_363\":1069,\"_56\":1070},140,\"Duplex\",\"unit_type_room_duplex\",[],{\"_62\":1072,\"_361\":1073,\"_363\":1074,\"_56\":1075},362,\"Triplex\",\"unit_type_room_triplex\",[],{\"_62\":1077,\"_361\":1078,\"_363\":1079,\"_56\":1080},357,\"Penthouse\",\"unit_type_room_penthouse\",[],{\"_62\":1082,\"_361\":1083,\"_363\":1084,\"_56\":1085},141,\"Villa in project\",\"unit_type_room_villa_in_project\",[],{\"_62\":1087,\"_361\":1088,\"_363\":1089,\"_56\":1090},346,\"Retail\",\"unit_type_room_retail\",[],{\"_62\":1092,\"_361\":1093,\"_363\":1094,\"_56\":1095},347,\"Office\",\"unit_type_room_office\",[],\"unit_status\",{\"_62\":1098,\"_357\":1099},143,[1100,1106,1112],{\"_62\":1101,\"_361\":807,\"_363\":1102,\"_56\":1103},146,\"unit_status_reservation\",{\"_1104\":1105},\"bg_color\",\"#8e8e8d\",{\"_62\":1107,\"_361\":1108,\"_363\":1109,\"_56\":1110},145,\"Sold\",\"unit_status_sold\",{\"_1104\":1111},\"#DD6465\",{\"_62\":1113,\"_361\":1114,\"_363\":1115,\"_56\":1116},144,\"On sale\",\"unit_status_sale\",{\"_1104\":1117},\"#20CB6A\",\"project_badges\",{\"_62\":1120,\"_357\":1121},103,[1122,1130,1135,1142,1148,1157,1163,1169,1174],{\"_62\":1123,\"_361\":1124,\"_363\":1125,\"_56\":1126},444,\"Exclusive\",\"badge_exclusive\",{\"_1104\":1127,\"_905\":59,\"_1128\":1129},\"#dfe4ff\",\"color\",\"#4F5FD9\",{\"_62\":1131,\"_361\":1132,\"_363\":1133,\"_56\":1134},366,\"Recommended\",\"badge_recommend\",{\"_1104\":1127,\"_1128\":1129},{\"_62\":1136,\"_361\":1137,\"_363\":1138,\"_56\":1139},385,\"Announcement\",\"badge_announcement\",{\"_1104\":1140,\"_1128\":1141},\"#fff1f2\",\"#d74652\",{\"_62\":1143,\"_361\":1144,\"_363\":1145,\"_56\":1146},361,\"Presale (EOI)\",\"badge_early_booking\",{\"_1104\":1147,\"_1128\":1117},\"#E9FAF0\",{\"_62\":1149,\"_361\":1150,\"_363\":1151,\"_56\":1152},163,\"Launch\",\"badge_start_sale\",{\"_1104\":1153,\"_1154\":1155,\"_1128\":1156},\"#fffff1\",\"remove_days\",\"14\",\"#c88902\",{\"_62\":1158,\"_361\":1159,\"_363\":1160,\"_56\":1161},161,\"Sold Out\",\"badge_sold_out\",{\"_1104\":1140,\"_1128\":1162},\"#ff0015\",{\"_62\":1164,\"_361\":1165,\"_363\":1166,\"_56\":1167},105,\"Free Installment\",\"badge_free_installment\",{\"_1104\":1168,\"_1128\":1162},\"#e7efff\",{\"_62\":1170,\"_361\":1171,\"_363\":1172,\"_56\":1173},384,\"Prelaunch\",\"badge_prelaunch_sale\",{\"_1104\":1140,\"_1128\":1162},{\"_62\":1175,\"_361\":1176,\"_363\":1177,\"_56\":1178},386,\"Price on request\",\"badge_price_on_request\",{\"_1104\":1179,\"_1128\":1180},\"#e6fafa\",\"#14a1a2\",\"development_stage\",{\"_62\":1183,\"_357\":1184},116,[1185,1190,1195,1200],{\"_62\":1186,\"_361\":1187,\"_363\":1188,\"_56\":1189},117,\"Scheduled\",\"development_stage_scheduled\",[],{\"_62\":1191,\"_361\":1192,\"_363\":1193,\"_56\":1194},120,\"Ready\",\"development_stage_finished\",[],{\"_62\":1196,\"_361\":1197,\"_363\":1198,\"_56\":1199},119,\"Stopped\",\"development_stage_stopped\",[],{\"_62\":1201,\"_361\":1202,\"_363\":1203,\"_56\":1204},348,\"In Progress\",\"development_stage_progress\",[],\"project_sales_status\",{\"_62\":120,\"_357\":1207},[1208,1212,1216,1223,1226,1232],{\"_62\":1209,\"_361\":1137,\"_363\":1210,\"_56\":1211},335,\"project_sales_status_announcement\",{\"_1128\":1141,\"_1104\":1140},{\"_62\":1213,\"_361\":1144,\"_363\":1214,\"_56\":1215},338,\"project_sales_status_eoi\",{\"_1128\":1117,\"_1104\":1147},{\"_62\":1217,\"_361\":1150,\"_363\":1218,\"_56\":1219},360,\"project_sales_status_new_launch\",{\"_1154\":1155,\"_1220\":1221,\"_1128\":1156,\"_1104\":1222},\"next_value\",\"project_sales_status_on_sale\",\"#fff6e9\",{\"_62\":97,\"_361\":1224,\"_363\":1221,\"_56\":1225},\"On Sale\",{\"_1128\":1117,\"_1104\":1147},{\"_62\":1227,\"_361\":1159,\"_363\":1228,\"_56\":1229},339,\"project_sales_status_sold_out\",{\"_1128\":1230,\"_1104\":1231},\"#12122D\",\"#DFDFEB\",{\"_62\":1233,\"_361\":1234,\"_363\":1235,\"_56\":1236},334,\"Pending\",\"project_sales_status_pending\",{\"_1128\":1162,\"_1104\":1140},\"currencies\",{\"_48\":1239},{\"_182\":1240,\"_144\":1241,\"_1242\":1243,\"_1244\":1245,\"_1246\":1247,\"_123\":1248,\"_1249\":1250,\"_1251\":1252,\"_106\":1253,\"_1254\":1255,\"_161\":1256},0.2310371419,0.1983360782,\"IDR\",4411.8487326964,\"INR\",23.325521015,\"JPY\",39.158099095,0.1045301587,\"PKR\",77.2687860817,\"RUB\",21.2937659993,8.8311349764,\"TRY\",10.846529803,0.2723273941,\"languages\",{\"_1259\":1260,\"_1266\":1267,\"_40\":1270,\"_1273\":1274,\"_1277\":1278,\"_1281\":1282,\"_1286\":1287,\"_1290\":1291,\"_1295\":1296,\"_1299\":1300,\"_1303\":1304,\"_1307\":1308},\"ar\",{\"_58\":1261,\"_1262\":1263,\"_1264\":1265},\"AR: العربية\",\"native\",\"العربية\",\"flag\",\"ae\",\"de\",{\"_58\":1268,\"_1262\":1269,\"_1264\":1266},\"DE: Deutsch\",\"Deutsch\",{\"_58\":1271,\"_1262\":587,\"_1264\":1272},\"EN: English\",\"gb\",\"es\",{\"_58\":1275,\"_1262\":1276,\"_1264\":1273},\"ES: Español\",\"Español\",\"fr\",{\"_58\":1279,\"_1262\":1280,\"_1264\":1277},\"FR: Français\",\"Français\",\"hi\",{\"_58\":1283,\"_1262\":1284,\"_1264\":1285},\"HI: Hindi\",\"हिन्दी\",\"in\",\"it\",{\"_58\":1288,\"_1262\":1289,\"_1264\":1286},\"IT: Italiano\",\"Italiano\",\"ja\",{\"_58\":1292,\"_1262\":1293,\"_1264\":1294},\"JA: Japanese\",\"日本語\",\"jp\",\"pt\",{\"_58\":1297,\"_1262\":1298,\"_1264\":1295},\"PT: Português\",\"Português\",\"ru\",{\"_58\":1301,\"_1262\":1302,\"_1264\":1299},\"RU: Русский\",\"Русский\",\"tr\",{\"_58\":1305,\"_1262\":1306,\"_1264\":1303},\"TR: Türkçe\",\"Türkçe\",\"zh\",{\"_58\":1309,\"_1262\":1310,\"_1264\":1311},\"ZH: Chinese\",\"中文\",\"cn\",\"statistics\",{\"_1314\":1315,\"_1316\":1317},\"projects\",2266,\"units\",698147,\"priceRanges\",[1320,1327,1330,1333,1336,1339,1342,1345,1348,1351,1354,1357,1360,1363,1366,1369,1372,1374,1376,1378,1380,1382,1385,1388,1391,1394,1396,1399,1402,1404],{\"_1321\":1322,\"_1323\":1324,\"_1325\":1326},\"count\",247,\"from\",382300,\"to\",577105,{\"_1321\":1328,\"_1323\":1326,\"_1325\":1329},1233,771910,{\"_1321\":1331,\"_1323\":1329,\"_1325\":1332},1582,966715,{\"_1321\":1334,\"_1323\":1332,\"_1325\":1335},2480,1161520,{\"_1321\":1337,\"_1323\":1335,\"_1325\":1338},2275,1356325,{\"_1321\":1340,\"_1323\":1338,\"_1325\":1341},1426,1551130,{\"_1321\":1343,\"_1323\":1341,\"_1325\":1344},1663,1745935,{\"_1321\":1346,\"_1323\":1344,\"_1325\":1347},1684,1940740,{\"_1321\":1349,\"_1323\":1347,\"_1325\":1350},1857,2135545,{\"_1321\":1352,\"_1323\":1350,\"_1325\":1353},1344,2330350,{\"_1321\":1355,\"_1323\":1353,\"_1325\":1356},715,2525155,{\"_1321\":1358,\"_1323\":1356,\"_1325\":1359},1130,2719960,{\"_1321\":1361,\"_1323\":1359,\"_1325\":1362},618,2914765,{\"_1321\":1364,\"_1323\":1362,\"_1325\":1365},512,3109570,{\"_1321\":1367,\"_1323\":1365,\"_1325\":1368},579,3304375,{\"_1321\":1370,\"_1323\":1368,\"_1325\":1371},516,3499180,{\"_1321\":874,\"_1323\":1371,\"_1325\":1373},3693985,{\"_1321\":536,\"_1323\":1373,\"_1325\":1375},3888790,{\"_1321\":451,\"_1323\":1375,\"_1325\":1377},4083595,{\"_1321\":401,\"_1323\":1377,\"_1325\":1379},4278400,{\"_1321\":1030,\"_1323\":1379,\"_1325\":1381},4473205,{\"_1321\":1383,\"_1323\":1381,\"_1325\":1384},263,4668010,{\"_1321\":1386,\"_1323\":1384,\"_1325\":1387},192,4862815,{\"_1321\":1389,\"_1323\":1387,\"_1325\":1390},126,5057620,{\"_1321\":1392,\"_1323\":1390,\"_1325\":1393},134,5252425,{\"_1321\":845,\"_1323\":1393,\"_1325\":1395},5447230,{\"_1321\":1397,\"_1323\":1395,\"_1325\":1398},90,5642035,{\"_1321\":1400,\"_1323\":1398,\"_1325\":1401},54,5836840,{\"_1321\":898,\"_1323\":1401,\"_1325\":1403},6031645,{\"_1321\":1405,\"_1323\":1403,\"_1325\":1406},2617,340500000,\"priceAreaRanges\",[1409,1412,1414,1416,1419,1421,1424,1427,1430,1433,1436,1439,1442,1445,1448,1451,1454,1457,1460,1463,1466,1469,1472,1475,1478,1481,1484,1487,1490,1493],{\"_1321\":120,\"_1323\":1410,\"_1325\":1411},\"1177.0\",\"2683.0\",{\"_1321\":90,\"_1323\":1411,\"_1325\":1413},\"4189.0\",{\"_1321\":108,\"_1323\":1413,\"_1325\":1415},\"5695.0\",{\"_1321\":1417,\"_1323\":1415,\"_1325\":1418},61,\"7201.0\",{\"_1321\":264,\"_1323\":1418,\"_1325\":1420},\"8707.0\",{\"_1321\":1422,\"_1323\":1420,\"_1325\":1423},152,\"10213.0\",{\"_1321\":1425,\"_1323\":1423,\"_1325\":1426},158,\"11719.0\",{\"_1321\":1428,\"_1323\":1426,\"_1325\":1429},686,\"13225.0\",{\"_1321\":1431,\"_1323\":1429,\"_1325\":1432},1454,\"14731.0\",{\"_1321\":1434,\"_1323\":1432,\"_1325\":1435},2602,\"16237.0\",{\"_1321\":1437,\"_1323\":1435,\"_1325\":1438},2630,\"17743.0\",{\"_1321\":1440,\"_1323\":1438,\"_1325\":1441},2235,\"19249.0\",{\"_1321\":1443,\"_1323\":1441,\"_1325\":1444},1792,\"20755.0\",{\"_1321\":1446,\"_1323\":1444,\"_1325\":1447},2302,\"22261.0\",{\"_1321\":1449,\"_1323\":1447,\"_1325\":1450},1264,\"23767.0\",{\"_1321\":1452,\"_1323\":1450,\"_1325\":1453},977,\"25273.0\",{\"_1321\":1455,\"_1323\":1453,\"_1325\":1456},1281,\"26779.0\",{\"_1321\":1458,\"_1323\":1456,\"_1325\":1459},1278,\"28285.0\",{\"_1321\":1461,\"_1323\":1459,\"_1325\":1462},1153,\"29791.0\",{\"_1321\":1464,\"_1323\":1462,\"_1325\":1465},1095,\"31297.0\",{\"_1321\":1467,\"_1323\":1465,\"_1325\":1468},529,\"32803.0\",{\"_1321\":1470,\"_1323\":1468,\"_1325\":1471},325,\"34309.0\",{\"_1321\":1473,\"_1323\":1471,\"_1325\":1474},265,\"35815.0\",{\"_1321\":1476,\"_1323\":1474,\"_1325\":1477},274,\"37321.0\",{\"_1321\":1479,\"_1323\":1477,\"_1325\":1480},251,\"38827.0\",{\"_1321\":1482,\"_1323\":1480,\"_1325\":1483},261,\"40333.0\",{\"_1321\":1485,\"_1323\":1483,\"_1325\":1486},166,\"41839.0\",{\"_1321\":1488,\"_1323\":1486,\"_1325\":1489},136,\"43345.0\",{\"_1321\":1491,\"_1323\":1489,\"_1325\":1492},82,\"44851.0\",{\"_1321\":1494,\"_1323\":1495,\"_1325\":1496},1986,44851,2766535,\"permission\",[1499,1506,1509,1511,1514,1517,1520,1523,1526,1529,1532,1535,1538,1541,1544],{\"_73\":1500,\"_58\":1501,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"villas_list\",\"Villas List\",\"is_create\",\"is_read\",\"is_update\",\"is_delete\",{\"_73\":1507,\"_58\":1508,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"villas_plan\",\"Master Plan (Villas)\",{\"_73\":34,\"_58\":1510,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"Transactions\",{\"_73\":1512,\"_58\":1513,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"construction_information\",\"Extended Construction Progress Information\",{\"_73\":1515,\"_58\":1516,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"client_mode\",\"Client Mode\",{\"_73\":1518,\"_58\":1519,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"booking\",\"Booking\",{\"_73\":1521,\"_58\":1522,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"brochure\",\"Brochures\",{\"_73\":1524,\"_58\":1525,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"assignment\",\"Secondary\",{\"_73\":1527,\"_58\":1528,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"units_list\",\"Units List\",{\"_73\":1530,\"_58\":1531,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"units_chess\",\"Chessboard\",{\"_73\":1533,\"_58\":1534,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"units_floor_plan\",\"Floor Layout\",{\"_73\":1536,\"_58\":1537,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"sales_selection_share\",\"Sales Offer Share\",{\"_73\":1539,\"_58\":1540,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"promotions\",\"Promotions\",{\"_73\":1542,\"_58\":1543,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"sales_selection_selections\",\"Sales Offer Collections\",{\"_73\":1545,\"_58\":1546,\"_1502\":55,\"_1503\":55,\"_1504\":55,\"_1505\":55},\"builder_contacts\",\"Developer Contacts\",\"user\",\"invites\",[],\"onboarding\",[],\"cityCurrency\",\"city_id\",\"isAuthenticated\",\"client_mode_is_active\",\"client_mode_settings\",{\"_1558\":145,\"_1559\":145,\"_1560\":145,\"_1561\":145,\"_1562\":145,\"_1563\":145,\"_1564\":145,\"_1565\":145,\"_1566\":145,\"_1567\":145,\"_1568\":145},\"sales_offices\",\"exclusive_filter\",\"cooperation_terms\",\"private_description\",\"show_onbording\",\"agent_contacts\",\"promoted_projects\",\"completion_progress\",\"builder\",\"promo_for_brokers\",\"brokers_commission\",\"actionData\",\"errors\"]\n");
    </script>
    <!--$?-->
    <template id="B:1"></template>
    <!--/$-->
</div>
<script>
    $RC = function(b, c, e) {
        c = document.getElementById(c);
        c.parentNode.removeChild(c);
        var a = document.getElementById(b);
        if (a) {
            b = a.previousSibling;
            if (e)
                b.data = "$!",
                    a.setAttribute("data-dgst", e);
            else {
                e = b.parentNode;
                a = b.nextSibling;
                var f = 0;
                do {
                    if (a && 8 === a.nodeType) {
                        var d = a.data;
                        if ("/$" === d)
                            if (0 === f)
                                break;
                            else
                                f--;
                        else
                            "$" !== d && "$?" !== d && "$!" !== d || f++
                    }
                    d = a.nextSibling;
                    e.removeChild(a);
                    a = d
                } while (a);
                for (; c.firstChild; )
                    e.insertBefore(c.firstChild, a);
                b.data = "$"
            }
            b._reactRetry && b._reactRetry()
        }
    }
    ;
    $RC("B:0", "S:0")
</script>
<div hidden id="S:1">
    <script>
        window.__reactRouterContext.streamController.close();
    </script>
</div>
<script>
    $RC("B:1", "S:1")
</script>
</body>
</html>
