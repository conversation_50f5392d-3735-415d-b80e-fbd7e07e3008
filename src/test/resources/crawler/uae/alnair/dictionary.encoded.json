[{"_1": 2, "_2264": -5, "_2265": -5}, "loaderData", {"_3": 4, "_68": 69, "_1569": 1570}, "root", {"_5": 6, "_39": 40, "_41": 42, "_43": 44, "_45": -5, "_46": -5, "_47": 48, "_49": 50, "_51": 52, "_53": 54, "_66": 67}, "initNamespaces", [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38], "album", "auth", "billing", "bookUnit", "constants", "countryNames", "errorsPage", "featureAnnouncement", "feedbackForm", "filter", "footer", "forAgenciesPage", "header", "inventory", "landingPage", "layoutPage", "map", "paymentPlan", "profile", "project", "projectCard", "promotion", "salesOffices", "search", "selection", "selectionUnit", "sort", "transactions", "ui", "unit", "validation", "zod", "language", "en", "t", ["SingleFetchFallback"], "body", ["SingleFetchFallback"], "token", "token_expires_at", "currency", "AED", "measure", "sqm", "city", 1, "whitelabel", {"_55": 56, "_57": 58}, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, "data", {"_59": 60, "_61": 62}, "title", "<PERSON><PERSON><PERSON>", "logo", {"_63": 52, "_64": 65, "_61": 65}, "id", "src", "/favicon.png", "origin", "http://alnair.ae", "routes/layouts/app_layout", {"_70": 71, "_1237": 1238, "_47": 48, "_1552": 48, "_49": 50, "_39": 40, "_1553": 52, "_1548": -5, "_1554": 56, "_45": -5, "_46": -5, "_1547": -5, "_1497": -5, "_1555": 56, "_1556": 1557, "_53": 54}, "info", {"_51": 72, "_83": 84, "_333": 334, "_352": 353, "_1237": 1238, "_1257": 1258, "_1312": 1313, "_1318": 1319, "_1407": 1408, "_1497": 1498, "_1547": -5, "_1548": 1549, "_1550": 1551}, {"_63": 52, "_73": 74, "_75": 76, "_77": 78, "_79": 80, "_47": 48, "_81": 56, "_82": 52}, "name", "Dubai", "country_id", 2, "latitude", "25.15433020", "longitude", "55.26127900", "is_private", "is_permit_number", "cities", [85, 86, 91, 96, 101, 107, 112, 117, 124, 129, 134, 139, 146, 151, 156, 162, 167, 172, 177, 183, 188, 193, 198, 203, 208, 213, 218, 223, 228, 233, 238, 243, 248, 253, 258, 263, 268, 273, 278, 282, 287, 292, 297, 302, 308, 313, 318, 323, 328], {"_63": 52, "_73": 74, "_75": 76, "_77": 78, "_79": 80, "_47": 48, "_81": 56, "_82": 52}, {"_63": 76, "_73": 87, "_75": 76, "_77": 88, "_79": 89, "_47": 48, "_81": 56, "_82": 90}, "Abu Dhabi", "24.39871220", "54.47347640", 0, {"_63": 92, "_73": 93, "_75": 76, "_77": 94, "_79": 95, "_47": 48, "_81": 56, "_82": 90}, 3, "Sharjah", "25.34447620", "55.40548330", {"_63": 97, "_73": 98, "_75": 76, "_77": 99, "_79": 100, "_47": 48, "_81": 56, "_82": 90}, 5, "<PERSON><PERSON>", "25.77862550", "55.95320280", {"_63": 102, "_73": 103, "_75": 92, "_77": 104, "_79": 105, "_47": 106, "_81": 56, "_82": 90}, 6, "Phuke<PERSON>", "7.87897800", "98.39839200", "THB", {"_63": 108, "_73": 109, "_75": 76, "_77": 110, "_79": 111, "_47": 48, "_81": 56, "_82": 90}, 7, "<PERSON><PERSON><PERSON>", "25.40327750", "55.47766910", {"_63": 113, "_73": 114, "_75": 92, "_77": 115, "_79": 116, "_47": 106, "_81": 56, "_82": 90}, 8, "<PERSON><PERSON>", "9.49987710", "99.93911410", {"_63": 118, "_73": 119, "_75": 120, "_77": 121, "_79": 122, "_47": 123, "_81": 56, "_82": 90}, 9, "Muscat", 4, "23.61018270", "58.59334350", "OMR", {"_63": 125, "_73": 126, "_75": 120, "_77": 127, "_79": 128, "_47": 123, "_81": 56, "_82": 90}, 10, "<PERSON><PERSON><PERSON>", "17.01570000", "54.09240000", {"_63": 130, "_73": 131, "_75": 120, "_77": 132, "_79": 133, "_47": 123, "_81": 56, "_82": 90}, 11, "<PERSON><PERSON><PERSON>", "19.57340000", "57.63320000", {"_63": 135, "_73": 136, "_75": 92, "_77": 137, "_79": 138, "_47": 106, "_81": 56, "_82": 90}, 12, "Bangkok", "13.75630000", "100.50180000", {"_63": 140, "_73": 141, "_75": 97, "_77": 142, "_79": 143, "_47": 144, "_81": 145, "_82": 90}, 13, "London (Intermark)", "51.49582475", "-0.09205173", "GBP", true, {"_63": 147, "_73": 148, "_75": 76, "_77": 149, "_79": 150, "_47": 48, "_81": 56, "_82": 90}, 14, "<PERSON><PERSON>", "25.54917200", "55.54931700", {"_63": 152, "_73": 153, "_75": 92, "_77": 154, "_79": 155, "_47": 106, "_81": 56, "_82": 90}, 15, "<PERSON><PERSON><PERSON>", "12.92360000", "100.88280000", {"_63": 157, "_73": 158, "_75": 102, "_77": 159, "_79": 160, "_47": 161, "_81": 56, "_82": 90}, 16, "Bali", "-8.34050000", "115.09200000", "USD", {"_63": 163, "_73": 164, "_75": 92, "_77": 165, "_79": 166, "_47": 106, "_81": 56, "_82": 90}, 17, "<PERSON>", "18.70610000", "98.98170000", {"_63": 168, "_73": 169, "_75": 92, "_77": 170, "_79": 171, "_47": 106, "_81": 56, "_82": 90}, 18, "<PERSON><PERSON>", "12.56830000", "99.96280000", {"_63": 173, "_73": 174, "_75": 108, "_77": 175, "_79": 176, "_47": 48, "_81": 145, "_82": 90}, 19, "Qatar", "25.27698700", "51.52639800", {"_63": 178, "_73": 179, "_75": 113, "_77": 180, "_79": 181, "_47": 182, "_81": 145, "_82": 90}, 21, "Limassol", "34.70710000", "33.02260000", "EUR", {"_63": 184, "_73": 185, "_75": 113, "_77": 186, "_79": 187, "_47": 182, "_81": 145, "_82": 90}, 22, "<PERSON><PERSON><PERSON>", "34.77560000", "32.42450000", {"_63": 189, "_73": 190, "_75": 113, "_77": 191, "_79": 192, "_47": 182, "_81": 145, "_82": 90}, 23, "Larnaca", "34.91993750", "33.61554530", {"_63": 194, "_73": 195, "_75": 113, "_77": 196, "_79": 197, "_47": 182, "_81": 145, "_82": 90}, 24, "Nicosia", "35.18560000", "33.38230000", {"_63": 199, "_73": 200, "_75": 113, "_77": 201, "_79": 202, "_47": 182, "_81": 145, "_82": 90}, 25, "Famagusta", "35.11750000", "33.94000000", {"_63": 204, "_73": 205, "_75": 97, "_77": 206, "_79": 207, "_47": 144, "_81": 145, "_82": 90}, 26, "Manchester", "53.47876500", "-2.24945700", {"_63": 209, "_73": 210, "_75": 97, "_77": 211, "_79": 212, "_47": 144, "_81": 145, "_82": 90}, 27, "Birmingham", "52.47930400", "-1.89882750", {"_63": 214, "_73": 215, "_75": 97, "_77": 216, "_79": 217, "_47": 144, "_81": 145, "_82": 90}, 28, "Liverpool", "53.40631400", "-2.98152200", {"_63": 219, "_73": 220, "_75": 118, "_77": 221, "_79": 222, "_47": 182, "_81": 145, "_82": 90}, 29, "Athens", "37.98380000", "23.72750000", {"_63": 224, "_73": 225, "_75": 118, "_77": 226, "_79": 227, "_47": 182, "_81": 145, "_82": 90}, 30, "Chalkidiki", "40.24440000", "23.49970000", {"_63": 229, "_73": 230, "_75": 118, "_77": 231, "_79": 232, "_47": 182, "_81": 145, "_82": 90}, 31, "Thessaloniki", "40.64010000", "22.94440000", {"_63": 234, "_73": 235, "_75": 125, "_77": 236, "_79": 237, "_47": 182, "_81": 145, "_82": 90}, 32, "Barcelona", "41.38510000", "2.17340000", {"_63": 239, "_73": 240, "_75": 125, "_77": 241, "_79": 242, "_47": 182, "_81": 145, "_82": 90}, 33, "Madrid", "40.41680000", "-3.70380000", {"_63": 244, "_73": 245, "_75": 125, "_77": 246, "_79": 247, "_47": 182, "_81": 145, "_82": 90}, 34, "Malaga", "36.72130000", "-4.42140000", {"_63": 249, "_73": 250, "_75": 125, "_77": 251, "_79": 252, "_47": 182, "_81": 145, "_82": 90}, 35, "Benidorm", "38.54110000", "-0.12250000", {"_63": 254, "_73": 255, "_75": 125, "_77": 256, "_79": 257, "_47": 182, "_81": 145, "_82": 90}, 36, "Costa Del Sol", "36.52980000", "-4.88300000", {"_63": 259, "_73": 260, "_75": 125, "_77": 261, "_79": 262, "_47": 182, "_81": 145, "_82": 90}, 37, "Costa Blanca", "38.34520000", "-0.48150000", {"_63": 264, "_73": 265, "_75": 125, "_77": 266, "_79": 267, "_47": 182, "_81": 145, "_82": 90}, 38, "<PERSON>", "41.55060000", "2.42690000", {"_63": 269, "_73": 270, "_75": 125, "_77": 271, "_79": 272, "_47": 182, "_81": 145, "_82": 90}, 39, "<PERSON>", "41.20190000", "1.52570000", {"_63": 274, "_73": 275, "_75": 125, "_77": 276, "_79": 277, "_47": 182, "_81": 145, "_82": 90}, 40, "Costa Bravo", "41.97940000", "3.16610000", {"_63": 279, "_73": 280, "_75": 102, "_77": 159, "_79": 281, "_47": 161, "_81": 56, "_82": 90}, 41, "Gili Islands", "116.02750000", {"_63": 283, "_73": 284, "_75": 102, "_77": 285, "_79": 286, "_47": 161, "_81": 56, "_82": 90}, 42, "Lombok", "-8.65000000", "116.32000000", {"_63": 288, "_73": 289, "_75": 102, "_77": 290, "_79": 291, "_47": 161, "_81": 56, "_82": 90}, 43, "Sumbawa", "-8.65290000", "117.36160000", {"_63": 293, "_73": 294, "_75": 118, "_77": 295, "_79": 296, "_47": 182, "_81": 145, "_82": 90}, 44, "Glyfada", "37.87443866", "23.75560853", {"_63": 298, "_73": 299, "_75": 118, "_77": 300, "_79": 301, "_47": 182, "_81": 145, "_82": 90}, 45, "<PERSON><PERSON><PERSON><PERSON>", "37.38570216", "23.24543336", {"_63": 303, "_73": 304, "_75": 305, "_77": 306, "_79": 307, "_47": 161, "_81": 56, "_82": 90}, 46, "Istanbul", 157, "41.13530395", "28.98328603", {"_63": 309, "_73": 310, "_75": 305, "_77": 311, "_79": 312, "_47": 161, "_81": 56, "_82": 90}, 47, "<PERSON><PERSON>", "36.54545085", "31.99651492", {"_63": 314, "_73": 315, "_75": 305, "_77": 316, "_79": 317, "_47": 161, "_81": 56, "_82": 90}, 48, "<PERSON><PERSON><PERSON>", "36.91125691", "30.69741097", {"_63": 319, "_73": 320, "_75": 305, "_77": 321, "_79": 322, "_47": 161, "_81": 56, "_82": 90}, 49, "Mersin", "36.81173288", "34.64391483", {"_63": 324, "_73": 325, "_75": 118, "_77": 326, "_79": 327, "_47": 161, "_81": 145, "_82": 90}, 50, "<PERSON><PERSON>", "38.24563686", "21.73702184", {"_63": 329, "_73": 330, "_75": 118, "_77": 331, "_79": 332, "_47": 161, "_81": 56, "_82": 90}, 51, "Crete", "35.24753144", "24.79500397", "countries", [335, 337, 339, 341, 343, 345, 346, 348, 350], {"_63": 76, "_59": 336, "_47": 48}, "United Arab Emirates", {"_63": 92, "_59": 338, "_47": 106}, "Thailand", {"_63": 120, "_59": 340, "_47": 123}, "Oman", {"_63": 97, "_59": 342, "_47": 144}, "United Kingdom", {"_63": 102, "_59": 344, "_47": 161}, "Indonesia", {"_63": 108, "_59": 174, "_47": 48}, {"_63": 113, "_59": 347, "_47": 182}, "Cyprus South", {"_63": 118, "_59": 349, "_47": 182}, "Greece", {"_63": 125, "_59": 351, "_47": 182}, "Spain", "catalogs", {"_354": 355, "_396": 397, "_680": 681, "_699": 700, "_753": 754, "_772": 773, "_786": 787, "_824": 825, "_843": 844, "_872": 873, "_896": 897, "_961": 962, "_970": 971, "_1047": 1048, "_1096": 1097, "_1118": 1119, "_1181": 1182, "_1205": 1206}, "unit_complaints", {"_63": 356, "_357": 358}, 449, "options", [359, 366, 371, 376, 381, 386, 391], {"_63": 360, "_361": 362, "_363": 364, "_57": 365}, 451, "value", "No response from broker", "key", "unit_complaints_no_response_broker", [], {"_63": 367, "_361": 368, "_363": 369, "_57": 370}, 450, "Property not available", "unit_complaints_not_available", [], {"_63": 372, "_361": 373, "_363": 374, "_57": 375}, 452, "No property details", "unit_complaints_no_details", [], {"_63": 377, "_361": 378, "_363": 379, "_57": 380}, 454, "Inaccurate locations", "unit_complaints_locations", [], {"_63": 382, "_361": 383, "_363": 384, "_57": 385}, 453, "Poor quality or irrelevant photos", "unit_complaints_poor_quality", [], {"_63": 387, "_361": 388, "_363": 389, "_57": 390}, 455, "Inaccurate number of bedrooms", "unit_complaints_bedrooms", [], {"_63": 392, "_361": 393, "_363": 394, "_57": 395}, 456, "Incorrect property type", "unit_complaints_property_type", [], "spoken_languages", {"_63": 398, "_357": 399}, 387, [400, 405, 410, 415, 420, 425, 430, 435, 440, 445, 450, 455, 460, 465, 470, 475, 480, 485, 490, 495, 500, 505, 510, 515, 520, 525, 530, 535, 540, 545, 550, 555, 560, 565, 570, 575, 580, 585, 590, 595, 600, 605, 610, 615, 620, 625, 630, 635, 640, 645, 650, 655, 660, 665, 670, 675], {"_63": 401, "_361": 402, "_363": 403, "_57": 404}, 441, "Ukrainian", "spoken_languages_ukrainian", [], {"_63": 406, "_361": 407, "_363": 408, "_57": 409}, 437, "Tamil", "spoken_languages_tamil", [], {"_63": 411, "_361": 412, "_363": 413, "_57": 414}, 406, "German", "spoken_languages_german", [], {"_63": 416, "_361": 417, "_363": 418, "_57": 419}, 421, "Norwegian", "spoken_languages_norwegian", [], {"_63": 421, "_361": 422, "_363": 423, "_57": 424}, 416, "<PERSON><PERSON>", "spoken_languages_kurdi", [], {"_63": 426, "_361": 427, "_363": 428, "_57": 429}, 409, "Hindi", "spoken_languages_hindi", [], {"_63": 431, "_361": 432, "_363": 433, "_57": 434}, 389, "Albanian", "spoken_languages_albanian", [], {"_63": 436, "_361": 437, "_363": 438, "_57": 439}, 420, "Mandarin", "spoken_languages_mandarin", [], {"_63": 441, "_361": 442, "_363": 443, "_57": 444}, 417, "Latvian", "spoken_languages_latvian", [], {"_63": 446, "_361": 447, "_363": 448, "_57": 449}, 397, "Cantonese", "spoken_languages_cantonese", [], {"_63": 451, "_361": 452, "_363": 453, "_57": 454}, 388, "Afrikaans", "spoken_languages_afrikaans", [], {"_63": 456, "_361": 457, "_363": 458, "_57": 459}, 438, "Telugu", "spoken_languages_telugu", [], {"_63": 461, "_361": 462, "_363": 463, "_57": 464}, 431, "Slovene", "spoken_languages_slovene", [], {"_63": 466, "_361": 467, "_363": 468, "_57": 469}, 426, "Romanian", "spoken_languages_romanian", [], {"_63": 471, "_361": 472, "_363": 473, "_57": 474}, 424, "Portuguese", "spoken_languages_portuguese", [], {"_63": 476, "_361": 477, "_363": 478, "_57": 479}, 401, "Danish", "spoken_languages_danish", [], {"_63": 481, "_361": 482, "_363": 483, "_57": 484}, 433, "Spanish", "spoken_languages_spanish", [], {"_63": 486, "_361": 487, "_363": 488, "_57": 489}, 419, "Malayalam", "spoken_languages_malayalam", [], {"_63": 491, "_361": 492, "_363": 493, "_57": 494}, 408, "Gujarati", "spoken_languages_gujarati", [], {"_63": 496, "_361": 497, "_363": 498, "_57": 499}, 390, "Amharic", "spoken_languages_amharic", [], {"_63": 501, "_361": 502, "_363": 503, "_57": 504}, 427, "Russian", "spoken_languages_russian", [], {"_63": 506, "_361": 507, "_363": 508, "_57": 509}, 407, "Greek", "spoken_languages_greek", [], {"_63": 511, "_361": 512, "_363": 513, "_57": 514}, 402, "Dutch", "spoken_languages_dutch", [], {"_63": 516, "_361": 517, "_363": 518, "_57": 519}, 395, "Berber", "spoken_languages_berber", [], {"_63": 521, "_361": 522, "_363": 523, "_57": 524}, 443, "Uzbek", "spoken_languages_uzbek", [], {"_63": 526, "_361": 527, "_363": 528, "_57": 529}, 430, "Slovak", "spoken_languages_slovak", [], {"_63": 531, "_361": 532, "_363": 533, "_57": 534}, 400, "Czech", "spoken_languages_czech", [], {"_63": 536, "_361": 537, "_363": 538, "_57": 539}, 434, "Swahili", "spoken_languages_swahili", [], {"_63": 541, "_361": 542, "_363": 543, "_57": 544}, 428, "Serbian", "spoken_languages_serbian", [], {"_63": 546, "_361": 547, "_363": 548, "_57": 549}, 423, "Polish", "spoken_languages_polish", [], {"_63": 551, "_361": 552, "_363": 553, "_57": 554}, 411, "Italian", "spoken_languages_italian", [], {"_63": 556, "_361": 557, "_363": 558, "_57": 559}, 392, "Azerbaijani", "spoken_languages_azerbaijani", [], {"_63": 561, "_361": 562, "_363": 563, "_57": 564}, 435, "Swedish", "spoken_languages_swedish", [], {"_63": 566, "_361": 567, "_363": 568, "_57": 569}, 422, "Persian/Farsi", "spoken_languages_persian", [], {"_63": 571, "_361": 572, "_363": 573, "_57": 574}, 410, "Hungarian", "spoken_languages_hungarian", [], {"_63": 576, "_361": 577, "_363": 578, "_57": 579}, 405, "French", "spoken_languages_french", [], {"_63": 581, "_361": 582, "_363": 583, "_57": 584}, 404, "Finnish", "spoken_languages_finnish", [], {"_63": 586, "_361": 587, "_363": 588, "_57": 589}, 403, "English", "spoken_languages_english", [], {"_63": 591, "_361": 592, "_363": 593, "_57": 594}, 396, "Bulgarian", "spoken_languages_bulgarian", [], {"_63": 596, "_361": 597, "_363": 598, "_57": 599}, 399, "Croatian", "spoken_languages_croatian", [], {"_63": 601, "_361": 602, "_363": 603, "_57": 604}, 398, "Catalan", "spoken_languages_catalan", [], {"_63": 606, "_361": 607, "_363": 608, "_57": 609}, 442, "Urdu", "spoken_languages_urdu", [], {"_63": 611, "_361": 612, "_363": 613, "_57": 614}, 440, "Turkish", "spoken_languages_turkish", [], {"_63": 616, "_361": 617, "_363": 618, "_57": 619}, 439, "Thai", "spoken_languages_thai", [], {"_63": 621, "_361": 622, "_363": 623, "_57": 624}, 436, "Tagalog", "spoken_languages_tagalog", [], {"_63": 626, "_361": 627, "_363": 628, "_57": 629}, 394, "Bengali", "spoken_languages_bengali", [], {"_63": 631, "_361": 632, "_363": 633, "_57": 634}, 432, "Somali", "spoken_languages_somali", [], {"_63": 636, "_361": 637, "_363": 638, "_57": 639}, 414, "Kazakh", "spoken_languages_kazakh", [], {"_63": 641, "_361": 642, "_363": 643, "_57": 644}, 413, "Kannada", "spoken_languages_kannada", [], {"_63": 646, "_361": 647, "_363": 648, "_57": 649}, 393, "Belarusian", "spoken_languages_belarusian", [], {"_63": 651, "_361": 652, "_363": 653, "_57": 654}, 429, "Sinhalese", "spoken_languages_sinhalese", [], {"_63": 656, "_361": 657, "_363": 658, "_57": 659}, 418, "Malay", "spoken_languages_malay", [], {"_63": 661, "_361": 662, "_363": 663, "_57": 664}, 415, "Korean", "spoken_languages_korean", [], {"_63": 666, "_361": 667, "_363": 668, "_57": 669}, 412, "Japanese", "spoken_languages_japanese", [], {"_63": 671, "_361": 672, "_363": 673, "_57": 674}, 391, "Arabic", "spoken_languages_arabic", [], {"_63": 676, "_361": 677, "_363": 678, "_57": 679}, 425, "Punjabi", "spoken_languages_punjabi", [], "compound_villa_plot", {"_63": 682, "_357": 683}, 240, [684, 689, 694], {"_63": 685, "_361": 686, "_363": 687, "_57": 688}, 241, "Plot with a house", "compound_villa_plot_with_house", [], {"_63": 690, "_361": 691, "_363": 692, "_57": 693}, 242, "Land without a house", "compound_villa_plot_without_house", [], {"_63": 695, "_361": 696, "_363": 697, "_57": 698}, 243, "Individual project", "compound_villa_plot_individual_project", [], "payment_plan_when", {"_63": 701, "_357": 702}, 349, [703, 708, 713, 718, 723, 728, 733, 738, 743, 748], {"_63": 704, "_361": 705, "_363": 706, "_57": 707}, 350, "At booking", "payment_plan_when_booking", [], {"_63": 709, "_361": 710, "_363": 711, "_57": 712}, 351, "Exact Date", "payment_plan_when_exact_date", [], {"_63": 714, "_361": 715, "_363": 716, "_57": 717}, 352, "Period after booking", "payment_plan_when_days_after_booking", [], {"_63": 719, "_361": 720, "_363": 721, "_57": 722}, 353, "Completion rate", "payment_plan_when_completion_rate", [], {"_63": 724, "_361": 725, "_363": 726, "_57": 727}, 354, "Handover", "payment_plan_when_handover", [], {"_63": 729, "_361": 730, "_363": 731, "_57": 732}, 355, "Days after handover", "payment_plan_when_days_after_handover", [], {"_63": 734, "_361": 735, "_363": 736, "_57": 737}, 382, "Tax Fee", "payment_plan_when_tax_fee", [], {"_63": 739, "_361": 740, "_363": 741, "_57": 742}, 381, "Down payment", "payment_plan_when_down_payment", [], {"_63": 744, "_361": 745, "_363": 746, "_57": 747}, 383, "Extra Fee", "payment_plan_when_extra_fee", [], {"_63": 749, "_361": 750, "_363": 751, "_57": 752}, 465, "Period after down payment", "payment_plan_when_days_after_down_payment", [], "selection_status", {"_63": 755, "_357": 756}, 181, [757, 762, 767], {"_63": 758, "_361": 759, "_363": 760, "_57": 761}, 184, "Removed", "selection_status_removed", [], {"_63": 763, "_361": 764, "_363": 765, "_57": 766}, 183, "Completed", "selection_status_completed", [], {"_63": 768, "_361": 769, "_363": 770, "_57": 771}, 182, "In progress", "selection_status_progress", [], "unit_media_type", {"_63": 774, "_357": 775}, 363, [776, 781], {"_63": 777, "_361": 778, "_363": 779, "_57": 780}, 364, "Photo", "unit_media_type_photo", [], {"_63": 782, "_361": 783, "_363": 784, "_57": 785}, 365, "Plan", "unit_media_type_plan", [], "booking_status", {"_63": 788, "_357": 789}, 368, [790, 795, 800, 805, 810, 814, 819], {"_63": 791, "_361": 792, "_363": 793, "_57": 794}, 375, "Commission payed", "booking_status_commission_payed", [], {"_63": 796, "_361": 797, "_363": 798, "_57": 799}, 369, "Created", "booking_status_created", [], {"_63": 801, "_361": 802, "_363": 803, "_57": 804}, 374, "Waiting for commission payment", "booking_status_waiting_commission_payed", [], {"_63": 806, "_361": 807, "_363": 808, "_57": 809}, 373, "Booked", "booking_status_booked", [], {"_63": 811, "_361": 740, "_363": 812, "_57": 813}, 372, "booking_status_down_payment", [], {"_63": 815, "_361": 816, "_363": 817, "_57": 818}, 370, "Fixed", "booking_status_fixed", [], {"_63": 820, "_361": 821, "_363": 822, "_57": 823}, 371, "Booking payment", "booking_status_booking_payment", [], "unit_furnishing", {"_63": 826, "_357": 827}, 330, [828, 833, 838], {"_63": 829, "_361": 830, "_363": 831, "_57": 832}, 331, "Furnished", "unit_furnishing_furnished", [], {"_63": 834, "_361": 835, "_363": 836, "_57": 837}, 332, "Unfurnished", "unit_furnishing_unfurnished", [], {"_63": 839, "_361": 840, "_363": 841, "_57": 842}, 333, "Partially furnished", "unit_furnishings_partly_furnished", [], "project_gallery_category", {"_63": 845, "_357": 846}, 92, [847, 852, 857, 862, 867], {"_63": 848, "_361": 849, "_363": 850, "_57": 851}, 107, "Project presentation", "project_gallery_category_presentation", [], {"_63": 853, "_361": 854, "_363": 855, "_57": 856}, 93, "Construction progress", "project_gallery_category_construction_progress", [], {"_63": 858, "_361": 859, "_363": 860, "_57": 861}, 94, "Finishing examples", "project_gallery_category_finishing_examples", [], {"_63": 863, "_361": 864, "_363": 865, "_57": 866}, 95, "Infrastructure", "project_gallery_category_infrastructure", [], {"_63": 868, "_361": 869, "_363": 870, "_57": 871}, 96, "View", "project_gallery_category_view", [], "booking_payment_method", {"_63": 874, "_357": 875}, 376, [876, 881, 886, 891], {"_63": 877, "_361": 878, "_363": 879, "_57": 880}, 378, "Card not Russia", "booking_status_card_not_russia", [], {"_63": 882, "_361": 883, "_363": 884, "_57": 885}, 380, "Other", "booking_status_other", [], {"_63": 887, "_361": 888, "_363": 889, "_57": 890}, 379, "Card of Russia", "booking_status_card_russia", [], {"_63": 892, "_361": 893, "_363": 894, "_57": 895}, 377, "SWIFT", "booking_payment_method_swift", [], "project_facilities", {"_63": 898, "_357": 899}, 66, [900, 907, 913, 919, 925, 931, 937, 943, 949, 955], {"_63": 901, "_361": 902, "_363": 903, "_57": 904}, 457, "Branded", "project_facilities_branded", {"_905": 906}, "icon", "branded", {"_63": 908, "_361": 909, "_363": 910, "_57": 911}, 153, "Concierge Service", "project_facilities_concierge", {"_905": 912}, "concierge_service", {"_63": 914, "_361": 915, "_363": 916, "_57": 917}, 286, "<PERSON><PERSON>owed", "project_facilities_pets", {"_905": 918}, "pets", {"_63": 920, "_361": 921, "_363": 922, "_57": 923}, 281, "Kids Play Area", "project_facilities_kids", {"_905": 924}, "kids_play_area", {"_63": 926, "_361": 927, "_363": 928, "_57": 929}, 156, "Electric Car Charger", "project_facilities_car_charger", {"_905": 930}, "electric_car_charger", {"_63": 932, "_361": 933, "_363": 934, "_57": 935}, 446, "Free A/C", "project_facilities_free_ac", {"_905": 936}, "central_ac", {"_63": 938, "_361": 939, "_363": 940, "_57": 941}, 447, "Private beach", "project_facilities_private_beach", {"_905": 942}, "private_beach", {"_63": 944, "_361": 945, "_363": 946, "_57": 947}, 448, "Short-term rental forbidden", "project_facilities_short_rental_forbidden", {"_905": 948}, "short_rental_forbidden", {"_63": 950, "_361": 951, "_363": 952, "_57": 953}, 463, "Private pool", "project_facilities_private_pool", {"_905": 954}, "private_pool", {"_63": 956, "_361": 957, "_363": 958, "_57": 959}, 464, "Maid/Study room", "project_facilities_maid_room", {"_905": 960}, "maid_room", "project_media_type", {"_63": 963, "_357": 964}, 358, [965], {"_63": 966, "_361": 967, "_363": 968, "_57": 969}, 359, "Presentation", "project_media_type_presentation", [], "rooms", {"_63": 972, "_357": 973}, 109, [974, 981, 987, 993, 999, 1005, 1011, 1017, 1023, 1029, 1035, 1041], {"_63": 975, "_361": 976, "_363": 977, "_57": 978}, 110, "Studio", "rooms_studio", {"_979": 980}, "short_value", "St", {"_63": 982, "_361": 983, "_363": 984, "_57": 985}, 111, "1 BR", "rooms_1", {"_979": 986}, "1BR", {"_63": 988, "_361": 989, "_363": 990, "_57": 991}, 112, "2 BR", "rooms_2", {"_979": 992}, "2BR", {"_63": 994, "_361": 995, "_363": 996, "_57": 997}, 113, "3 BR", "rooms_3", {"_979": 998}, "3BR", {"_63": 1000, "_361": 1001, "_363": 1002, "_57": 1003}, 114, "4 BR", "rooms_4", {"_979": 1004}, "4BR", {"_63": 1006, "_361": 1007, "_363": 1008, "_57": 1009}, 115, "5 BR", "rooms_5", {"_979": 1010}, "5BR", {"_63": 1012, "_361": 1013, "_363": 1014, "_57": 1015}, 341, "6 BR", "rooms_6", {"_979": 1016}, "6BR", {"_63": 1018, "_361": 1019, "_363": 1020, "_57": 1021}, 342, "7 BR", "rooms_7", {"_979": 1022}, "7BR", {"_63": 1024, "_361": 1025, "_363": 1026, "_57": 1027}, 343, "8 BR", "rooms_8", {"_979": 1028}, "8BR", {"_63": 1030, "_361": 1031, "_363": 1032, "_57": 1033}, 344, "9 BR", "rooms_9", {"_979": 1034}, "9BR", {"_63": 1036, "_361": 1037, "_363": 1038, "_57": 1039}, 345, "10 BR", "rooms_10", {"_979": 1040}, "10BR", {"_63": 1042, "_361": 1043, "_363": 1044, "_57": 1045}, 164, "NA", "rooms_n", {"_979": 1046}, "Free", "unit_type_room", {"_63": 1049, "_357": 1050}, 137, [1051, 1056, 1061, 1066, 1071, 1076, 1081, 1086, 1091], {"_63": 1052, "_361": 1053, "_363": 1054, "_57": 1055}, 138, "Apartment", "unit_type_room_unit", [], {"_63": 1057, "_361": 1058, "_363": 1059, "_57": 1060}, 462, "Villa", "unit_type_room_villa", [], {"_63": 1062, "_361": 1063, "_363": 1064, "_57": 1065}, 445, "Townhouse", "unit_type_room_townhouse", [], {"_63": 1067, "_361": 1068, "_363": 1069, "_57": 1070}, 140, "Duplex", "unit_type_room_duplex", [], {"_63": 1072, "_361": 1073, "_363": 1074, "_57": 1075}, 362, "<PERSON>x", "unit_type_room_triplex", [], {"_63": 1077, "_361": 1078, "_363": 1079, "_57": 1080}, 357, "Penthouse", "unit_type_room_penthouse", [], {"_63": 1082, "_361": 1083, "_363": 1084, "_57": 1085}, 141, "Villa in project", "unit_type_room_villa_in_project", [], {"_63": 1087, "_361": 1088, "_363": 1089, "_57": 1090}, 346, "Retail", "unit_type_room_retail", [], {"_63": 1092, "_361": 1093, "_363": 1094, "_57": 1095}, 347, "Office", "unit_type_room_office", [], "unit_status", {"_63": 1098, "_357": 1099}, 143, [1100, 1106, 1112], {"_63": 1101, "_361": 807, "_363": 1102, "_57": 1103}, 146, "unit_status_reservation", {"_1104": 1105}, "bg_color", "#8e8e8d", {"_63": 1107, "_361": 1108, "_363": 1109, "_57": 1110}, 145, "Sold", "unit_status_sold", {"_1104": 1111}, "#DD6465", {"_63": 1113, "_361": 1114, "_363": 1115, "_57": 1116}, 144, "On sale", "unit_status_sale", {"_1104": 1117}, "#20CB6A", "project_badges", {"_63": 1120, "_357": 1121}, 103, [1122, 1130, 1135, 1142, 1148, 1157, 1163, 1169, 1174], {"_63": 1123, "_361": 1124, "_363": 1125, "_57": 1126}, 444, "Exclusive", "badge_exclusive", {"_1104": 1127, "_905": 60, "_1128": 1129}, "#dfe4ff", "color", "#4F5FD9", {"_63": 1131, "_361": 1132, "_363": 1133, "_57": 1134}, 366, "Recommended", "badge_recommend", {"_1104": 1127, "_1128": 1129}, {"_63": 1136, "_361": 1137, "_363": 1138, "_57": 1139}, 385, "Announcement", "badge_announcement", {"_1104": 1140, "_1128": 1141}, "#fff1f2", "#d74652", {"_63": 1143, "_361": 1144, "_363": 1145, "_57": 1146}, 361, "Presale (EOI)", "badge_early_booking", {"_1104": 1147, "_1128": 1117}, "#E9FAF0", {"_63": 1149, "_361": 1150, "_363": 1151, "_57": 1152}, 163, "Launch", "badge_start_sale", {"_1104": 1153, "_1154": 1155, "_1128": 1156}, "#fffff1", "remove_days", "14", "#c88902", {"_63": 1158, "_361": 1159, "_363": 1160, "_57": 1161}, 161, "Sold Out", "badge_sold_out", {"_1104": 1140, "_1128": 1162}, "#ff0015", {"_63": 1164, "_361": 1165, "_363": 1166, "_57": 1167}, 105, "Free Installment", "badge_free_installment", {"_1104": 1168, "_1128": 1162}, "#e7efff", {"_63": 1170, "_361": 1171, "_363": 1172, "_57": 1173}, 384, "Prelaunch", "badge_prelaunch_sale", {"_1104": 1140, "_1128": 1162}, {"_63": 1175, "_361": 1176, "_363": 1177, "_57": 1178}, 386, "Price on request", "badge_price_on_request", {"_1104": 1179, "_1128": 1180}, "#e6fafa", "#14a1a2", "development_stage", {"_63": 1183, "_357": 1184}, 116, [1185, 1190, 1195, 1200], {"_63": 1186, "_361": 1187, "_363": 1188, "_57": 1189}, 117, "Scheduled", "development_stage_scheduled", [], {"_63": 1191, "_361": 1192, "_363": 1193, "_57": 1194}, 120, "Ready", "development_stage_finished", [], {"_63": 1196, "_361": 1197, "_363": 1198, "_57": 1199}, 119, "Stopped", "development_stage_stopped", [], {"_63": 1201, "_361": 1202, "_363": 1203, "_57": 1204}, 348, "In Progress", "development_stage_progress", [], "project_sales_status", {"_63": 120, "_357": 1207}, [1208, 1212, 1216, 1223, 1226, 1232], {"_63": 1209, "_361": 1137, "_363": 1210, "_57": 1211}, 335, "project_sales_status_announcement", {"_1128": 1141, "_1104": 1140}, {"_63": 1213, "_361": 1144, "_363": 1214, "_57": 1215}, 338, "project_sales_status_eoi", {"_1128": 1117, "_1104": 1147}, {"_63": 1217, "_361": 1150, "_363": 1218, "_57": 1219}, 360, "project_sales_status_new_launch", {"_1154": 1155, "_1220": 1221, "_1128": 1156, "_1104": 1222}, "next_value", "project_sales_status_on_sale", "#fff6e9", {"_63": 97, "_361": 1224, "_363": 1221, "_57": 1225}, "On Sale", {"_1128": 1117, "_1104": 1147}, {"_63": 1227, "_361": 1159, "_363": 1228, "_57": 1229}, 339, "project_sales_status_sold_out", {"_1128": 1230, "_1104": 1231}, "#12122D", "#DFDFEB", {"_63": 1233, "_361": 1234, "_363": 1235, "_57": 1236}, 334, "Pending", "project_sales_status_pending", {"_1128": 1162, "_1104": 1140}, "currencies", {"_48": 1239}, {"_182": 1240, "_144": 1241, "_1242": 1243, "_1244": 1245, "_1246": 1247, "_123": 1248, "_1249": 1250, "_1251": 1252, "_106": 1253, "_1254": 1255, "_161": 1256}, 0.2310371419, 0.1983360782, "IDR", 4411.8487326964, "INR", 23.325521015, "JPY", 39.158099095, 0.1045301587, "PKR", 77.2687860817, "RUB", 21.2937659993, 8.8311349764, "TRY", 10.846529803, 0.2723273941, "languages", {"_1259": 1260, "_1266": 1267, "_40": 1270, "_1273": 1274, "_1277": 1278, "_1281": 1282, "_1286": 1287, "_1290": 1291, "_1295": 1296, "_1299": 1300, "_1303": 1304, "_1307": 1308}, "ar", {"_59": 1261, "_1262": 1263, "_1264": 1265}, "AR: العربية", "native", "العربية", "flag", "ae", "de", {"_59": 1268, "_1262": 1269, "_1264": 1266}, "DE: De<PERSON>ch", "De<PERSON>ch", {"_59": 1271, "_1262": 587, "_1264": 1272}, "EN: English", "gb", "es", {"_59": 1275, "_1262": 1276, "_1264": 1273}, "ES: Español", "Español", "fr", {"_59": 1279, "_1262": 1280, "_1264": 1277}, "FR: Français", "Français", "hi", {"_59": 1283, "_1262": 1284, "_1264": 1285}, "HI: Hindi", "हिन्दी", "in", "it", {"_59": 1288, "_1262": 1289, "_1264": 1286}, "IT: <PERSON><PERSON>", "Italiano", "ja", {"_59": 1292, "_1262": 1293, "_1264": 1294}, "JA: Japanese", "日本語", "jp", "pt", {"_59": 1297, "_1262": 1298, "_1264": 1295}, "PT: Português", "Português", "ru", {"_59": 1301, "_1262": 1302, "_1264": 1299}, "RU: Русский", "Русский", "tr", {"_59": 1305, "_1262": 1306, "_1264": 1303}, "TR: Türkçe", "Türkçe", "zh", {"_59": 1309, "_1262": 1310, "_1264": 1311}, "ZH: Chinese", "中文", "cn", "statistics", {"_1314": 1315, "_1316": 1317}, "projects", 2266, "units", 698147, "priceRanges", [1320, 1327, 1330, 1333, 1336, 1339, 1342, 1345, 1348, 1351, 1354, 1357, 1360, 1363, 1366, 1369, 1372, 1374, 1376, 1378, 1380, 1382, 1385, 1388, 1391, 1394, 1396, 1399, 1402, 1404], {"_1321": 1322, "_1323": 1324, "_1325": 1326}, "count", 247, "from", 382300, "to", 577105, {"_1321": 1328, "_1323": 1326, "_1325": 1329}, 1233, 771910, {"_1321": 1331, "_1323": 1329, "_1325": 1332}, 1582, 966715, {"_1321": 1334, "_1323": 1332, "_1325": 1335}, 2480, 1161520, {"_1321": 1337, "_1323": 1335, "_1325": 1338}, 2275, 1356325, {"_1321": 1340, "_1323": 1338, "_1325": 1341}, 1426, 1551130, {"_1321": 1343, "_1323": 1341, "_1325": 1344}, 1663, 1745935, {"_1321": 1346, "_1323": 1344, "_1325": 1347}, 1684, 1940740, {"_1321": 1349, "_1323": 1347, "_1325": 1350}, 1857, 2135545, {"_1321": 1352, "_1323": 1350, "_1325": 1353}, 1344, 2330350, {"_1321": 1355, "_1323": 1353, "_1325": 1356}, 715, 2525155, {"_1321": 1358, "_1323": 1356, "_1325": 1359}, 1130, 2719960, {"_1321": 1361, "_1323": 1359, "_1325": 1362}, 618, 2914765, {"_1321": 1364, "_1323": 1362, "_1325": 1365}, 512, 3109570, {"_1321": 1367, "_1323": 1365, "_1325": 1368}, 579, 3304375, {"_1321": 1370, "_1323": 1368, "_1325": 1371}, 516, 3499180, {"_1321": 874, "_1323": 1371, "_1325": 1373}, 3693985, {"_1321": 536, "_1323": 1373, "_1325": 1375}, 3888790, {"_1321": 451, "_1323": 1375, "_1325": 1377}, 4083595, {"_1321": 401, "_1323": 1377, "_1325": 1379}, 4278400, {"_1321": 1030, "_1323": 1379, "_1325": 1381}, 4473205, {"_1321": 1383, "_1323": 1381, "_1325": 1384}, 263, 4668010, {"_1321": 1386, "_1323": 1384, "_1325": 1387}, 192, 4862815, {"_1321": 1389, "_1323": 1387, "_1325": 1390}, 126, 5057620, {"_1321": 1392, "_1323": 1390, "_1325": 1393}, 134, 5252425, {"_1321": 845, "_1323": 1393, "_1325": 1395}, 5447230, {"_1321": 1397, "_1323": 1395, "_1325": 1398}, 90, 5642035, {"_1321": 1400, "_1323": 1398, "_1325": 1401}, 54, 5836840, {"_1321": 898, "_1323": 1401, "_1325": 1403}, 6031645, {"_1321": 1405, "_1323": 1403, "_1325": 1406}, 2617, 340500000, "priceAreaRanges", [1409, 1412, 1414, 1416, 1419, 1421, 1424, 1427, 1430, 1433, 1436, 1439, 1442, 1445, 1448, 1451, 1454, 1457, 1460, 1463, 1466, 1469, 1472, 1475, 1478, 1481, 1484, 1487, 1490, 1493], {"_1321": 120, "_1323": 1410, "_1325": 1411}, "1177.0", "2683.0", {"_1321": 90, "_1323": 1411, "_1325": 1413}, "4189.0", {"_1321": 108, "_1323": 1413, "_1325": 1415}, "5695.0", {"_1321": 1417, "_1323": 1415, "_1325": 1418}, 61, "7201.0", {"_1321": 264, "_1323": 1418, "_1325": 1420}, "8707.0", {"_1321": 1422, "_1323": 1420, "_1325": 1423}, 152, "10213.0", {"_1321": 1425, "_1323": 1423, "_1325": 1426}, 158, "11719.0", {"_1321": 1428, "_1323": 1426, "_1325": 1429}, 686, "13225.0", {"_1321": 1431, "_1323": 1429, "_1325": 1432}, 1454, "14731.0", {"_1321": 1434, "_1323": 1432, "_1325": 1435}, 2602, "16237.0", {"_1321": 1437, "_1323": 1435, "_1325": 1438}, 2630, "17743.0", {"_1321": 1440, "_1323": 1438, "_1325": 1441}, 2235, "19249.0", {"_1321": 1443, "_1323": 1441, "_1325": 1444}, 1792, "20755.0", {"_1321": 1446, "_1323": 1444, "_1325": 1447}, 2302, "22261.0", {"_1321": 1449, "_1323": 1447, "_1325": 1450}, 1264, "23767.0", {"_1321": 1452, "_1323": 1450, "_1325": 1453}, 977, "25273.0", {"_1321": 1455, "_1323": 1453, "_1325": 1456}, 1281, "26779.0", {"_1321": 1458, "_1323": 1456, "_1325": 1459}, 1278, "28285.0", {"_1321": 1461, "_1323": 1459, "_1325": 1462}, 1153, "29791.0", {"_1321": 1464, "_1323": 1462, "_1325": 1465}, 1095, "31297.0", {"_1321": 1467, "_1323": 1465, "_1325": 1468}, 529, "32803.0", {"_1321": 1470, "_1323": 1468, "_1325": 1471}, 325, "34309.0", {"_1321": 1473, "_1323": 1471, "_1325": 1474}, 265, "35815.0", {"_1321": 1476, "_1323": 1474, "_1325": 1477}, 274, "37321.0", {"_1321": 1479, "_1323": 1477, "_1325": 1480}, 251, "38827.0", {"_1321": 1482, "_1323": 1480, "_1325": 1483}, 261, "40333.0", {"_1321": 1485, "_1323": 1483, "_1325": 1486}, 166, "41839.0", {"_1321": 1488, "_1323": 1486, "_1325": 1489}, 136, "43345.0", {"_1321": 1491, "_1323": 1489, "_1325": 1492}, 82, "44851.0", {"_1321": 1494, "_1323": 1495, "_1325": 1496}, 1986, 44851, 2766535, "permission", [1499, 1506, 1509, 1511, 1514, 1517, 1520, 1523, 1526, 1529, 1532, 1535, 1538, 1541, 1544], {"_73": 1500, "_59": 1501, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "villas_list", "Villas List", "is_create", "is_read", "is_update", "is_delete", {"_73": 1507, "_59": 1508, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "villas_plan", "Master Plan (Villas)", {"_73": 34, "_59": 1510, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "Transactions", {"_73": 1512, "_59": 1513, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "construction_information", "Extended Construction Progress Information", {"_73": 1515, "_59": 1516, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "client_mode", "Client Mode", {"_73": 1518, "_59": 1519, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "booking", "Booking", {"_73": 1521, "_59": 1522, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "brochure", "Brochures", {"_73": 1524, "_59": 1525, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "assignment", "Secondary", {"_73": 1527, "_59": 1528, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "units_list", "Units List", {"_73": 1530, "_59": 1531, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "units_chess", "Chessboard", {"_73": 1533, "_59": 1534, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "units_floor_plan", "Floor Layout", {"_73": 1536, "_59": 1537, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "sales_selection_share", "Sales Offer Share", {"_73": 1539, "_59": 1540, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "promotions", "Promotions", {"_73": 1542, "_59": 1543, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "sales_selection_selections", "Sales Offer Collections", {"_73": 1545, "_59": 1546, "_1502": 56, "_1503": 56, "_1504": 56, "_1505": 56}, "builder_contacts", "Developer Contacts", "user", "invites", [], "onboarding", [], "cityCurrency", "city_id", "isAuthenticated", "client_mode_is_active", "client_mode_settings", {"_1558": 145, "_1559": 145, "_1560": 145, "_1561": 145, "_1562": 145, "_1563": 145, "_1564": 145, "_1565": 145, "_1566": 145, "_1567": 145, "_1568": 145}, "sales_offices", "exclusive_filter", "cooperation_terms", "private_description", "show_onbording", "agent_contacts", "promoted_projects", "completion_progress", "builder", "promo_for_brokers", "brokers_commission", "pages/project/compound_page", {"_1571": 1572, "_2262": 2263}, "compound", {"_63": 1573, "_1574": 1571, "_59": 1575, "_1576": 1577, "_1578": 1579, "_1580": 1581, "_1582": 1583, "_1584": 1583, "_1585": 1583, "_1586": 1587, "_1588": 56, "_1589": 1590, "_1616": 1617, "_1618": 1619, "_1620": 1583, "_1621": 56, "_1622": -5, "_1524": -5, "_1623": 1624, "_1625": 1626, "_1627": 1628, "_1629": -5, "_1630": -5, "_1631": 56, "_1632": -5, "_1633": -5, "_1634": -5, "_1635": 1636, "_1637": 1638, "_1566": 1639, "_1650": 120, "_1651": 90, "_1652": 1653, "_61": 1657, "_1661": 56, "_1662": 97, "_1663": -5, "_1664": -5, "_1665": 1666, "_2099": 2100, "_352": 2106, "_2110": 2111, "_2115": -5, "_1558": 2116, "_1312": 2235, "_1553": 52, "_77": 2258, "_79": 2259, "_2260": 2261}, 4291, "type", "Naseem Townhouses", "description", "<p>One of the most exclusive developments in Dubai, Town Square Dubai by leading developer NSHAMA is a fully-integrated community that offers all the services and facilities you need.</p><p>It has set a new benchmark with its class of living, raising the expectation of home owners seeking modern lifestyle.</p><p>Surrounded by landscaped gardens, the beautifully designed apartments and townhouses range in size and style, offering ultimate privacy and peace of mind.</p><p>A gated community that offers the finest levels of living and serenity in the city, Naseem Townhouses located in Town Square North West district, welcome the discerning homeowners to offer them the lifestyle they seek and deserve. A fusion of lavish living with unparalleled facilities to provide residents with an incomparable living experience.</p><p>The lavish exterior of Naseem Townhouses sets the tone for the prestigious nature of the development. Manicured landscaping, wide pavements and thoughtfully designed pathways make the entire place a safe haven for children to enjoy their time outdoors with their playmates. Making the most of those lively enviroment, comfortable homes give a feeling of light and space from the moment you enter the community.</p><p>Designed by reputed architects, each unit is carefully designed with a contemporary style offering residents a modern lifestyle to suit all tastes. From distinguished fixtures and finishes, to architectural designs and convenient surroundings, the community ensures the ideal urban family living.</p><p>Naseem Townhouses captures the spirit and the style of the modern, urban way of life. Nestled amidst lush, beautiful greenery and colorful gardens, residents have access to the central community park, outdoor areas for all the family members, jogging and cycling trails, a sports area fit with all the modern sports equipment, and more.</p><p>Get ready for an intense and vibrant living experience... Welcome home!</p>", "updated_at", "2025-06-10 06:30:02", "start_at", "2018-06-01 00:00:00", "planned_at", "2020-12-21 00:00:00", "predicted_completion_at", "completed_at", "website", "https://nshama.ae/properties/naseem-townhouses/", "is_floor_plan_view", "polygon", [1591, 1594, 1597, 1600, 1603, 1606, 1609, 1612, 1615], [1592, 1593], 55.282514, 25.012527, [1595, 1596], 55.282856, 25.012581, [1598, 1599], 55.285901, 25.015545, [1601, 1602], 55.285775, 25.016823, [1604, 1605], 55.284978, 25.017559, [1607, 1608], 55.284024, 25.017022, [1610, 1611], 55.280966, 25.014199, [1613, 1614], 55.280947, 25.013928, [1592, 1593], "dld_project_number", 1936, "construction_percent", "100.00", "construction_inspection_date", "is_range_mode", "service_charge", "escrow_bank", "Dubai Islamic Bank (Public Joint Stock Company)", "escrow_number", "***************", "ownership", "freehold", "ownership_lease_duration_years", "ownership_lease_end_at", "ownership_lease_auto_renewal", "ownership_description", "agent_fee_value", "agent_fee_description", "district", "Al Yelayiss 2, Town Square", "address", "Naseem Townhouses, Town Square, Dubai", {"_63": 1640, "_1637": -5, "_59": 1641, "_1576": 1642, "_61": 1643, "_1586": 1647, "_1648": 1649}, 298, "<PERSON><PERSON><PERSON>", "<p>Nshama Company is a young, rapidly developing developer from Dubai, who is actively conquering the market and has earned a brilliant reputation as a reliable partner who always fulfills his obligations. The foundation date of the organization is considered to be 2014, when <PERSON>, who previously worked at Emaar International, decided to open his own business. He took the CEO's chair and went on a free voyage, implementing one grandiose project after another with enviable regularity.</p><p>The flagship project of Nasa, through which the company publicly declared itself as a worthy competitor to the real sharks of the market, became Town Square Dubai. He is the personification of the whole concept of the developer — cozy houses and real communities of people united by common interests and the same views on life. If you need not just an apartment, but a real home – cozy, comfortable, with a soul and a twist – <PERSON><PERSON><PERSON> knows how to help you!</p><p></p><p></p>", {"_63": 1644, "_64": 1645, "_61": 1646}, 64386, "https://files.alnair.ae/uploads/2023/5/10/63/10638be31cab4fe48691f1cb2b2099df.png", "https://files.alnair.ae/uploads/user_logo/2023/5/10/63/10638be31cab4fe48691f1cb2b2099df.png", "https://nshama.ae/", "phone", 971561441926, "property_age", "construction_percent_out_of_plan", "cover", {"_63": 1654, "_64": 1655, "_61": 1656}, 368160, "https://files.alnair.ae/uploads/2025/5/7d/9d/7d9d9a1a2277bbb8dfa157695e01e587.jpg", "https://files.alnair.ae/uploads/logo/2025/5/7d/9d/7d9d9a1a2277bbb8dfa157695e01e587.jpg", {"_63": 1658, "_64": 1659, "_61": 1660}, 368134, "https://files.alnair.ae/uploads/2025/5/c9/24/c924e9a391db09df12182ad24e5932c3.png", "https://files.alnair.ae/uploads/logo/2025/5/c9/24/c924e9a391db09df12182ad24e5932c3.png", "catalog_recommend", "catalog_sales_status", "catalog_sales_status_start_at", "catalog_sales_status_end_at", "galleries", [1667, 1709, 1733, 1756, 1809, 1869, 1924, 1979, 2014, 2069], {"_63": 1668, "_1574": 860, "_73": 859, "_1576": 1669, "_1670": -5, "_1671": -5, "_1672": 102, "_1673": 102, "_1674": 90, "_1675": 1676}, 19810, "Designed by reputed architects, each unit is carefully designed with a contemporary style offering residents a modern lifestyle to suit all tastes. From distinguished fixtures and finishes, to architectural designs and convenient surroundings, the community ensures the ideal urban family living.", "percentage", "date_at", "total", "number_of_photos", "number_of_videos", "photos", [1677, 1684, 1689, 1694, 1699, 1704], {"_63": 1678, "_64": 1679, "_61": 1680, "_1681": 1682, "_1683": -5}, 368175, "https://files.alnair.ae/uploads/gallery_photo/2025/5/cb/f1/cbf1b1005687f4b2c8552b886ed8b6f6.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/cb/f1/cbf1b1005687f4b2c8552b886ed8b6f6.jpg", "size", 482692, "src_video", {"_63": 1685, "_64": 1686, "_61": 1687, "_1681": 1688, "_1683": -5}, 368174, "https://files.alnair.ae/uploads/gallery_photo/2025/5/7e/45/7e45860a9c655d88f48a424020e0047c.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/7e/45/7e45860a9c655d88f48a424020e0047c.jpg", 556869, {"_63": 1690, "_64": 1691, "_61": 1692, "_1681": 1693, "_1683": -5}, 368173, "https://files.alnair.ae/uploads/gallery_photo/2025/5/5d/59/5d598a0815b4dac5f7d8121404727f7d.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/5d/59/5d598a0815b4dac5f7d8121404727f7d.jpg", 490353, {"_63": 1695, "_64": 1696, "_61": 1697, "_1681": 1698, "_1683": -5}, 368172, "https://files.alnair.ae/uploads/gallery_photo/2025/5/58/f9/58f9a262a6cb8e976042dbae88b36f3e.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/58/f9/58f9a262a6cb8e976042dbae88b36f3e.jpg", 621511, {"_63": 1700, "_64": 1701, "_61": 1702, "_1681": 1703, "_1683": -5}, 368171, "https://files.alnair.ae/uploads/gallery_photo/2025/5/87/17/87176ed9cab056888c555641489fcdf7.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/87/17/87176ed9cab056888c555641489fcdf7.jpg", 520830, {"_63": 1705, "_64": 1706, "_61": 1707, "_1681": 1708, "_1683": -5}, 368170, "https://files.alnair.ae/uploads/gallery_photo/2025/5/88/71/88712bbf76467f6477eef1f3eda21146.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/88/71/88712bbf76467f6477eef1f3eda21146.jpg", 500871, {"_63": 1710, "_1574": 850, "_73": 849, "_1576": 1711, "_1670": -5, "_1671": -5, "_1672": 120, "_1673": 120, "_1674": 90, "_1675": 1712}, 19809, "Surrounded by landscaped gardens, the beautifully designed apartments and townhouses range in size and style, offering ultimate privacy and peace of mind.\\r\\nA gated community that offers the finest levels of living and serenity in the city, Naseem Townhouses located in Town Square North West district, welcome the discerning homeowners to offer them the lifestyle they seek and deserve. A fusion of lavish living with unparalleled facilities to provide residents with an incomparable living experience.", [1713, 1718, 1723, 1728], {"_63": 1714, "_64": 1715, "_61": 1716, "_1681": 1717, "_1683": -5}, 368166, "https://files.alnair.ae/uploads/gallery_photo/2025/5/8e/82/8e82d17e7b001e1be0bfe06fb725d773.jpg", "https://files.alnair.ae/uploads/presentation/2025/5/8e/82/8e82d17e7b001e1be0bfe06fb725d773.jpg", 1269292, {"_63": 1719, "_64": 1720, "_61": 1721, "_1681": 1722, "_1683": -5}, 368165, "https://files.alnair.ae/uploads/gallery_photo/2025/5/bb/8a/bb8a18533f5dd58b309c4cd550578606.jpg", "https://files.alnair.ae/uploads/presentation/2025/5/bb/8a/bb8a18533f5dd58b309c4cd550578606.jpg", 842430, {"_63": 1724, "_64": 1725, "_61": 1726, "_1681": 1727, "_1683": -5}, 368164, "https://files.alnair.ae/uploads/gallery_photo/2025/5/62/98/6298cd8959dab31c7375f542de9d8b61.jpg", "https://files.alnair.ae/uploads/presentation/2025/5/62/98/6298cd8959dab31c7375f542de9d8b61.jpg", 560651, {"_63": 1729, "_64": 1730, "_61": 1731, "_1681": 1732, "_1683": -5}, 368163, "https://files.alnair.ae/uploads/gallery_photo/2025/5/73/90/7390c6e896e1edc9240b474336e22d6d.jpg", "https://files.alnair.ae/uploads/presentation/2025/5/73/90/7390c6e896e1edc9240b474336e22d6d.jpg", 496054, {"_63": 1734, "_1574": 865, "_73": 864, "_1576": -5, "_1670": -5, "_1671": -5, "_1672": 120, "_1673": 120, "_1674": 90, "_1675": 1735}, 19811, [1736, 1741, 1746, 1751], {"_63": 1737, "_64": 1738, "_61": 1739, "_1681": 1740, "_1683": -5}, 368211, "https://files.alnair.ae/uploads/gallery_photo/2025/5/b7/27/b727e9a01d3c3ade1fc2c38d49158467.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/b7/27/b727e9a01d3c3ade1fc2c38d49158467.jpg", 903623, {"_63": 1742, "_64": 1743, "_61": 1744, "_1681": 1745, "_1683": -5}, 368208, "https://files.alnair.ae/uploads/gallery_photo/2025/5/f7/97/f7971cefcabee4cd001b3a942d95b442.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/f7/97/f7971cefcabee4cd001b3a942d95b442.jpg", 1168833, {"_63": 1747, "_64": 1748, "_61": 1749, "_1681": 1750, "_1683": -5}, 368204, "https://files.alnair.ae/uploads/gallery_photo/2025/5/a9/0e/a90e4ad404e3696cb05ca33465050617.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/a9/0e/a90e4ad404e3696cb05ca33465050617.jpg", 1069730, {"_63": 1752, "_64": 1753, "_61": 1754, "_1681": 1755, "_1683": -5}, 368201, "https://files.alnair.ae/uploads/gallery_photo/2025/5/fc/aa/fcaa873a918c4b965ed4929afcc4cd28.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/fc/aa/fcaa873a918c4b965ed4929afcc4cd28.jpg", 856474, {"_63": 1757, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 1619, "_1671": 1583, "_1672": 125, "_1673": 125, "_1674": 90, "_1675": 1758}, 19838, [1759, 1764, 1769, 1774, 1779, 1784, 1789, 1794, 1799, 1804], {"_63": 1760, "_64": 1761, "_61": 1762, "_1681": 1763, "_1683": -5}, 369132, "https://files.alnair.ae/uploads/gallery_photo/2025/5/ba/e6/bae6fe6c0ecf46bfcaf750177a1f9b98.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/ba/e6/bae6fe6c0ecf46bfcaf750177a1f9b98.jpg", 68459, {"_63": 1765, "_64": 1766, "_61": 1767, "_1681": 1768, "_1683": -5}, 369131, "https://files.alnair.ae/uploads/gallery_photo/2025/5/54/40/54405cf852e01c9990d6542d7c3e7945.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/54/40/54405cf852e01c9990d6542d7c3e7945.jpg", 82835, {"_63": 1770, "_64": 1771, "_61": 1772, "_1681": 1773, "_1683": -5}, 369130, "https://files.alnair.ae/uploads/gallery_photo/2025/5/bc/f6/bcf66776464aea6188d1a73b74f3b1a0.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/bc/f6/bcf66776464aea6188d1a73b74f3b1a0.jpg", 153061, {"_63": 1775, "_64": 1776, "_61": 1777, "_1681": 1778, "_1683": -5}, 369129, "https://files.alnair.ae/uploads/gallery_photo/2025/5/ba/22/ba22e112e1e8437092c0199851b47511.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/ba/22/ba22e112e1e8437092c0199851b47511.jpg", 125254, {"_63": 1780, "_64": 1781, "_61": 1782, "_1681": 1783, "_1683": -5}, 369128, "https://files.alnair.ae/uploads/gallery_photo/2025/5/f4/38/f4385c74212a95cfc11d97e1ce4b3977.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/f4/38/f4385c74212a95cfc11d97e1ce4b3977.jpg", 80869, {"_63": 1785, "_64": 1786, "_61": 1787, "_1681": 1788, "_1683": -5}, 369127, "https://files.alnair.ae/uploads/gallery_photo/2025/5/92/8e/928ea697dbba9c18d976a0604a0e429e.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/92/8e/928ea697dbba9c18d976a0604a0e429e.jpg", 58474, {"_63": 1790, "_64": 1791, "_61": 1792, "_1681": 1793, "_1683": -5}, 369126, "https://files.alnair.ae/uploads/gallery_photo/2025/5/05/54/05544b55f6aa636c5620291b3d683cd9.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/05/54/05544b55f6aa636c5620291b3d683cd9.jpg", 72822, {"_63": 1795, "_64": 1796, "_61": 1797, "_1681": 1798, "_1683": -5}, 369125, "https://files.alnair.ae/uploads/gallery_photo/2025/5/f2/f4/f2f4dbfb610fde922a6d65c878a8a68a.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/f2/f4/f2f4dbfb610fde922a6d65c878a8a68a.jpg", 131686, {"_63": 1800, "_64": 1801, "_61": 1802, "_1681": 1803, "_1683": -5}, 369124, "https://files.alnair.ae/uploads/gallery_photo/2025/5/af/55/af558688e758337920e737eba8ca192e.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/af/55/af558688e758337920e737eba8ca192e.jpg", 71384, {"_63": 1805, "_64": 1806, "_61": 1807, "_1681": 1808, "_1683": -5}, 369123, "https://files.alnair.ae/uploads/gallery_photo/2025/5/9f/c1/9fc19ee1c94668a818bbf8f7c84355b5.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/9f/c1/9fc19ee1c94668a818bbf8f7c84355b5.jpg", 128013, {"_63": 1810, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 1811, "_1671": 1812, "_1672": 130, "_1673": 130, "_1674": 90, "_1675": 1813}, 19839, "75.53", "2020-07-01 00:00:00", [1814, 1819, 1824, 1829, 1834, 1839, 1844, 1849, 1854, 1859, 1864], {"_63": 1815, "_64": 1816, "_61": 1817, "_1681": 1818, "_1683": -5}, 369143, "https://files.alnair.ae/uploads/gallery_photo/2025/5/7b/1e/7b1e0cb38a2d26fbce47c1228ce665e4.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/7b/1e/7b1e0cb38a2d26fbce47c1228ce665e4.jpg", 74809, {"_63": 1820, "_64": 1821, "_61": 1822, "_1681": 1823, "_1683": -5}, 369142, "https://files.alnair.ae/uploads/gallery_photo/2025/5/78/9a/789aa16dddb27d30d571bd1824f908f9.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/78/9a/789aa16dddb27d30d571bd1824f908f9.jpg", 109550, {"_63": 1825, "_64": 1826, "_61": 1827, "_1681": 1828, "_1683": -5}, 369141, "https://files.alnair.ae/uploads/gallery_photo/2025/5/53/8b/538b10a28eafda02977d54cc1bffee6b.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/53/8b/538b10a28eafda02977d54cc1bffee6b.jpg", 101509, {"_63": 1830, "_64": 1831, "_61": 1832, "_1681": 1833, "_1683": -5}, 369140, "https://files.alnair.ae/uploads/gallery_photo/2025/5/d4/b2/d4b2ed7cc87ea94ec50a2775e37f18f1.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/d4/b2/d4b2ed7cc87ea94ec50a2775e37f18f1.jpg", 85580, {"_63": 1835, "_64": 1836, "_61": 1837, "_1681": 1838, "_1683": -5}, 369139, "https://files.alnair.ae/uploads/gallery_photo/2025/5/c8/e7/c8e7fea389c4de9de38a4a20e9977c3b.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/c8/e7/c8e7fea389c4de9de38a4a20e9977c3b.jpg", 86356, {"_63": 1840, "_64": 1841, "_61": 1842, "_1681": 1843, "_1683": -5}, 369138, "https://files.alnair.ae/uploads/gallery_photo/2025/5/5a/d9/5ad92efae670bc6be1b1764648fb58b4.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/5a/d9/5ad92efae670bc6be1b1764648fb58b4.jpg", 116810, {"_63": 1845, "_64": 1846, "_61": 1847, "_1681": 1848, "_1683": -5}, 369137, "https://files.alnair.ae/uploads/gallery_photo/2025/5/9a/3e/9a3eebfdcb51d3ac358be69e7fd1a369.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/9a/3e/9a3eebfdcb51d3ac358be69e7fd1a369.jpg", 100848, {"_63": 1850, "_64": 1851, "_61": 1852, "_1681": 1853, "_1683": -5}, 369136, "https://files.alnair.ae/uploads/gallery_photo/2025/5/11/a7/11a78fcf515f517f94f5844187dbed2b.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/11/a7/11a78fcf515f517f94f5844187dbed2b.jpg", 138140, {"_63": 1855, "_64": 1856, "_61": 1857, "_1681": 1858, "_1683": -5}, 369135, "https://files.alnair.ae/uploads/gallery_photo/2025/5/81/e4/81e4ad3d73166445ab8d3b8b026ce6bf.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/81/e4/81e4ad3d73166445ab8d3b8b026ce6bf.jpg", 117423, {"_63": 1860, "_64": 1861, "_61": 1862, "_1681": 1863, "_1683": -5}, 369134, "https://files.alnair.ae/uploads/gallery_photo/2025/5/b1/15/b115a1f91269d6808fcea49be11c3af1.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/b1/15/b115a1f91269d6808fcea49be11c3af1.jpg", 71418, {"_63": 1865, "_64": 1866, "_61": 1867, "_1681": 1868, "_1683": -5}, 369133, "https://files.alnair.ae/uploads/gallery_photo/2025/5/92/ad/92adba1d3e4e69606d819f926c8490c1.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/92/ad/92adba1d3e4e69606d819f926c8490c1.jpg", 171655, {"_63": 1870, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 1871, "_1671": 1872, "_1672": 125, "_1673": 125, "_1674": 90, "_1675": 1873}, 19840, "54.06", "2020-02-20 00:00:00", [1874, 1879, 1884, 1889, 1894, 1899, 1904, 1909, 1914, 1919], {"_63": 1875, "_64": 1876, "_61": 1877, "_1681": 1878, "_1683": -5}, 369153, "https://files.alnair.ae/uploads/gallery_photo/2025/5/41/f8/41f815fdd558a3f2632700949941f0e9.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/41/f8/41f815fdd558a3f2632700949941f0e9.jpg", 591603, {"_63": 1880, "_64": 1881, "_61": 1882, "_1681": 1883, "_1683": -5}, 369152, "https://files.alnair.ae/uploads/gallery_photo/2025/5/89/9d/899df2f05f069d8a98af801cd9344266.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/89/9d/899df2f05f069d8a98af801cd9344266.jpg", 584030, {"_63": 1885, "_64": 1886, "_61": 1887, "_1681": 1888, "_1683": -5}, 369151, "https://files.alnair.ae/uploads/gallery_photo/2025/5/91/d5/91d57082f8ee1e3ebd33fc28d465f0b7.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/91/d5/91d57082f8ee1e3ebd33fc28d465f0b7.jpg", 446419, {"_63": 1890, "_64": 1891, "_61": 1892, "_1681": 1893, "_1683": -5}, 369150, "https://files.alnair.ae/uploads/gallery_photo/2025/5/04/77/047772b137a61ce15c8512f80b35a891.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/04/77/047772b137a61ce15c8512f80b35a891.jpg", 855460, {"_63": 1895, "_64": 1896, "_61": 1897, "_1681": 1898, "_1683": -5}, 369149, "https://files.alnair.ae/uploads/gallery_photo/2025/5/28/d5/28d58f8b6542f7450b8a1928efe1eaf1.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/28/d5/28d58f8b6542f7450b8a1928efe1eaf1.jpg", 393870, {"_63": 1900, "_64": 1901, "_61": 1902, "_1681": 1903, "_1683": -5}, 369148, "https://files.alnair.ae/uploads/gallery_photo/2025/5/cf/2a/cf2a34fb716f1371318b5865899352b4.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/cf/2a/cf2a34fb716f1371318b5865899352b4.jpg", 605703, {"_63": 1905, "_64": 1906, "_61": 1907, "_1681": 1908, "_1683": -5}, 369147, "https://files.alnair.ae/uploads/gallery_photo/2025/5/33/f3/33f38acf67382c263b7aa9e9e57a5a6c.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/33/f3/33f38acf67382c263b7aa9e9e57a5a6c.jpg", 420873, {"_63": 1910, "_64": 1911, "_61": 1912, "_1681": 1913, "_1683": -5}, 369146, "https://files.alnair.ae/uploads/gallery_photo/2025/5/12/eb/12ebdeaa98f8fbe6f1f94cd6abd0b566.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/12/eb/12ebdeaa98f8fbe6f1f94cd6abd0b566.jpg", 380401, {"_63": 1915, "_64": 1916, "_61": 1917, "_1681": 1918, "_1683": -5}, 369145, "https://files.alnair.ae/uploads/gallery_photo/2025/5/21/ea/21eaaf421a6baca354b991b97a932e7c.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/21/ea/21eaaf421a6baca354b991b97a932e7c.jpg", 597574, {"_63": 1920, "_64": 1921, "_61": 1922, "_1681": 1923, "_1683": -5}, 369144, "https://files.alnair.ae/uploads/gallery_photo/2025/5/63/51/6351bd3b08395c1a170fbbc1a6771c44.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/63/51/6351bd3b08395c1a170fbbc1a6771c44.jpg", 533110, {"_63": 1925, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 1926, "_1671": 1927, "_1672": 125, "_1673": 125, "_1674": 90, "_1675": 1928}, 19841, "33.06", "2019-10-14 00:00:00", [1929, 1934, 1939, 1944, 1949, 1954, 1959, 1964, 1969, 1974], {"_63": 1930, "_64": 1931, "_61": 1932, "_1681": 1933, "_1683": -5}, 369163, "https://files.alnair.ae/uploads/gallery_photo/2025/5/8c/58/8c587093f3129a4f92c04e4f34437779.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/8c/58/8c587093f3129a4f92c04e4f34437779.jpg", 619749, {"_63": 1935, "_64": 1936, "_61": 1937, "_1681": 1938, "_1683": -5}, 369162, "https://files.alnair.ae/uploads/gallery_photo/2025/5/a7/3e/a73e257eae9e90c5343f72fcb1887c7b.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/a7/3e/a73e257eae9e90c5343f72fcb1887c7b.jpg", 711695, {"_63": 1940, "_64": 1941, "_61": 1942, "_1681": 1943, "_1683": -5}, 369161, "https://files.alnair.ae/uploads/gallery_photo/2025/5/40/cd/40cd0ff70033cd792c0e003b3c597a34.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/40/cd/40cd0ff70033cd792c0e003b3c597a34.jpg", 581539, {"_63": 1945, "_64": 1946, "_61": 1947, "_1681": 1948, "_1683": -5}, 369160, "https://files.alnair.ae/uploads/gallery_photo/2025/5/99/f3/99f307e0ba561b3d111b85dc6ea5be26.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/99/f3/99f307e0ba561b3d111b85dc6ea5be26.jpg", 684851, {"_63": 1950, "_64": 1951, "_61": 1952, "_1681": 1953, "_1683": -5}, 369159, "https://files.alnair.ae/uploads/gallery_photo/2025/5/3e/a9/3ea9d8176d591d506220a8b5afc38ed5.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/3e/a9/3ea9d8176d591d506220a8b5afc38ed5.jpg", 427379, {"_63": 1955, "_64": 1956, "_61": 1957, "_1681": 1958, "_1683": -5}, 369158, "https://files.alnair.ae/uploads/gallery_photo/2025/5/c3/ea/c3ea0c3e6e5e28ce85ae5cc9d3fe6f57.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/c3/ea/c3ea0c3e6e5e28ce85ae5cc9d3fe6f57.jpg", 765033, {"_63": 1960, "_64": 1961, "_61": 1962, "_1681": 1963, "_1683": -5}, 369157, "https://files.alnair.ae/uploads/gallery_photo/2025/5/bb/c5/bbc5eb6101e5034b5724fb92c8de0dd9.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/bb/c5/bbc5eb6101e5034b5724fb92c8de0dd9.jpg", 632346, {"_63": 1965, "_64": 1966, "_61": 1967, "_1681": 1968, "_1683": -5}, 369156, "https://files.alnair.ae/uploads/gallery_photo/2025/5/c2/8e/c28e3838cb66771f58e28965b5fdb558.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/c2/8e/c28e3838cb66771f58e28965b5fdb558.jpg", 479553, {"_63": 1970, "_64": 1971, "_61": 1972, "_1681": 1973, "_1683": -5}, 369155, "https://files.alnair.ae/uploads/gallery_photo/2025/5/dd/8a/dd8afb22de14deeb6f5765055059db5f.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/dd/8a/dd8afb22de14deeb6f5765055059db5f.jpg", 820078, {"_63": 1975, "_64": 1976, "_61": 1977, "_1681": 1978, "_1683": -5}, 369154, "https://files.alnair.ae/uploads/gallery_photo/2025/5/bd/a9/bda9c1478e47bb6124e7e0033b37523f.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/bd/a9/bda9c1478e47bb6124e7e0033b37523f.jpg", 495369, {"_63": 1980, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 1981, "_1671": 1982, "_1672": 102, "_1673": 102, "_1674": 90, "_1675": 1983}, 19842, "20.71", "2019-05-28 00:00:00", [1984, 1989, 1994, 1999, 2004, 2009], {"_63": 1985, "_64": 1986, "_61": 1987, "_1681": 1988, "_1683": -5}, 369169, "https://files.alnair.ae/uploads/gallery_photo/2025/5/1e/26/1e26718b5133bd4df5232395b479f22b.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/1e/26/1e26718b5133bd4df5232395b479f22b.jpg", 667259, {"_63": 1990, "_64": 1991, "_61": 1992, "_1681": 1993, "_1683": -5}, 369168, "https://files.alnair.ae/uploads/gallery_photo/2025/5/64/49/644935e55b51b0583eff90b0e12daa77.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/64/49/644935e55b51b0583eff90b0e12daa77.jpg", 699932, {"_63": 1995, "_64": 1996, "_61": 1997, "_1681": 1998, "_1683": -5}, 369167, "https://files.alnair.ae/uploads/gallery_photo/2025/5/8e/a8/8ea80983fe8865cb4390dcf01130b145.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/8e/a8/8ea80983fe8865cb4390dcf01130b145.jpg", 600618, {"_63": 2000, "_64": 2001, "_61": 2002, "_1681": 2003, "_1683": -5}, 369166, "https://files.alnair.ae/uploads/gallery_photo/2025/5/34/d5/34d5078b2248afc3a6c99410ee8ab73e.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/34/d5/34d5078b2248afc3a6c99410ee8ab73e.jpg", 478782, {"_63": 2005, "_64": 2006, "_61": 2007, "_1681": 2008, "_1683": -5}, 369165, "https://files.alnair.ae/uploads/gallery_photo/2025/5/b7/08/b7088a86fac8f477c4df3f3a06628e28.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/b7/08/b7088a86fac8f477c4df3f3a06628e28.jpg", 613525, {"_63": 2010, "_64": 2011, "_61": 2012, "_1681": 2013, "_1683": -5}, 369164, "https://files.alnair.ae/uploads/gallery_photo/2025/5/98/fd/98fdc08be96f367968ff035cd4a5b472.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/98/fd/98fdc08be96f367968ff035cd4a5b472.jpg", 425825, {"_63": 2015, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 2016, "_1671": 2017, "_1672": 125, "_1673": 125, "_1674": 90, "_1675": 2018}, 19843, "15.49", "2019-02-24 00:00:00", [2019, 2024, 2029, 2034, 2039, 2044, 2049, 2054, 2059, 2064], {"_63": 2020, "_64": 2021, "_61": 2022, "_1681": 2023, "_1683": -5}, 369179, "https://files.alnair.ae/uploads/gallery_photo/2025/5/7c/fd/7cfdd7e8e2e3fb3bd6d04aaad06d1281.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/7c/fd/7cfdd7e8e2e3fb3bd6d04aaad06d1281.jpg", 981997, {"_63": 2025, "_64": 2026, "_61": 2027, "_1681": 2028, "_1683": -5}, 369178, "https://files.alnair.ae/uploads/gallery_photo/2025/5/ee/2d/ee2d2071e76ff88654cd3071bdc7791b.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/ee/2d/ee2d2071e76ff88654cd3071bdc7791b.jpg", 925996, {"_63": 2030, "_64": 2031, "_61": 2032, "_1681": 2033, "_1683": -5}, 369177, "https://files.alnair.ae/uploads/gallery_photo/2025/5/73/8e/738e1bd1f56cb89f28cf64403a11137f.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/73/8e/738e1bd1f56cb89f28cf64403a11137f.jpg", 768521, {"_63": 2035, "_64": 2036, "_61": 2037, "_1681": 2038, "_1683": -5}, 369176, "https://files.alnair.ae/uploads/gallery_photo/2025/5/91/16/9116e4ba2965cecccb869e8e897f5383.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/91/16/9116e4ba2965cecccb869e8e897f5383.jpg", 639953, {"_63": 2040, "_64": 2041, "_61": 2042, "_1681": 2043, "_1683": -5}, 369175, "https://files.alnair.ae/uploads/gallery_photo/2025/5/63/a3/63a3a5ffac18da2b817a62c9409d0470.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/63/a3/63a3a5ffac18da2b817a62c9409d0470.jpg", 648800, {"_63": 2045, "_64": 2046, "_61": 2047, "_1681": 2048, "_1683": -5}, 369174, "https://files.alnair.ae/uploads/gallery_photo/2025/5/4a/ef/4aefb5db3178b28522ced1e753e336c2.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/4a/ef/4aefb5db3178b28522ced1e753e336c2.jpg", 893215, {"_63": 2050, "_64": 2051, "_61": 2052, "_1681": 2053, "_1683": -5}, 369173, "https://files.alnair.ae/uploads/gallery_photo/2025/5/a5/2b/a52bffccbc3e239697d42c9731693e02.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/a5/2b/a52bffccbc3e239697d42c9731693e02.jpg", 923396, {"_63": 2055, "_64": 2056, "_61": 2057, "_1681": 2058, "_1683": -5}, 369172, "https://files.alnair.ae/uploads/gallery_photo/2025/5/9f/76/9f760aa194fd706213fbc0495e885632.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/9f/76/9f760aa194fd706213fbc0495e885632.jpg", 823318, {"_63": 2060, "_64": 2061, "_61": 2062, "_1681": 2063, "_1683": -5}, 369171, "https://files.alnair.ae/uploads/gallery_photo/2025/5/cc/e7/cce75b9c8dda9431bf21406b43b7b129.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/cc/e7/cce75b9c8dda9431bf21406b43b7b129.jpg", 719909, {"_63": 2065, "_64": 2066, "_61": 2067, "_1681": 2068, "_1683": -5}, 369170, "https://files.alnair.ae/uploads/gallery_photo/2025/5/2a/76/2a76444374d40096d456ec78b62a7332.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/2a/76/2a76444374d40096d456ec78b62a7332.jpg", 817038, {"_63": 2070, "_1574": 855, "_73": 854, "_1576": -5, "_1670": 2071, "_1671": 2072, "_1672": 97, "_1673": 97, "_1674": 90, "_1675": 2073}, 19844, "3.00", "2018-09-23 00:00:00", [2074, 2079, 2084, 2089, 2094], {"_63": 2075, "_64": 2076, "_61": 2077, "_1681": 2078, "_1683": -5}, 369184, "https://files.alnair.ae/uploads/gallery_photo/2025/5/bc/87/bc87135e96565686b59820dd0c8b6d35.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/bc/87/bc87135e96565686b59820dd0c8b6d35.jpg", 693710, {"_63": 2080, "_64": 2081, "_61": 2082, "_1681": 2083, "_1683": -5}, 369183, "https://files.alnair.ae/uploads/gallery_photo/2025/5/08/5a/085a388373700f43b2f1fb80404a32bd.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/08/5a/085a388373700f43b2f1fb80404a32bd.jpg", 697534, {"_63": 2085, "_64": 2086, "_61": 2087, "_1681": 2088, "_1683": -5}, 369182, "https://files.alnair.ae/uploads/gallery_photo/2025/5/11/26/1126fae90bd922f4592edfc0a549ab29.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/11/26/1126fae90bd922f4592edfc0a549ab29.jpg", 667090, {"_63": 2090, "_64": 2091, "_61": 2092, "_1681": 2093, "_1683": -5}, 369181, "https://files.alnair.ae/uploads/gallery_photo/2025/5/97/a5/97a51eb98337adeaf1e25d53a39a4136.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/97/a5/97a51eb98337adeaf1e25d53a39a4136.jpg", 885549, {"_63": 2095, "_64": 2096, "_61": 2097, "_1681": 2098, "_1683": -5}, 369180, "https://files.alnair.ae/uploads/gallery_photo/2025/5/62/17/621704ab64958c70ec74a5ffccbf6c41.jpg", "https://files.alnair.ae/uploads/gallery_logo/2025/5/62/17/621704ab64958c70ec74a5ffccbf6c41.jpg", 780321, "brochures", [2101], {"_63": 2102, "_1681": 2103, "_64": 2104, "_59": 2105}, 368158, 10742762, "https://files.alnair.ae/uploads/2025/5/e6/39/e63933ece4d22883186266ca796bbcca.pdf", "Naseem Townhouses Brochure", {"_896": 2107, "_1181": 2108, "_1205": 2109}, [920], [1191], [97], "eoi", {"_2112": 56, "_2113": 2114}, "is_eoi_return", "eoi_items", [], "payment_plans", [2117, 2199], {"_63": 2118, "_2119": 2120, "_59": 2121, "_1576": -5, "_2122": 2123, "_2124": -5, "_2125": 2071, "_2126": 2127, "_1648": 2128, "_2129": 2130, "_1586": 2131, "_2132": -5, "_1637": 2133, "_77": 2134, "_79": 2135, "_2136": 145, "_2137": 145, "_2138": 56, "_2139": 2140, "_61": 2153, "_2157": 2158, "_2182": 2183, "_1566": -5}, 634, "agency_id", 8810, "Get up to 88% of developer commission", "description_short", "Book unit and receive the majority of the commission\\r\\nfor yourself! Service provided by For You", "advance_payment", "commission_percent", "user_commission_percent", "2.64", 971505641863, "email", "<EMAIL>", "https://foryou-realestate.com/", "terms_link", "Greens, Onyx 2, office 910, Dubai", 25.096278986525, 55.168852742076, "is_available_booking", "is_priority", "is_open", "opening_hours", [2141, 2147, 2148, 2149, 2150, 2151, 2152], {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 52}, "begin_time", 32400, "finish_time", 75600, "day_of_week", {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 76}, {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 92}, {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 120}, {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 97}, {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 102}, {"_2138": 145, "_2142": 2143, "_2144": 2145, "_2146": 108}, {"_63": 2154, "_64": 2155, "_61": 2156}, 319257, "https://files.alnair.ae/uploads/2025/3/8d/5b/8d5bedec4441bc4c6a75b3e69f01f553.jpg", "https://files.alnair.ae/uploads/logo/2025/3/8d/5b/8d5bedec4441bc4c6a75b3e69f01f553.jpg", "contacts", [2159], {"_63": 2160, "_2161": 2162, "_1576": -5, "_2163": -5, "_61": 2164, "_2168": 2169, "_2170": 2171, "_2173": 2174, "_2175": -5, "_2176": -5, "_2177": -5, "_352": 2178, "_2180": 2181}, 10488, "full_name", "<PERSON>", "grade", {"_63": 2165, "_64": 2166, "_61": 2167}, 328650, "https://files.alnair.ae/uploads/2025/3/8a/26/8a26aad2f83838ff27ae2efd5b8b7479.jpg", "https://files.alnair.ae/uploads/user_logo/2025/3/8a/26/8a26aad2f83838ff27ae2efd5b8b7479.jpg", "emails", [], "phones", [2172], "971502250706", "whatsapp", "+971-502-250-706", "telegram", "linkedin", "instagram", {"_396": 2179}, [586, 501], "office_ids", [2118], "agency", {"_63": 2120, "_1553": 147, "_59": 2184, "_1576": 2185, "_61": 2186, "_2190": -5, "_1586": 2131, "_1637": 2133, "_77": 2134, "_79": 2135, "_1648": 2128, "_2129": 2191, "_2173": -5, "_2176": -5, "_2177": -5, "_2192": -5, "_2193": 2194, "_2195": 2196, "_2197": 145, "_2198": 145}, "For You Real Estate", "For You - агенство полного цикла, при продаже недвижимости мы сопровождаем клиента на каждом этапе от покупки до продажи, размещения маркетинга и аренды. Помимо недвижимости мы предоставляем финансовые и юридические услуги.", {"_63": 2187, "_64": 2188, "_61": 2189}, 261428, "https://files.alnair.ae/uploads/2024/11/2b/f0/2bf0b5dad01e520e2496265d694c7e74.png", "https://files.alnair.ae/uploads/logo/2024/11/2b/f0/2bf0b5dad01e520e2496265d694c7e74.png", "domain", "<EMAIL>", "registration_number", "license_number", "********", "payment_type", "agency_bank", "is_resale", "is_visible_booking", {"_63": 234, "_2119": -5, "_59": 2200, "_1576": -5, "_2122": -5, "_2124": -5, "_2125": -5, "_2126": -5, "_1648": 2201, "_2129": 2202, "_1586": 1647, "_2132": -5, "_1637": 2203, "_77": 2204, "_79": 2205, "_2136": 56, "_2137": 56, "_2138": 56, "_2139": 2206, "_61": -5, "_2157": 2216, "_2182": -5, "_1566": 2233}, "Sales Centre", ************, "<EMAIL>", "Town Square, Dubai", 25.0044075, 55.292505, [2207, 2210, 2211, 2212, 2213, 2214, 2215], {"_2138": 145, "_2142": 2208, "_2144": 2209, "_2146": 52}, 36000, 68400, {"_2138": 145, "_2142": 2208, "_2144": 2209, "_2146": 76}, {"_2138": 145, "_2142": 2208, "_2144": 2209, "_2146": 92}, {"_2138": 145, "_2142": 2208, "_2144": 2209, "_2146": 120}, {"_2138": 145, "_2142": 2208, "_2144": 2209, "_2146": 97}, {"_2138": 145, "_2142": 2208, "_2144": 2209, "_2146": 102}, {"_2138": 145, "_2142": -5, "_2144": -5, "_2146": 108}, [2217], {"_63": 2218, "_2161": 2219, "_1576": 2220, "_2163": 2221, "_61": 2222, "_2168": 2226, "_2170": 2227, "_2173": 2229, "_2175": -5, "_2176": -5, "_2177": -5, "_352": 2230, "_2180": 2232}, 3367, "<PERSON><PERSON><PERSON>", "All Nshama projects.", "Sales manager", {"_63": 2223, "_64": 2224, "_61": 2225}, 326607, "https://files.alnair.ae/uploads/2025/3/7d/00/7d00ada455ded7d1321cd2d52fb4d0d8.png", "https://files.alnair.ae/uploads/user_logo/2025/3/7d/00/7d00ada455ded7d1321cd2d52fb4d0d8.png", [2202], [2228], "************", "+************", {"_396": 2231}, [586, 501], [234], {"_63": 1640, "_1637": -5, "_59": 1641, "_1576": 1642, "_61": 2234, "_1586": 1647, "_1648": 1649}, {"_63": 1644, "_64": 1645, "_61": 1646}, {"_34": 2236, "_2237": 2238, "_1672": 2239, "_2251": 2252}, {"_883": 108}, "rents", {"_883": 125}, {"_1321": 52, "_2240": 2241, "_2242": 2241, "_2243": 90, "_2244": 90, "_2245": 2246, "_2247": 52, "_2248": 2249, "_2250": 90}, "price_from", 3850000, "price_to", "price_m2_from", "price_m2_to", "units_count", 324, "units_resale_count", "units_area_mt", "227.61", "units_max_floor", "villas", {"_2253": 2254}, "445", {"_1321": 52, "_2240": 2241, "_2242": 2241, "_2243": -5, "_2244": -5, "_2255": 2256, "_2257": 2256}, "area_from", 227.61, "area_to", 25.015043348549, 55.283424053549, "constructions", [], "news", [], "actionData", "errors"]