{"loaderData": {"routes/layouts/app_layout": {"info": {"cities": [{"id": 1, "name": "Dubai", "country_id": 2, "latitude": "25.15433020", "longitude": "55.26127900", "currency": "AED", "is_private": false, "is_permit_number": 1}, {"id": 2, "name": "Abu Dhabi", "country_id": 2, "latitude": "24.39871220", "longitude": "54.47347640", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 3, "name": "Sharjah", "country_id": 2, "latitude": "25.34447620", "longitude": "55.40548330", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 5, "name": "<PERSON><PERSON>", "country_id": 2, "latitude": "25.77862550", "longitude": "55.95320280", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 6, "name": "Phuke<PERSON>", "country_id": 3, "latitude": "7.87897800", "longitude": "98.39839200", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 7, "name": "<PERSON><PERSON><PERSON>", "country_id": 2, "latitude": "25.40327750", "longitude": "55.47766910", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 8, "name": "<PERSON><PERSON>", "country_id": 3, "latitude": "9.49987710", "longitude": "99.93911410", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 9, "name": "Muscat", "country_id": 4, "latitude": "23.61018270", "longitude": "58.59334350", "currency": "OMR", "is_private": false, "is_permit_number": 0}, {"id": 10, "name": "<PERSON><PERSON><PERSON>", "country_id": 4, "latitude": "17.01570000", "longitude": "54.09240000", "currency": "OMR", "is_private": false, "is_permit_number": 0}, {"id": 11, "name": "<PERSON><PERSON><PERSON>", "country_id": 4, "latitude": "19.57340000", "longitude": "57.63320000", "currency": "OMR", "is_private": false, "is_permit_number": 0}, {"id": 12, "name": "Bangkok", "country_id": 3, "latitude": "13.75630000", "longitude": "100.50180000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 13, "name": "London (Intermark)", "country_id": 5, "latitude": "51.49582475", "longitude": "-0.09205173", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 14, "name": "<PERSON><PERSON>", "country_id": 2, "latitude": "25.54917200", "longitude": "55.54931700", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 15, "name": "<PERSON><PERSON><PERSON>", "country_id": 3, "latitude": "12.92360000", "longitude": "100.88280000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 16, "name": "Bali", "country_id": 6, "latitude": "-8.34050000", "longitude": "115.09200000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 17, "name": "<PERSON>", "country_id": 3, "latitude": "18.70610000", "longitude": "98.98170000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 18, "name": "<PERSON><PERSON>", "country_id": 3, "latitude": "12.56830000", "longitude": "99.96280000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 19, "name": "Qatar", "country_id": 7, "latitude": "25.27698700", "longitude": "51.52639800", "currency": "AED", "is_private": true, "is_permit_number": 0}, {"id": 21, "name": "Limassol", "country_id": 8, "latitude": "34.70710000", "longitude": "33.02260000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 22, "name": "<PERSON><PERSON><PERSON>", "country_id": 8, "latitude": "34.77560000", "longitude": "32.42450000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 23, "name": "Larnaca", "country_id": 8, "latitude": "34.91993750", "longitude": "33.61554530", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 24, "name": "Nicosia", "country_id": 8, "latitude": "35.18560000", "longitude": "33.38230000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 25, "name": "Famagusta", "country_id": 8, "latitude": "35.11750000", "longitude": "33.94000000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 26, "name": "Manchester", "country_id": 5, "latitude": "53.47876500", "longitude": "-2.24945700", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 27, "name": "Birmingham", "country_id": 5, "latitude": "52.47930400", "longitude": "-1.89882750", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 28, "name": "Liverpool", "country_id": 5, "latitude": "53.40631400", "longitude": "-2.98152200", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 29, "name": "Athens", "country_id": 9, "latitude": "37.98380000", "longitude": "23.72750000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 30, "name": "Chalkidiki", "country_id": 9, "latitude": "40.24440000", "longitude": "23.49970000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 31, "name": "Thessaloniki", "country_id": 9, "latitude": "40.64010000", "longitude": "22.94440000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 32, "name": "Barcelona", "country_id": 10, "latitude": "41.38510000", "longitude": "2.17340000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 33, "name": "Madrid", "country_id": 10, "latitude": "40.41680000", "longitude": "-3.70380000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 34, "name": "Malaga", "country_id": 10, "latitude": "36.72130000", "longitude": "-4.42140000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 35, "name": "Benidorm", "country_id": 10, "latitude": "38.54110000", "longitude": "-0.12250000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 36, "name": "Costa Del Sol", "country_id": 10, "latitude": "36.52980000", "longitude": "-4.88300000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 37, "name": "Costa Blanca", "country_id": 10, "latitude": "38.34520000", "longitude": "-0.48150000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 38, "name": "<PERSON>", "country_id": 10, "latitude": "41.55060000", "longitude": "2.42690000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 39, "name": "<PERSON>", "country_id": 10, "latitude": "41.20190000", "longitude": "1.52570000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 40, "name": "Costa Bravo", "country_id": 10, "latitude": "41.97940000", "longitude": "3.16610000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 41, "name": "Gili Islands", "country_id": 6, "latitude": "-8.34050000", "longitude": "116.02750000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 42, "name": "Lombok", "country_id": 6, "latitude": "-8.65000000", "longitude": "116.32000000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 43, "name": "Sumbawa", "country_id": 6, "latitude": "-8.65290000", "longitude": "117.36160000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 44, "name": "Glyfada", "country_id": 9, "latitude": "37.87443866", "longitude": "23.75560853", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 45, "name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 9, "latitude": "37.38570216", "longitude": "23.24543336", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 46, "name": "Istanbul", "country_id": 157, "latitude": "41.13530395", "longitude": "28.98328603", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 47, "name": "<PERSON><PERSON>", "country_id": 157, "latitude": "36.54545085", "longitude": "31.99651492", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 48, "name": "<PERSON><PERSON><PERSON>", "country_id": 157, "latitude": "36.91125691", "longitude": "30.69741097", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 49, "name": "Mersin", "country_id": 157, "latitude": "36.81173288", "longitude": "34.64391483", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 50, "name": "<PERSON><PERSON>", "country_id": 9, "latitude": "38.24563686", "longitude": "21.73702184", "currency": "USD", "is_private": true, "is_permit_number": 0}, {"id": 51, "name": "Crete", "country_id": 9, "latitude": "35.24753144", "longitude": "24.79500397", "currency": "USD", "is_private": false, "is_permit_number": 0}], "countries": [{"id": 2, "title": "United Arab Emirates", "currency": "AED"}, {"id": 3, "title": "Thailand", "currency": "THB"}, {"id": 4, "title": "Oman", "currency": "OMR"}, {"id": 5, "title": "United Kingdom", "currency": "GBP"}, {"id": 6, "title": "Indonesia", "currency": "USD"}, {"id": 7, "title": "Qatar", "currency": "AED"}, {"id": 8, "title": "Cyprus South", "currency": "EUR"}, {"id": 9, "title": "Greece", "currency": "EUR"}, {"id": 10, "title": "Spain", "currency": "EUR"}], "catalogs": {"compound_villa_plot": {"id": 240, "options": [{"id": 241, "value": "Plot with a house", "key": "compound_villa_plot_with_house", "data": []}, {"id": 242, "value": "Land without a house", "key": "compound_villa_plot_without_house", "data": []}, {"id": 243, "value": "Individual project", "key": "compound_villa_plot_individual_project", "data": []}]}, "payment_plan_when": {"id": 349, "options": [{"id": 350, "value": "At booking", "key": "payment_plan_when_booking", "data": []}, {"id": 351, "value": "Exact Date", "key": "payment_plan_when_exact_date", "data": []}, {"id": 352, "value": "Period after booking", "key": "payment_plan_when_days_after_booking", "data": []}, {"id": 353, "value": "Completion rate", "key": "payment_plan_when_completion_rate", "data": []}, {"id": 354, "value": "Handover", "key": "payment_plan_when_handover", "data": []}, {"id": 355, "value": "Days after handover", "key": "payment_plan_when_days_after_handover", "data": []}, {"id": 382, "value": "Tax Fee", "key": "payment_plan_when_tax_fee", "data": []}, {"id": 381, "value": "Down payment", "key": "payment_plan_when_down_payment", "data": []}, {"id": 383, "value": "Extra Fee", "key": "payment_plan_when_extra_fee", "data": []}, {"id": 465, "value": "Period after down payment", "key": "payment_plan_when_days_after_down_payment", "data": []}]}, "unit_media_type": {"id": 363, "options": [{"id": 364, "value": "Photo", "key": "unit_media_type_photo", "data": []}, {"id": 365, "value": "Plan", "key": "unit_media_type_plan", "data": []}]}, "unit_furnishing": {"id": 330, "options": [{"id": 331, "value": "Furnished", "key": "unit_furnishing_furnished", "data": []}, {"id": 332, "value": "Unfurnished", "key": "unit_furnishing_unfurnished", "data": []}, {"id": 333, "value": "Partially furnished", "key": "unit_furnishings_partly_furnished", "data": []}]}, "project_gallery_category": {"id": 92, "options": [{"id": 107, "value": "Project presentation", "key": "project_gallery_category_presentation", "data": []}, {"id": 93, "value": "Construction progress", "key": "project_gallery_category_construction_progress", "data": []}, {"id": 94, "value": "Finishing examples", "key": "project_gallery_category_finishing_examples", "data": []}, {"id": 95, "value": "Infrastructure", "key": "project_gallery_category_infrastructure", "data": []}, {"id": 96, "value": "View", "key": "project_gallery_category_view", "data": []}]}, "project_facilities": {"id": 66, "options": [{"id": 457, "value": "Branded", "key": "project_facilities_branded", "data": {"icon": "branded"}}, {"id": 153, "value": "Concierge Service", "key": "project_facilities_concierge", "data": {"icon": "concierge_service"}}, {"id": 286, "value": "<PERSON><PERSON>owed", "key": "project_facilities_pets", "data": {"icon": "pets"}}, {"id": 281, "value": "Kids Play Area", "key": "project_facilities_kids", "data": {"icon": "kids_play_area"}}, {"id": 156, "value": "Electric Car Charger", "key": "project_facilities_car_charger", "data": {"icon": "electric_car_charger"}}, {"id": 446, "value": "Free A/C", "key": "project_facilities_free_ac", "data": {"icon": "central_ac"}}, {"id": 447, "value": "Private beach", "key": "project_facilities_private_beach", "data": {"icon": "private_beach"}}, {"id": 448, "value": "Short-term rental forbidden", "key": "project_facilities_short_rental_forbidden", "data": {"icon": "short_rental_forbidden"}}, {"id": 463, "value": "Private pool", "key": "project_facilities_private_pool", "data": {"icon": "private_pool"}}, {"id": 464, "value": "Maid/Study room", "key": "project_facilities_maid_room", "data": {"icon": "maid_room"}}]}, "project_media_type": {"id": 358, "options": [{"id": 359, "value": "Presentation", "key": "project_media_type_presentation", "data": []}]}, "rooms": {"id": 109, "options": [{"id": 110, "value": "Studio", "key": "rooms_studio", "data": {"short_value": "St"}}, {"id": 111, "value": "1 BR", "key": "rooms_1", "data": {"short_value": "1BR"}}, {"id": 112, "value": "2 BR", "key": "rooms_2", "data": {"short_value": "2BR"}}, {"id": 113, "value": "3 BR", "key": "rooms_3", "data": {"short_value": "3BR"}}, {"id": 114, "value": "4 BR", "key": "rooms_4", "data": {"short_value": "4BR"}}, {"id": 115, "value": "5 BR", "key": "rooms_5", "data": {"short_value": "5BR"}}, {"id": 341, "value": "6 BR", "key": "rooms_6", "data": {"short_value": "6BR"}}, {"id": 342, "value": "7 BR", "key": "rooms_7", "data": {"short_value": "7BR"}}, {"id": 343, "value": "8 BR", "key": "rooms_8", "data": {"short_value": "8BR"}}, {"id": 344, "value": "9 BR", "key": "rooms_9", "data": {"short_value": "9BR"}}, {"id": 345, "value": "10 BR", "key": "rooms_10", "data": {"short_value": "10BR"}}, {"id": 164, "value": "NA", "key": "rooms_n", "data": {"short_value": "Free"}}]}, "unit_type_room": {"id": 137, "options": [{"id": 138, "value": "Apartment", "key": "unit_type_room_unit", "data": []}, {"id": 462, "value": "Villa", "key": "unit_type_room_villa", "data": []}, {"id": 445, "value": "Townhouse", "key": "unit_type_room_townhouse", "data": []}, {"id": 140, "value": "Duplex", "key": "unit_type_room_duplex", "data": []}, {"id": 362, "value": "<PERSON>x", "key": "unit_type_room_triplex", "data": []}, {"id": 357, "value": "Penthouse", "key": "unit_type_room_penthouse", "data": []}, {"id": 141, "value": "Villa in project", "key": "unit_type_room_villa_in_project", "data": []}, {"id": 346, "value": "Retail", "key": "unit_type_room_retail", "data": []}, {"id": 347, "value": "Office", "key": "unit_type_room_office", "data": []}]}, "unit_status": {"id": 143, "options": [{"id": 146, "value": "Booked", "key": "unit_status_reservation", "data": {"bg_color": "#8e8e8d"}}, {"id": 145, "value": "Sold", "key": "unit_status_sold", "data": {"bg_color": "#DD6465"}}, {"id": 144, "value": "On sale", "key": "unit_status_sale", "data": {"bg_color": "#20CB6A"}}]}, "project_badges": {"id": 103, "options": [{"id": 444, "value": "Exclusive", "key": "badge_exclusive", "data": {"bg_color": "#dfe4ff", "icon": "<PERSON><PERSON><PERSON>", "color": "#4F5FD9"}}, {"id": 366, "value": "Recommended", "key": "badge_recommend", "data": {"bg_color": "#dfe4ff", "color": "#4F5FD9"}}, {"id": 385, "value": "Announcement", "key": "badge_announcement", "data": {"bg_color": "#fff1f2", "color": "#d74652"}}, {"id": 361, "value": "Presale (EOI)", "key": "badge_early_booking", "data": {"bg_color": "#E9FAF0", "color": "#20CB6A"}}, {"id": 163, "value": "Launch", "key": "badge_start_sale", "data": {"bg_color": "#fffff1", "remove_days": "14", "color": "#c88902"}}, {"id": 161, "value": "Sold Out", "key": "badge_sold_out", "data": {"bg_color": "#fff1f2", "color": "#ff0015"}}, {"id": 105, "value": "Free Installment", "key": "badge_free_installment", "data": {"bg_color": "#e7efff", "color": "#ff0015"}}, {"id": 384, "value": "Prelaunch", "key": "badge_prelaunch_sale", "data": {"bg_color": "#fff1f2", "color": "#ff0015"}}, {"id": 386, "value": "Price on request", "key": "badge_price_on_request", "data": {"bg_color": "#e6fafa", "color": "#14a1a2"}}]}, "development_stage": {"id": 116, "options": [{"id": 117, "value": "Scheduled", "key": "development_stage_scheduled", "data": []}, {"id": 120, "value": "Ready", "key": "development_stage_finished", "data": []}, {"id": 119, "value": "Stopped", "key": "development_stage_stopped", "data": []}, {"id": 348, "value": "In Progress", "key": "development_stage_progress", "data": []}]}, "project_sales_status": {"id": 4, "options": [{"id": 335, "value": "Announcement", "key": "project_sales_status_announcement", "data": {"color": "#d74652", "bg_color": "#fff1f2"}}, {"id": 338, "value": "Presale (EOI)", "key": "project_sales_status_eoi", "data": {"color": "#20CB6A", "bg_color": "#E9FAF0"}}, {"id": 360, "value": "Launch", "key": "project_sales_status_new_launch", "data": {"remove_days": "14", "next_value": "project_sales_status_on_sale", "color": "#c88902", "bg_color": "#fff6e9"}}, {"id": 5, "value": "On Sale", "key": "project_sales_status_on_sale", "data": {"color": "#20CB6A", "bg_color": "#E9FAF0"}}, {"id": 339, "value": "Sold Out", "key": "project_sales_status_sold_out", "data": {"color": "#12122D", "bg_color": "#DFDFEB"}}, {"id": 334, "value": "Pending", "key": "project_sales_status_pending", "data": {"color": "#ff0015", "bg_color": "#fff1f2"}}]}}}}}}